--
-- PostgreSQL database dump
--

-- Dumped from database version 16.9 (Ubuntu 16.9-0ubuntu0.24.04.1)
-- Dumped by pg_dump version 16.9 (Ubuntu 16.9-0ubuntu0.24.04.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: payment_methods; Type: TABLE; Schema: public; Owner: postgres
--


CREATE DATABASE hos_reporter WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE = 'en_US.UTF-8';


ALTER DATABASE hos_reporter OWNER TO postgres;

\connect hos_reporter

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: report_records; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.report_records (
    id bigint NOT NULL,
    record_type character varying(255) NOT NULL,
    business_id bigint NOT NULL,
    reporting_status character varying(50) DEFAULT 'pending'::character varying NOT NULL,
    last_reporting_attempt_at timestamp without time zone,
    reporting_retry_count integer DEFAULT 0 NOT NULL,
    error_message text,
    erp_response_code character varying(100),
    site_id character varying(100),
    priority integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    CONSTRAINT chk_reporting_status CHECK (((reporting_status)::text = ANY ((ARRAY['pending'::character varying, 'processing'::character varying, 'success'::character varying, 'failed'::character varying])::text[]))),
    CONSTRAINT report_record_reporting_retry_count_check CHECK ((reporting_retry_count >= 0))
);


ALTER TABLE public.report_records OWNER TO postgres;

--
-- Name: TABLE report_records; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.report_records IS '上报记录表，用于跟踪各种数据（交易、发货、库存）的上报状态';


--
-- Name: COLUMN report_records.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.id IS '主键ID';


--
-- Name: COLUMN report_records.record_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.record_type IS '记录类型: transaction, delivery, inventory';


--
-- Name: COLUMN report_records.business_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.business_id IS '业务数据ID，对应原始业务表的主键';


--
-- Name: COLUMN report_records.reporting_status; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.reporting_status IS '上报状态: pending-待处理, processing-处理中, success-成功, failed-失败';


--
-- Name: COLUMN report_records.last_reporting_attempt_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.last_reporting_attempt_at IS '上次尝试上报的时间';


--
-- Name: COLUMN report_records.reporting_retry_count; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.reporting_retry_count IS '上报重试次数';


--
-- Name: COLUMN report_records.error_message; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.error_message IS '最后一次失败的错误信息';


--
-- Name: COLUMN report_records.erp_response_code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.erp_response_code IS 'ERP返回的响应码';


--
-- Name: COLUMN report_records.site_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.site_id IS '站点ID';


--
-- Name: COLUMN report_records.priority; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.priority IS '优先级，数字越大优先级越高';


--
-- Name: COLUMN report_records.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.created_at IS '创建时间';


--
-- Name: COLUMN report_records.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.updated_at IS '更新时间';


--
-- Name: COLUMN report_records.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.report_records.deleted_at IS '记录删除时间（软删除）';


--
-- Name: report_record_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.report_record_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.report_record_id_seq OWNER TO postgres;

--
-- Name: report_record_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.report_record_id_seq OWNED BY public.report_records.id;


--
-- Name: report_records id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.report_records ALTER COLUMN id SET DEFAULT nextval('public.report_record_id_seq'::regclass);


--
-- Name: report_records report_record_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.report_records
    ADD CONSTRAINT report_record_pkey PRIMARY KEY (id);


--
-- PostgreSQL database dump complete
--

