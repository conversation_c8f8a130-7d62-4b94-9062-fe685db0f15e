# HOS Reporter 集成测试使用指南

## 概述

集成测试允许你指定 URL、订单 ID 或自定义内容，向 ERP 系统发送同步请求，验证整个数据上报链路。

## 测试文件位置

```
internal/task/reporter/integration_test.go
```

## 使用方法

### 1. 基础集成测试

测试从数据库查询真实订单数据上报：

```bash
INTEGRATION_TEST=1 go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration
```

### 2. 指定订单 ID 测试

测试特定订单 ID 的数据上报：

```bash
INTEGRATION_TEST=1 ORDER_ID=12345 go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration
```

### 3. 使用自定义数据测试

测试多笔交易的自定义数据：

```bash
INTEGRATION_TEST=1 CUSTOM_DATA=true go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration
```

### 4. 指定自定义 URL 测试

测试不同环境的 ERP API：

```bash
INTEGRATION_TEST=1 ERP_BASE_URL=http://custom.erp.api go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration
```

### 5. 批量测试

测试多个连续请求：

```bash
INTEGRATION_TEST=1 BATCH_TEST=1 go test -v ./internal/task/reporter/ -run TestSyncOrder_BatchIntegration
```

## 环境变量配置

| 环境变量 | 说明 | 默认值 | 示例 |
|---------|------|-------|------|
| `INTEGRATION_TEST` | 启用集成测试 | - | `1` |
| `ERP_BASE_URL` | ERP API 基础URL | `http://52.253.90.246/apr/api` | `http://custom.erp.api` |
| `ORDER_ID` | 指定订单ID | - | `12345` |
| `CUSTOM_DATA` | 使用自定义测试数据 | `false` | `true` |
| `BATCH_TEST` | 启用批量测试 | `false` | `1` |

## UAT 环境配置

测试使用的 UAT 环境信息：

- **URL**: `http://52.253.90.246/apr/api`
- **用户名**: `wecar`
- **密码**: `1!8$3#7*4@`
- **Key**: `123456789` (ERP API认证密钥)

## 测试数据类型

### 真实数据库数据（推荐）
- 从 order_service 数据库查询真实订单
- 包含关联的 fuel_transaction 数据
- 使用真实的交易号、金额、油量等信息
- 自动排除已上报的订单

### 指定订单ID数据
- 从数据库查询指定ID的订单
- 如果订单存在关联的fuel_transaction，使用真实数据
- 如果无关联数据，使用订单基本信息生成

### 自定义测试数据
- 2笔不同类型的交易
- 包含柴油和高级汽油
- 不同车型（卡车和轿车）
- 不同金额和时间

### 默认测试数据（后备方案）
- 单笔交易
- 汽油类型
- 5升，50,000卢比
- 固定的测试车辆和站点信息

## 响应码说明

| 响应码 | 含义 | 处理方式 |
|-------|------|---------|
| `200` | 成功 | ✅ 正常完成 |
| `210` | 重复数据 | ⚠️ 视为成功 |
| `1001` | Token过期 | ❌ 认证失败 |
| `401` | 认证失败 | ❌ 检查凭证 |
| 其他 | 业务错误 | ❓ 查看具体消息 |

## 测试输出示例

```
=== 开始集成测试：同步订单数据到ERP系统 ===
ERP API 基础URL: http://52.253.90.246/apr/api
用户: wecar
使用订单ID: 12345 的测试数据
=== 开始发送同步请求 ===
请求耗时: 1.234s
=== 响应结果 ===
响应码: 200
响应消息: Success
处理的交易数量: 1
创建的数据条数: 1
✅ 上报成功
=== 集成测试完成 ===
```

## 故障排除

### 常见问题

1. **认证失败**
   ```
   认证失败 - 可能需要更新凭证: 获取访问令牌失败
   ```
   - 检查用户名、密码是否正确
   - 确认Key值是否有效
   - 验证网络连接

2. **Redis连接失败**
   ```
   Redis连接失败，将跳过缓存: dial tcp :6379: connect: connection refused
   ```
   - 这是正常的，测试会继续进行
   - 如果需要缓存功能，启动Redis服务

3. **网络超时**
   ```
   发送HTTP请求失败: context deadline exceeded
   ```
   - 检查网络连接
   - 确认ERP服务状态
   - 可能需要增加超时时间

### 调试技巧

1. **启用详细日志**
   ```bash
   INTEGRATION_TEST=1 go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration -test.v
   ```

2. **仅运行特定测试**
   ```bash
   INTEGRATION_TEST=1 go test ./internal/task/reporter/ -run TestSyncOrder_Integration -count=1
   ```

3. **禁用测试缓存**
   ```bash
   INTEGRATION_TEST=1 go test ./internal/task/reporter/ -run TestSyncOrder_Integration -count=1
   ```

## 扩展测试

### 添加新的测试场景

1. **创建新的数据生成函数**
   ```go
   func createSpecialTestData(cfg *config.ERPAPIConfig) *erp.DispenserTransactionRequest {
       // 自定义测试数据逻辑
   }
   ```

2. **添加新的环境变量支持**
   ```go
   if os.Getenv("SPECIAL_TEST") == "true" {
       testRequest = createSpecialTestData(cfg)
   }
   ```

3. **增加验证逻辑**
   ```go
   // 添加特定业务逻辑验证
   if resp.Code == 200 && resp.Data.TransactionCount > 0 {
       // 自定义验证逻辑
   }
   ```

## 生产环境注意事项

⚠️ **重要提醒**：

1. **不要在生产环境运行集成测试**
2. **确保使用测试环境的凭证和URL**
3. **测试数据不会影响真实业务数据**
4. **定期更新测试凭证以确保有效性**

## 持续集成

如果需要在CI/CD流程中集成：

```yaml
# GitHub Actions 示例
- name: Run Integration Tests
  env:
    INTEGRATION_TEST: 1
    ERP_BASE_URL: ${{ secrets.UAT_ERP_URL }}
  run: go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration
``` 