# Redis降级方案

## 概述

HOS Reporter的Token管理器现在支持Redis失效时的自动降级功能。当Redis不可用时，系统会自动切换到内存缓存，确保服务的连续性。

## 功能特性

### 1. 自动降级机制
- **Redis健康检测**: 自动检测Redis连接状态
- **无缝切换**: Redis失效时自动降级到内存缓存
- **故障恢复**: Redis恢复后可重新使用（需重启服务）

### 2. 缓存策略
- **优先级**: Redis缓存 > 内存缓存 > API调用
- **失效检测**: 通过连接错误自动识别Redis故障
- **降级标记**: 维护Redis健康状态标记

### 3. 数据一致性
- **同步清理**: 清除缓存时同时处理Redis和内存
- **过期管理**: 内存缓存支持TTL过期检查
- **线程安全**: 使用读写锁保护内存缓存

## 架构设计

### 数据结构

```go
type TokenManager struct {
    httpClient   *http.Client
    erpConfig    *config.ERPAPIConfig
    redisClient  *redis.Client
    memoryCache  map[string]*TokenEntry // 内存缓存
    cacheMutex   sync.RWMutex           // 读写锁
    redisHealthy bool                   // Redis健康状态
}

type TokenEntry struct {
    Token     string
    ExpiresAt time.Time
}
```

### 工作流程

```mermaid
graph TD
    A[获取Token请求] --> B{Redis可用?}
    B -->|是| C[尝试Redis读取]
    B -->|否| G[使用内存缓存]
    
    C --> D{读取成功?}
    D -->|是| E[返回Token]
    D -->|否| F[标记Redis不健康]
    F --> G
    
    G --> H{内存缓存命中?}
    H -->|是| I{Token未过期?}
    H -->|否| J[调用ERP API]
    
    I -->|是| E
    I -->|否| J
    
    J --> K[获取新Token]
    K --> L[缓存到内存]
    L --> E
    
    style F fill:#ff9999
    style G fill:#99ccff
    style L fill:#99ff99
```

## 使用场景

### 1. 正常运行（Redis可用）
```
TokenManager.GetToken()
  ↓
检查Redis缓存
  ↓
命中：返回Token
未命中：获取新Token → 缓存到Redis → 返回
```

### 2. Redis故障降级
```
TokenManager.GetToken()
  ↓
尝试Redis访问 → 失败
  ↓
标记Redis不健康
  ↓
检查内存缓存
  ↓
命中：返回Token
未命中：获取新Token → 缓存到内存 → 返回
```

### 3. 无Redis环境（集成测试）
```
TokenManager.GetToken()
  ↓
检测到redisClient为nil
  ↓
直接使用内存缓存
```

## 配置说明

### 初始化
```go
// 有Redis环境
tokenManager := erp.NewTokenManager(httpClient, erpConfig, redisClient)

// 无Redis环境（如集成测试）
tokenManager := erp.NewTokenManager(httpClient, erpConfig, nil)
```

### 监控接口
```go
// 获取缓存状态
status := tokenManager.GetCacheStatus()
// 返回:
// {
//   "redis_healthy": false,
//   "redis_available": true,
//   "memory_cache_count": 1,
//   "memory_entries": {
//     "transaction": {
//       "expires_at": "2025-08-08T16:41:26Z",
//       "expired": false
//     }
//   }
// }
```

## 性能对比

| 场景 | Redis可用 | Redis降级 | 无Redis |
|------|----------|----------|---------|
| 缓存命中 | ~1ms | ~0.1ms | ~0.1ms |
| 缓存未命中 | API调用 + Redis写入 | API调用 + 内存写入 | API调用 + 内存写入 |
| 内存使用 | 最低 | 中等 | 中等 |
| 持久性 | 是 | 否 | 否 |

## 注意事项

### 1. 内存限制
- 内存缓存在服务重启时丢失
- 不适合大量Token的长期存储
- 建议监控内存使用情况

### 2. 集群环境
- 不同实例的内存缓存独立
- Redis故障影响所有实例
- 考虑使用Redis集群提高可用性

### 3. 故障恢复
- Redis恢复后需要重启服务以重新标记为健康
- 或者实现定期健康检查机制（未实现）

## 测试覆盖

### 1. 单元测试
- ✅ Redis故障自动降级
- ✅ 内存缓存功能
- ✅ Token过期检查
- ✅ 缓存清理机制
- ✅ 状态监控接口

### 2. 集成测试
- ✅ 无Redis环境运行
- ✅ Redis连接失败处理
- ✅ 与ERP API的完整流程

### 3. 测试命令
```bash
# 运行降级功能测试
go test -v -run TestTokenManager_RedisFallbackToMemory

# 运行无Redis测试
go test -v -run TestTokenManager_NoRedis

# 运行Token过期测试
go test -v -run TestTokenEntry_Expiration
```

## 日志说明

### 正常日志
```
Token已缓存到内存，用户类型: transaction，过期时间: 2025-08-08T16:41:26Z
已清除用户类型 transaction 的Token缓存
```

### 警告日志
```
警告: Redis访问失败，降级为内存缓存: dial tcp [::1]:6379: connect: connection refused
警告: Redis缓存Token失败: dial tcp [::1]:6379: connect: connection refused
警告: 清除Token缓存时发生错误: [清除Redis Token缓存失败: ...]
```

## 最佳实践

### 1. 生产环境
- 优先使用Redis集群保证高可用性
- 监控Redis健康状态
- 设置合适的Token过期时间

### 2. 测试环境
- 集成测试可以不依赖Redis
- 使用内存缓存进行快速测试
- 验证降级机制的可靠性

### 3. 开发环境
- 本地开发可以使用内存缓存
- 简化环境搭建复杂度
- 专注业务逻辑开发 