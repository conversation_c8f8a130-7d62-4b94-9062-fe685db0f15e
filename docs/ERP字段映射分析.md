# ERP交易数据字段映射分析

## 概述
本文档详细分析ERP接口`TransactionDataItem`结构中每个字段的数据来源、获取方式、联查关系，以及当前实现状态。

## 数据库架构说明
- **order_schema**: 订单相关表（订单、油品交易、支付等）
- **core_schema**: 核心数据表（用户、站点、权限等）
- **oil_schema**: 油品相关表（产品、价格等）
- **payment_schema**: 支付相关表（支付方式、支付记录等）
- **promotion_schema**: 优惠相关表（促销、折扣、券等）

## 字段映射分析

### 1. 基础交易信息

#### `transactionId` (string)
- **备注**: 交易ID, order_table
- **数据来源**: `order_schema.orders.id`
- **取值方式**: 基于订单ID生成唯一交易标识，如`TXN{order_id}_{timestamp}`
- **联查**: 直接从订单主表获取
- **当前实现状态**: ✅ **已实现** - 使用`generateTransactionID`生成，基于订单ID

#### `slipNumber` (string)
- **备注**: 订单编号, order_table
- **数据来源**: `order_schema.orders.order_number`
- **取值方式**: 直接使用订单编号，或基于订单ID生成小票号如`SLIP_{order_id}`
- **联查**: 直接从订单主表获取
- **当前实现状态**: ✅ **已实现** - 使用`generateSlipNumber`生成，基于订单ID

### 2. 设备信息

#### `dispenserNumber` (int)
- **备注**: 加油机编号, order_table.transaction,Pump + Nozzle 做一个 Map 对应
- **数据来源**: `order_schema.fuel_transactions.pump_id`
- **取值方式**: 
  - 通过Map映射：`pump_id` → `dispenserNumber`
  - 需要维护Pump ID到Dispenser编号的映射表
  - 后备方案：使用默认值1
- **联查**: `orders` → `fuel_transaction_order_links` → `fuel_transactions`
- **当前实现状态**: ⚠️ **部分实现** - 在`fillFromFuelTransaction`中如果有数据会更新，但**缺少Pump ID到Dispenser编号的映射表**

#### `nozzleNumber` (int)
- **备注**: 油枪编号, order_table.transaction
- **数据来源**: `order_schema.fuel_transactions.nozzle_id`
- **取值方式**: 
  - 通过Map映射：`nozzle_id` → `nozzleNumber`
  - 需要维护Nozzle ID到Nozzle编号的映射表
  - 后备方案：使用默认值1
- **联查**: `orders` → `fuel_transaction_order_links` → `fuel_transactions`
- **当前实现状态**: ⚠️ **部分实现** - 在`fillFromFuelTransaction`中如果有数据会更新，但**缺少Nozzle ID到Nozzle编号的映射表**

### 3. 车辆信息

#### `vehicleType` (int)
- **备注**: 车辆类型, order_table.order.metadata
- **数据来源**: `order_schema.orders.metadata`
- **取值方式**: 
  - 从订单metadata JSON字段中提取车辆类型
  - 映射规则：1=汽车, 2=摩托车, 3=卡车
  - 后备方案：默认值1（汽车）
- **联查**: 直接从订单主表获取
- **当前实现状态**: ❌ **未实现** - 当前使用固定值`VehicleTypeCar`，**需要从order.metadata JSON字段解析**

#### `vehicleID` (string)
- **备注**: 车牌号, order_table.order.metadata
- **数据来源**: `order_schema.orders.metadata`
- **取值方式**: 
  - 从订单metadata JSON字段中提取车牌号
  - 后备方案：生成格式如`VEH_{order_id}`
- **联查**: 直接从订单主表获取
- **当前实现状态**: ❌ **未实现** - 当前使用`generateVehicleID`生成，**需要从order.metadata JSON字段解析**

#### `iD_VehicleTypeGroup` (string)
- **备注**: 车辆类型组, order_table.order.metadata.[Car or Motor]
- **数据来源**: `order_schema.orders.metadata`
- **取值方式**: 
  - 从订单metadata中提取或基于vehicleType映射
  - 映射规则："Car", "Motor", "Truck"
- **联查**: 直接从订单主表获取
- **当前实现状态**: ❌ **未实现** - 当前使用固定值"Car"，**需要从order.metadata JSON字段解析**

### 4. 产品信息

#### `productID` (string)
- **备注**: 油品代码, order_table.transaction
- **数据来源**: `order_schema.fuel_transactions.fuel_type`
- **取值方式**: 
  - 从燃油交易表的fuel_type字段映射到ERP产品代码
  - 映射规则：PERTAMAX → "PERTAMAX", PERTALITE → "PERTALITE"等
  - 后备方案：默认"PERTALITE"
- **联查**: `orders` → `fuel_transaction_order_links` → `fuel_transactions`
- **当前实现状态**: ✅ **已实现** - 在`fillFromFuelTransaction`中通过`mapFuelTypeToProductID`映射

#### `iD_ProductGroup` (string)
- **备注**: 油品类型组, order_table.transaction.fuel_type - oil_table.oil_product.Category
- **数据来源**: `oil_schema.oil_products.category`
- **取值方式**: 
  - 通过fuel_type关联oil_products表获取category
  - 后备方案：默认"Fuel"
- **联查**: `fuel_transactions.fuel_type` → `oil_products.code` → `oil_products.category`
- **当前实现状态**: ❌ **未实现** - 当前使用固定值"Fuel"，**需要联查oil_schema.oil_products表**

### 5. 操作员信息

#### `operatorID` (string)
- **备注**: 员工编号, order_table.transaction
- **数据来源**: 通过员工卡关联获取员工姓名
- **取值方式**: 
  - 优先：`orders.staff_card_id` → `staff_cards.user_id` → `users.full_name`
  - 或者：`fuel_transactions.staff_card_id` → `staff_cards.user_id` → `users.full_name`
  - 次选：`orders.employee_no`（如果没有staff_card_id）
  - 后备：默认"OP001"
- **联查**: `staff_card_id` → `staff_cards.user_id` → `users.full_name`
- **当前实现状态**: ✅ **已实现** - 使用`getOperatorID`获取，优先使用员工卡ID或员工编号

### 6. 金额和数量

#### `amount` (float64)
- **备注**: 订单金额, order_table.order.net_amount
- **数据来源**: `order_schema.orders.final_amount`
- **取值方式**: 直接使用订单的最终金额（扣除折扣后）
- **联查**: 直接从订单主表获取
- **当前实现状态**: ✅ **已实现** - 在`fillFromFuelTransaction`中使用`ft.Amount`，在`fillFromOrderData`中使用`order.TotalAmount`

#### `price` (float64)
- **备注**: 交易单价, order_table.transaction.price
- **数据来源**: `order_schema.fuel_transactions.unit_price`
- **取值方式**: 
  - 优先从燃油交易表获取单价
  - 后备方案：使用默认单价15000.00
- **联查**: `orders` → `fuel_transaction_order_links` → `fuel_transactions`
- **当前实现状态**: ✅ **已实现** - 在`fillFromFuelTransaction`中使用`ft.UnitPrice`

#### `volume` (float64)
- **备注**: 交易体积, order_table.transaction.volume
- **数据来源**: `order_schema.fuel_transactions.volume`
- **取值方式**: 
  - 优先从燃油交易表获取加油量
  - 后备方案：通过金额/单价计算
- **联查**: `orders` → `fuel_transaction_order_links` → `fuel_transactions`
- **当前实现状态**: ✅ **已实现** - 在`fillFromFuelTransaction`中使用`ft.Volume`

### 7. 时间信息

#### `transactionDate` (string)
- **备注**: 交易时间, order_table.transaction.created_at
- **数据来源**: 多个时间字段优先级获取
- **取值方式**: 
  - 优先：`fuel_transactions.nozzle_end_time`（挂枪时间）
  - 次选：`fuel_transactions.created_at`（交易创建时间）
  - 后备：`orders.created_at`（订单创建时间）
  - 格式：`2006-01-02 15:04:05`
- **联查**: `orders` → `fuel_transaction_order_links` → `fuel_transactions`
- **当前实现状态**: ✅ **已实现** - 在`fillFromFuelTransaction`中优先使用`ft.NozzleEndTime`，否则使用`ft.CreatedAt`

### 8. 计数器信息

#### `totalizerStart` (float64)
- **备注**: 起始计数器, order_table.transaction.start_totalizer
- **数据来源**: `order_schema.fuel_transactions.start_totalizer`
- **取值方式**: 
  - 优先使用实际开始计数器读数
  - 后备方案：`total_volume - volume`
- **联查**: `orders` → `fuel_transaction_order_links` → `fuel_transactions`
- **当前实现状态**: ✅ **已实现** - 在`fillFromFuelTransaction`中使用`ft.StartTotalizer`

#### `totalizerEnd` (float64)
- **备注**: 结束计数器, order_table.transaction.end_totalizer
- **数据来源**: `order_schema.fuel_transactions.end_totalizer`
- **取值方式**: 
  - 优先使用实际结束计数器读数
  - 后备方案：`total_volume`
- **联查**: `orders` → `fuel_transaction_order_links` → `fuel_transactions`
- **当前实现状态**: ✅ **已实现** - 在`fillFromFuelTransaction`中使用`ft.EndTotalizer`

### 9. 站点信息

#### `siteID` (string)
- **备注**: 站点ID, order_table.order.site_id
- **数据来源**: `order_schema.orders.station_id`
- **取值方式**: 格式化为`SITE{station_id:03d}`，如SITE001
- **联查**: 直接从订单主表获取
- **当前实现状态**: ✅ **已实现** - 使用`getSiteID`基于`order.StationID`

#### `deviceID` (string)
- **备注**: 设备ID, order_table.order.metadata
- **数据来源**: `order_schema.orders.station_id`或metadata
- **取值方式**: 
  - 优先从orders.metadata提取
  - 后备方案：格式化为`DEV{station_id:03d}`
- **联查**: 直接从订单主表获取
- **当前实现状态**: ❌ **未实现** - 当前基于`order.StationID`生成，**需要从order.metadata JSON字段解析**

#### `areaSite` (string)
- **备注**: order_table.transaction.site_id - core_table.sites.address
- **数据来源**: `core_schema.stations.address`
- **取值方式**: 
  - 通过station_id关联stations表获取地址信息
  - 从address JSON字段提取区域信息
  - 后备方案：默认"Jakarta"
- **联查**: `orders.station_id` → `stations.address`
- **当前实现状态**: ❌ **未实现** - 当前使用固定值"Jakarta"，**需要联查stations表**

### 10. 会员和RFID信息

#### `rFIDVehicleID` (string)
- **备注**: B2B, Null
- **数据来源**: `order_schema.fuel_transactions.member_card_id`
- **取值方式**: 
  - 从燃油交易表获取会员卡ID
  - B2B场景可能为空
- **联查**: `orders` → `fuel_transaction_order_links` → `fuel_transactions`
- **当前实现状态**: ✅ **已实现** - 在`fillFromFuelTransaction`中使用`ft.MemberCardID`

### 11. 客户信息

#### `customerName` (string)
- **备注**: order_table.order.metadata
- **数据来源**: `order_schema.orders.customer_name`或metadata
- **取值方式**: 
  - 优先使用customer_name字段
  - 次选从metadata中提取
  - 后备方案："Guest Customer"
- **联查**: 直接从订单主表获取
- **当前实现状态**: ⚠️ **部分实现** - 在`fillCustomerInfo`中使用`order.CustomerName`，**需要从metadata解析**

#### `customerPhoneNo` (string)
- **备注**: order_table.order.metadata
- **数据来源**: `order_schema.orders.metadata`
- **取值方式**: 从订单metadata JSON字段中提取电话号码
- **联查**: 直接从订单主表获取
- **当前实现状态**: ❌ **未实现** - 当前使用空字符串，**需要从order.metadata JSON字段解析**

#### `email` (string)
- **备注**: order_table.order.metadata | null
- **数据来源**: `order_schema.orders.metadata`
- **取值方式**: 从订单metadata JSON字段中提取邮箱
- **联查**: 直接从订单主表获取
- **当前实现状态**: ❌ **未实现** - 当前使用空字符串，**需要从order.metadata JSON字段解析**

#### `gender` (string)
- **备注**: "male" or "female"
- **数据来源**: `order_schema.orders.metadata`
- **取值方式**: 从订单metadata JSON字段中提取性别信息
- **联查**: 直接从订单主表获取
- **当前实现状态**: ❌ **未实现** - 当前使用空字符串，**需要从order.metadata JSON字段解析**

#### `dob` (*string)
- **备注**: order_table.order.metadata | null
- **数据来源**: `order_schema.orders.metadata`
- **取值方式**: 从订单metadata JSON字段中提取生日，注意DateTime格式转换
- **联查**: 直接从订单主表获取
- **当前实现状态**: ❌ **未实现** - 当前未设置，**需要从order.metadata JSON字段解析**

### 12. 支付相关

#### `fieldTambahan1` (string)
- **备注**: order_table.order.id - payment_table.payments.order_id - payment_method - payment_table.payment_methods.display_name
- **数据来源**: `payment_schema.payment_methods.display_name`
- **取值方式**: 通过订单ID关联支付记录，获取支付方式显示名称
- **联查**: `orders.id` → `payments.order_id` → `payment_methods.display_name`
- **当前实现状态**: ❌ **未实现** - 当前使用空字符串，**需要联查payment_schema.payment_methods表**

#### `fieldTambahan2` (string)
- **备注**: order_table.transaction.staff_card_id - order_table.staff_card.user_id - core_table.users.full_name
- **数据来源**: `core_schema.users.full_name`
- **取值方式**: 通过员工卡ID关联用户表，获取员工姓名
- **联查**: `fuel_transactions.staff_card_id` → `staff_cards.user_id` → `users.full_name`
- **当前实现状态**: ❌ **未实现** - 当前使用空字符串，**需要联查core_schema.users表**

#### `fieldTambahan3` (string)
- **备注**: voucher code, null
- **数据来源**: 券码相关表（可能在promotion_schema中）
- **取值方式**: 暂时为空，未来可扩展券码功能
- **联查**: 待定
- **当前实现状态**: ✅ **已实现** - 空字符串，符合null要求

### 13. 优惠相关

#### `promotionType` (string)
- **备注**: order_table.order_promotion.promotion_name
- **数据来源**: `order_schema.order_promotions.promotion_name`
- **取值方式**: 
  - 从订单优惠表获取优惠名称
  - 后备方案："None"
- **联查**: `orders.id` → `order_promotions.order_id`
- **当前实现状态**: ❌ **未实现** - 当前使用固定值"None"，**需要联查order_schema.order_promotions表**

#### `percentDiscount` (float64)
- **备注**: 类型为百分比折扣的优惠，折扣百分比, order_table.order_promotion.promotion_type = "percent".metadata
- **数据来源**: `order_schema.order_promotions.metadata`
- **取值方式**: 
  - 筛选promotion_type="percent"的记录
  - 从metadata中提取折扣百分比
- **联查**: `orders.id` → `order_promotions.order_id`
- **当前实现状态**: ❌ **未实现** - 当前使用固定值0.00，**需要联查order_schema.order_promotions表**

#### `amountPercentDiscount` (float64)
- **备注**: 类型为百分比折扣的优惠，折扣的金额, order_table.order_promotion.promotion_type = "percent".discount_amount
- **数据来源**: `order_schema.order_promotions.discount_amount`
- **取值方式**: 筛选promotion_type="percent"的记录的折扣金额
- **联查**: `orders.id` → `order_promotions.order_id`
- **当前实现状态**: ❌ **未实现** - 当前使用固定值0.00，**需要联查order_schema.order_promotions表**

#### `amountDiscount` (float64)
- **备注**: 类型为金额折扣的优惠，折扣的金额, order_table.order_promotion.promotion_type = "fixed_amount".discount_amount
- **数据来源**: `order_schema.order_promotions.discount_amount`
- **取值方式**: 筛选promotion_type="fixed_amount"的记录的折扣金额
- **联查**: `orders.id` → `order_promotions.order_id`
- **当前实现状态**: ❌ **未实现** - 当前使用固定值0.00，**需要联查order_schema.order_promotions表**

#### `flagItemPromotion` (int)
- **备注**: 是否为优惠，0 否，1 是
- **数据来源**: `order_schema.order_promotions`表的存在性
- **取值方式**: 检查订单是否有关联的优惠记录，有则为1，无则为0
- **联查**: `orders.id` → `order_promotions.order_id`
- **当前实现状态**: ❌ **未实现** - 当前使用固定值0，**需要联查order_schema.order_promotions表**

#### `finalDiscount` (float64)
- **备注**: 订单折扣金额, order_table.order.discount_amount
- **数据来源**: `order_schema.orders.discount_amount`
- **取值方式**: 直接使用订单的总折扣金额
- **联查**: 直接从订单主表获取
- **当前实现状态**: ❌ **未实现** - 当前使用固定值0.00，**需要使用order.discount_amount字段**

### 14. 其他字段

#### `voucher` (float64)
- **备注**: null
- **数据来源**: 暂无
- **取值方式**: 固定为0.00
- **联查**: 无
- **当前实现状态**: ✅ **已实现** - 固定值0.00，符合null要求

#### `point` (int)
- **备注**: null
- **数据来源**: 暂无
- **取值方式**: 固定为0
- **联查**: 无
- **当前实现状态**: ✅ **已实现** - 固定值0，符合null要求

#### `transactionLength` (int)
- **备注**: 交易长度, order_table.order.payment_time - order_table.transaction.created_at
- **数据来源**: 时间差计算
- **取值方式**: 
  - 计算支付完成时间与交易开始时间的差值（秒）
  - 后备方案：默认值5
- **联查**: 涉及订单和交易时间字段
- **当前实现状态**: ❌ **未实现** - 当前使用固定值5，**需要计算时间差**

#### `reprint` (int)
- **备注**: 是否重印, order_table.metadata | 0
- **数据来源**: `order_schema.orders.metadata`
- **取值方式**: 
  - 从订单metadata中检查重印标志
  - 默认值：0（不重印）
- **联查**: 直接从订单主表获取
- **当前实现状态**: ❌ **未实现** - 当前使用固定值0，**需要从order.metadata JSON字段解析**

## 主要联查路径

1. **订单→燃油交易**: `orders` → `fuel_transaction_order_links` → `fuel_transactions`
2. **订单→支付信息**: `orders` → `order_payments` → `payment_methods`
3. **订单→优惠信息**: `orders` → `order_promotions`
4. **员工信息**: `staff_card_id` → `staff_cards` → `users`
5. **站点信息**: `station_id` → `stations`
6. **油品信息**: `fuel_type` → `oil_products`

## 实现状态汇总

### ✅ 已正确实现（17个字段）
- `transactionId` - 基于订单ID生成
- `slipNumber` - 基于订单ID生成
- `productID` - 从fuel_transactions.fuel_type映射
- `operatorID` - 从员工卡ID或员工编号获取
- `amount` - 从fuel_transactions或订单金额
- `price` - 从fuel_transactions.unit_price
- `volume` - 从fuel_transactions.volume
- `transactionDate` - 从fuel_transactions时间字段
- `totalizerStart` - 从fuel_transactions.start_totalizer
- `totalizerEnd` - 从fuel_transactions.end_totalizer
- `siteID` - 基于order.station_id
- `rFIDVehicleID` - 从fuel_transactions.member_card_id
- `fieldTambahan3` - 正确为空
- `voucher` - 正确为空
- `point` - 正确为空
- `messageHeader` - 正确为空
- `messageID` - 固定值"msg11"
- `category` - 固定值"fuel"

### ⚠️ 部分实现（3个字段）
- `dispenserNumber` - 有数据时会更新，但缺少映射表
- `nozzleNumber` - 有数据时会更新，但缺少映射表
- `customerName` - 使用order.customer_name，但需要从metadata解析

### ❌ 未实现（23个字段）
- `vehicleType` - 需要从order.metadata解析
- `vehicleID` - 需要从order.metadata解析
- `iD_VehicleTypeGroup` - 需要从order.metadata解析
- `iD_ProductGroup` - 需要联查oil_products表
- `deviceID` - 需要从order.metadata解析
- `areaSite` - 需要联查stations表
- `customerPhoneNo` - 需要从order.metadata解析
- `email` - 需要从order.metadata解析
- `gender` - 需要从order.metadata解析
- `dob` - 需要从order.metadata解析
- `fieldTambahan1` - 需要联查payment_methods表
- `fieldTambahan2` - 需要联查users表
- `promotionType` - 需要联查order_promotions表
- `percentDiscount` - 需要联查order_promotions表
- `amountPercentDiscount` - 需要联查order_promotions表
- `amountDiscount` - 需要联查order_promotions表
- `flagItemPromotion` - 需要联查order_promotions表
- `finalDiscount` - 需要使用order.discount_amount字段
- `transactionLength` - 需要计算时间差
- `reprint` - 需要从order.metadata解析
- `DEXROWID` - 需要从order.metadata解析
- `DEX_ROW_TS` - 需要从order.metadata解析

### 数据获取优先级

1. **优先使用真实交易数据**：如果存在fuel_transactions记录，优先使用其中的详细数据
2. **订单数据作为后备**：当交易数据不完整时，使用orders表的基础信息
3. **默认值兜底**：当前两者都不可用时，使用预定义的默认值

## 注意事项

1. **JSON字段处理**：metadata字段需要JSON解析，注意空值处理
2. **数据类型转换**：varchar字段转int时需要错误处理
3. **时间格式**：统一使用`2006-01-02 15:04:05`格式
4. **空值处理**：某些字段可以为空，需要适当的默认值策略
5. **性能考虑**：复杂联查可能影响性能，考虑适当的索引和缓存策略

## 改进建议

1. **优先实现关键联查**：
   - 支付方式信息联查（fieldTambahan1）
   - 员工姓名联查（fieldTambahan2）
   - 站点地址联查（areaSite）
   - 优惠信息联查（promotion相关字段）

2. **metadata解析优化**：
   - 实现order.metadata JSON字段解析
   - 提取车辆信息、客户信息等

3. **映射表完善**：
   - 建立Pump/Nozzle ID到编号的映射
   - 建立产品分类映射

4. **性能优化**：
   - 考虑在查询时预加载关联数据
   - 使用适当的数据库索引 