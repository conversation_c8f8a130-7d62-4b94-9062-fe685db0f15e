# 集成测试调用链路分析

## 时序图 - 完整调用流程

```mermaid
sequenceDiagram
    participant Test as TestSyncOrder_Integration
    participant Config as config.LoadConfig
    participant DB1 as database.NewOrderServiceClient
    participant DB2 as database.NewPostgresClient
    participant OrderRepo as repository.OrderRepoImpl
    participant ReportRepo as repository.ReportRecordRepoImpl
    participant ERPClient as erp.ERPClient
    participant TokenMgr as erp.TokenManager
    participant ERP_API as ERP System

    Test->>Config: LoadConfig("../../../config/config.yaml")
    Config-->>Test: globalCfg

    Test->>DB1: NewOrderServiceClient(&globalCfg.OdDatabase)
    Note over DB1: DSN with search_path=order_schema
    DB1-->>Test: odDbClient

    Test->>DB2: NewPostgresClient(&globalCfg.RpDatabase)
    DB2-->>Test: rpDbClient

    Test->>OrderRepo: NewOrderRepo(odDbClient)
    OrderRepo-->>Test: orderRepo

    Test->>ReportRepo: NewReportRecordRepo(rpDbClient)
    ReportRepo-->>Test: reportRecordRepo

    Test->>ReportRepo: GetByType(ctx, "transaction")
    ReportRepo-->>Test: reportedOrderIDs

    Test->>OrderRepo: FindPaidNotReport(ctx, reportedOrderIDs)
    Note over OrderRepo: Query: order_schema.orders<br/>WHERE status='completed'<br/>AND id NOT IN (reportedOrderIDs)
    OrderRepo-->>Test: orders[309个]

    Test->>Test: createRequestFromOrders(cfg, orders[:1])
    Note over Test: 字段映射:<br/>TransactionNumber→TransactionID<br/>UnitPrice→Price<br/>CreatedAt→TransactionDate

    Test->>ERPClient: PostTransactions(ctx, testRequest)
    ERPClient->>ERPClient: postTransactionsWithRetry(ctx, request, 1)
    ERPClient->>ERPClient: Post(ctx, "/ATG/PostTransaction", "transaction", request)

    ERPClient->>TokenMgr: GetToken(ctx, "transaction")
    Note over TokenMgr: redisClient == nil，跳过缓存
    TokenMgr->>TokenMgr: FetchNewToken(ctx, "transaction")
    TokenMgr->>TokenMgr: isUATEnvironment()
    Note over TokenMgr: 检查 erpConfig.BaseURLUAT
    TokenMgr->>ERP_API: POST /ATG/GetToken
    Note over ERP_API: {"key":"123456789",<br/>"user":"wecar",<br/>"password":"1!8$3#7*4@"}
    ERP_API-->>TokenMgr: token

    TokenMgr-->>ERPClient: token

    ERPClient->>ERPClient: 构造完整URL
    Note over ERPClient: baseURL + "/ATG/PostTransaction"
    ERPClient->>ERP_API: POST /ATG/PostTransaction
    Note over ERP_API: 请求体包含真实交易数据:<br/>订单ID=137<br/>交易号=fffdd06d-8d23-4c93-9703-38a0ffbf2b7b<br/>金额=5000.00, 油量=2.37

    ERP_API-->>ERPClient: HTTP 200 + 业务错误
    Note over ERP_API: {"transactionData[0].dob":<br/>["Error converting value {null}<br/>to type 'System.DateTime'"]}

    ERPClient-->>Test: ERP业务错误 (错误码: 0)

    Test->>Test: 检查响应并记录结果
    Note over Test: 测试通过 (PASS)<br/>- 数据库连接成功<br/>- 真实数据获取成功<br/>- ERP请求发送成功<br/>- 发现dob字段格式问题
```

## 架构图 - 关键组件和函数调用

```mermaid
graph LR
    subgraph "测试文件"
        Test[integration_test.go]
        Test --> TestFunc[TestSyncOrder_Integration]
        Test --> CreateReq[createRequestFromOrders]
        Test --> TestData[createDefaultTestData]
    end
    
    subgraph "配置模块"
        Config[config/config.go]
        Config --> LoadConf[LoadConfig]
        Config --> GetConf[GetConfig - 有全局依赖问题]
    end
    
    subgraph "数据库模块"
        DB[infra/database/postgres.go]
        DB --> NewOrd[NewOrderServiceClient]
        DB --> NewRep[NewPostgresClient]
        
        Repo[internal/repository/]
        Repo --> OrdRepo[order_repo.go]
        Repo --> RepRepo[report_record_repo.go]
        
        OrdRepo --> FindPaid[FindPaidNotReport]
        OrdRepo --> GetByIDs[GetByIDs]
        RepRepo --> GetByType[GetByType]
    end
    
    subgraph "ERP适配器"
        ERP[internal/adapter/erp/]
        ERP --> Client[client.go]
        ERP --> Token[token.go]
        ERP --> Trans[transaction.go]
        
        Client --> Post[Post方法 - 修复了GetBaseURL依赖]
        Token --> GetToken[GetToken - 修复了Redis空指针]
        Token --> FetchToken[FetchNewToken - 修复了config依赖]
        Token --> IsUAT[isUATEnvironment - 修复了config依赖]
        Trans --> PostTrans[PostTransactions]
    end
    
    subgraph "问题修复点"
        Fix1[修复1: search_path=order_schema]
        Fix2[修复2: Redis null检查]
        Fix3[修复3: 移除config.GetBaseURL依赖]
        Fix4[修复4: 直接加载配置文件]
    end
    
    NewOrd -.-> Fix1
    GetToken -.-> Fix2
    Post -.-> Fix3
    LoadConf -.-> Fix4
    
    style Test fill:#e1f5fe
    style ERP fill:#f3e5f5
    style Fix1 fill:#c8e6c9
    style Fix2 fill:#c8e6c9
    style Fix3 fill:#c8e6c9
    style Fix4 fill:#c8e6c9
```

## 数据结构调整指南

### 调整位置

如果您需要调整数据结构（如修复生日字段报错等），主要在以下位置进行调整：

#### 1. 主要调整文件
- **文件位置**：`internal/task/reporter/integration_test.go`
- **函数名称**：`createRequestFromOrders()`
- **行数范围**：大约 216-284 行

#### 2. 当前字段映射逻辑

```go
// 在 createRequestFromOrders 函数中
transactionItem := erp.TransactionData{
    TransactionID:    fmt.Sprintf("real_txn_%d_%s", order.ID, fuelTransaction.TransactionNumber),
    SlipNumber:       fmt.Sprintf("REAL_SLIP_%d", order.ID),
    DispenserNumber:  1,
    NozzleNumber:     1,
    VehicleType:      1,
    VehicleID:        fmt.Sprintf("VEH_%d", order.ID),
    ProductID:        "",
    OperatorID:       getEmployeeOperatorID(order.EmployeeID),
    Amount:           int64(order.TotalAmount),
    Price:            fuelTransaction.UnitPrice,
    Volume:           fuelTransaction.Volume,
    TransactionDate:  fuelTransaction.CreatedAt.Format("2006-01-02 15:04:05"),
    // ... 其他字段
    Dob:              "", // 🔴 问题字段：应该为 nil 或有效日期
}
```

### 需要查询的表和字段

#### 3. 数据库表结构分析

基于您的需求，以下是可能需要查询的表：

```sql
-- 当前已查询的表
order_schema.orders                    -- 订单基本信息
order_schema.fuel_transactions        -- 燃油交易信息
order_schema.fuel_transaction_order_links -- 订单与交易关联

-- 可能需要额外查询的表（根据ERP字段需求）
order_schema.employees                 -- 员工信息（获取operatorID等）
order_schema.order_payments           -- 支付信息
order_schema.shifts                   -- 班次信息
order_schema.staff_cards              -- 员工卡信息

-- 客户相关表（如果需要客户生日、电话等信息）
customer_schema.customers             -- 客户基本信息
customer_schema.customer_profiles     -- 客户详细资料
```

#### 4. 字段映射扩展建议

如果需要完整的客户信息，可以在 `createRequestFromOrders` 函数中添加额外查询：

```go
// 扩展查询示例
func createRequestFromOrders(cfg *config.ERPAPIConfig, orders []*order_service.Order) *erp.DispenserTransactionRequest {
    // ... 现有代码 ...
    
    for _, order := range orders {
        // 1. 查询客户信息（如果需要）
        // customer := queryCustomerInfo(order.CustomerID)
        
        // 2. 查询员工详细信息
        // employee := queryEmployeeInfo(order.EmployeeID)
        
        // 3. 查询支付信息
        // payment := queryPaymentInfo(order.ID)
        
        transactionItem := erp.TransactionData{
            // ... 现有字段 ...
            
            // 修复的字段
            Dob:              nil,  // 或 customer.DateOfBirth
            CustomerName:     "",   // 或 customer.Name
            CustomerPhoneNo:  "",   // 或 customer.Phone
            Email:           "",    // 或 customer.Email
            Gender:          "",    // 或 customer.Gender
        }
    }
}
```

### 具体调整步骤

#### 5. 立即可做的修复

**修复生日字段**：
```go
// 当前代码（第 xxx 行）
Dob: "",

// 修改为
Dob: nil,  // 或者完全删除这个字段
```

#### 6. 如果需要真实客户数据

1. **扩展Repository**：在 `internal/repository/order_repo.go` 中添加方法
2. **增加查询**：查询客户、员工等相关表
3. **更新字段映射**：在 `createRequestFromOrders` 中使用真实数据

#### 7. 测试验证

修改后可以通过以下命令验证：

```bash
# 测试特定订单
INTEGRATION_TEST=1 ORDER_ID=137 go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration

# 测试自定义数据
INTEGRATION_TEST=1 CUSTOM_DATA=true go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration
```

### 总结

- **主要调整位置**：`internal/task/reporter/integration_test.go` 的 `createRequestFromOrders()` 函数
- **立即修复**：将 `Dob: ""` 改为 `Dob: nil`
- **扩展数据源**：根据需要查询更多相关表获取完整客户信息
- **验证方法**：使用环境变量控制测试不同场景 