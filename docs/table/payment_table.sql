-- payment_schema.bank_configs definition

-- Drop table

-- DROP TABLE payment_schema.bank_configs;

CREATE TABLE payment_schema.bank_configs (
	id bigserial NOT NULL,
	bank_code varchar(20) NOT NULL, -- 银行代码，如BCA、BNI、MANDIRI等
	bank_name varchar(100) NOT NULL,
	display_name varchar(100) NOT NULL,
	country_code varchar(3) DEFAULT 'ID'::character varying NOT NULL,
	enabled bool DEFAULT true NOT NULL,
	gateway_class varchar(100) NULL, -- 网关实现类名，用于动态创建银行网关
	gateway_config text NULL, -- JSON格式的银行网关配置
	min_amount numeric(15, 2) DEFAULT 0.00 NULL,
	max_amount numeric(15, 2) NULL,
	daily_limit numeric(15, 2) NULL,
	fee_type varchar(20) NULL,
	fee_value numeric(15, 4) DEFAULT 0.0000 NULL,
	requires_pin bool DEFAULT true NULL,
	supports_refund bool DEFAULT true NULL,
	supports_partial_refund bool DEFAULT true NULL,
	timeout_seconds int4 DEFAULT 60 NULL,
	sort_order int4 DEFAULT 0 NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	deleted_at timestamp NULL,
	CONSTRAINT bank_configs_bank_code_key UNIQUE (bank_code),
	CONSTRAINT bank_configs_pkey PRIMARY KEY (id)
);

CREATE TABLE payment_schema.bank_types (
	id serial4 NOT NULL,
	code varchar(20) NOT NULL,
	"name" varchar(100) NOT NULL,
	name_en varchar(100) NULL,
	is_active bool DEFAULT true NULL,
	sort_order int4 DEFAULT 0 NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT bank_types_code_key UNIQUE (code),
	CONSTRAINT bank_types_pkey PRIMARY KEY (id)
);

CREATE TABLE payment_schema.payment_methods (
	id bigserial NOT NULL,
	"type" varchar(20) NOT NULL, -- 支付类型 - 新增TERA回罐油支付
	"name" varchar(100) NOT NULL,
	display_name varchar(100) NOT NULL,
	description text NULL,
	icon varchar(255) NULL,
	gateway_type varchar(50) NOT NULL,
	gateway_config text NULL, -- JSON格式 - 存储支付网关的特定配置参数
	enabled bool DEFAULT true NOT NULL,
	min_amount numeric(15, 2) DEFAULT 0.00 NULL,
	max_amount numeric(15, 2) NULL,
	daily_limit numeric(15, 2) NULL,
	fee_type varchar(20) NULL,
	fee_value numeric(15, 4) DEFAULT 0.0000 NULL,
	available_time text NULL, -- JSON格式 - 存储支付方式的可用时间配置
	allowed_stations text NULL, -- JSON格式 - 存储允许使用此支付方式的站点列表
	sort_order int4 DEFAULT 0 NULL,
	group_name varchar(50) NULL, -- 支付方式分组 - other表示其他类支付方式
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	deleted_at timestamp NULL,
	CONSTRAINT payment_methods_pkey PRIMARY KEY (id)
);

CREATE TABLE payment_schema.payment_status_changes (
	id bigserial NOT NULL,
	payment_id int8 NOT NULL,
	status varchar(20) NOT NULL,
	changed_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	reason text NULL,
	operator_id varchar(50) NULL,
	CONSTRAINT payment_status_changes_pkey PRIMARY KEY (id)
);

CREATE TABLE payment_schema.payments (
	id bigserial NOT NULL,
	order_id varchar(50) NOT NULL,
	payment_number varchar(50) NOT NULL, -- 支付编号 - 业务层面的唯一支付标识
	amount numeric(15, 2) NOT NULL,
	currency varchar(10) DEFAULT 'IDR'::character varying NOT NULL,
	payment_type varchar(20) NOT NULL,
	payment_method int8 NULL,
	status varchar(20) NOT NULL,
	gateway_type varchar(50) NULL,
	gateway_order_no varchar(100) NULL,
	gateway_response text NULL, -- JSON格式 - 存储支付网关返回的完整响应数据
	customer_id varchar(50) NULL,
	customer_name varchar(100) NULL,
	station_id int8 NOT NULL,
	terminal_id varchar(50) NULL,
	operator_id varchar(50) NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	paid_at timestamp NULL,
	expires_at timestamp NULL,
	metadata text NULL, -- JSON格式 - 存储扩展信息和业务相关的元数据
	CONSTRAINT payments_payment_number_key UNIQUE (payment_number),
	CONSTRAINT payments_pkey PRIMARY KEY (id)
);

CREATE TABLE payment_schema.refunds (
	id bigserial NOT NULL,
	payment_id int8 NOT NULL,
	refund_number varchar(50) NOT NULL,
	amount numeric(15, 2) NOT NULL,
	reason text NOT NULL,
	status varchar(20) NOT NULL,
	gateway_refund_no varchar(100) NULL,
	refunded_at timestamp NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	metadata text NULL,
	CONSTRAINT refunds_pkey PRIMARY KEY (id),
	CONSTRAINT refunds_refund_number_key UNIQUE (refund_number)
);