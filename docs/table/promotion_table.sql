
CREATE TABLE promotion_schema.promotion (
	id varchar(36) NOT NULL,
	"name" varchar(30) NOT NULL,
	description varchar(200) NULL,
	"type" varchar(20) NOT NULL,
	status promotion_schema.promotion_status NOT NULL,
	"scope" varchar(20) NOT NULL,
	value float8 NOT NULL,
	min_order_amount float8 DEFAULT 0 NULL,
	max_discount_amount float8 DEFAULT 0 NULL,
	start_time timestamp NOT NULL,
	end_time timestamp NOT NULL,
	created_at timestamp NOT NULL,
	updated_at timestamp NOT NULL,
	deleted_at timestamp NULL,
	target_ids _text NULL,
	requires_coupon bool DEFAULT false NULL,
	priority int4 DEFAULT 0 NULL,
	stackable bool DEFAULT false NULL,
	max_use_count int4 DEFAULT 0 NULL,
	used_count int4 DEFAULT 0 NULL,
	max_per_user int4 DEFAULT 0 NULL,
	daily_quota int4 DEFAULT 0 NULL,
	weekly_quota int4 DEFAULT 0 NULL,
	monthly_quota int4 DEFAULT 0 NULL,
	total_quota int4 DEFAULT 0 NULL,
	current_quota int4 DEFAULT 0 NULL,
	for_members bool DEFAULT true NULL,
	for_non_members bool DEFAULT false NULL,
	member_levels _text NULL,
	member_tags _text NULL,
	gift_fuel_type varchar(50) NULL,
	gift_fuel_amount float8 DEFAULT 0 NULL,
	metadata jsonb NULL,
	"version" int4 DEFAULT 1 NOT NULL,
	reviewer_id varchar(36) NULL,
	review_time timestamp NULL,
	review_comments text NULL,
	last_status_change timestamp NULL,
	status_change_reason text NULL,
	last_changed_by varchar(36) NULL,
	created_by varchar(36) NULL,
	site_ids _text NULL,
	site_exclude_ids _text NULL,
	all_sites bool NULL,
	used_quota int4 DEFAULT 0 NULL,
	CONSTRAINT promotion_pkey PRIMARY KEY (id)
);
CREATE TABLE promotion_schema.coupons (
	id varchar(36) NOT NULL,
	code varchar(20) NOT NULL,
	promotion_id varchar(36) NOT NULL,
	status varchar(20) NOT NULL,
	user_id varchar(36) NULL,
	order_id varchar(36) NULL,
	valid_from timestamp NOT NULL,
	valid_until timestamp NOT NULL,
	created_at timestamp NOT NULL,
	updated_at timestamp NOT NULL,
	used_at timestamp NULL,
	metadata jsonb NULL,
	CONSTRAINT coupons_code_key UNIQUE (code),
	CONSTRAINT coupons_pkey PRIMARY KEY (id),
	CONSTRAINT fk_promotion FOREIGN KEY (promotion_id) REFERENCES promotion_schema.promotion(id) ON DELETE SET NULL
);
-- DROP TABLE promotion_schema.discounts;

CREATE TABLE promotion_schema.discounts (
	id varchar(36) NOT NULL,
	promotion_id varchar(36) NOT NULL,
	coupon_id varchar(36) NULL,
	code varchar(50) NULL,
	"type" varchar(20) NOT NULL,
	discount_value float8 NOT NULL,
	min_purchase_amount float8 DEFAULT 0 NULL,
	product_ids _text NULL,
	category_ids _text NULL,
	max_use_count int4 DEFAULT 0 NULL,
	used_count int4 DEFAULT 0 NULL,
	metadata jsonb NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT discounts_code_key UNIQUE (code),
	CONSTRAINT discounts_pkey PRIMARY KEY (id),
	CONSTRAINT fk_coupon FOREIGN KEY (coupon_id) REFERENCES promotion_schema.coupons(id) ON DELETE SET NULL,
	CONSTRAINT fk_promotion FOREIGN KEY (promotion_id) REFERENCES promotion_schema.promotion(id) ON DELETE SET NULL
);

CREATE TABLE promotion_schema.gift_items (
	id varchar(36) NOT NULL,
	promotion_id varchar(36) NOT NULL,
	product_id varchar(36) NOT NULL,
	product_name varchar(100) NOT NULL,
	quantity int4 DEFAULT 1 NOT NULL,
	CONSTRAINT gift_items_pkey PRIMARY KEY (id),
	CONSTRAINT fk_promotion FOREIGN KEY (promotion_id) REFERENCES promotion_schema.promotion(id) ON DELETE SET NULL
);
CREATE TABLE promotion_schema.promotion_audit_log (
	id varchar(36) NOT NULL,
	promotion_id varchar(36) NOT NULL,
	changed_by varchar(36) NOT NULL,
	changed_at timestamp NOT NULL,
	field_name varchar(50) NOT NULL,
	old_value text NULL,
	new_value text NULL,
	change_type varchar(20) NOT NULL,
	CONSTRAINT promotion_audit_log_pkey PRIMARY KEY (id),
	CONSTRAINT fk_promotion FOREIGN KEY (promotion_id) REFERENCES promotion_schema.promotion(id) ON DELETE SET NULL
);

CREATE TABLE promotion_schema.promotion_status_history (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	promotion_id varchar(36) NOT NULL,
	from_status varchar(20) NULL,
	to_status varchar(20) NOT NULL,
	changed_by varchar(36) NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT promotion_status_history_pkey PRIMARY KEY (id),
	CONSTRAINT fk_promotion FOREIGN KEY (promotion_id) REFERENCES promotion_schema.promotion(id) ON DELETE SET NULL
);
CREATE TABLE promotion_schema.rules (
	id varchar(36) NOT NULL,
	promotion_id varchar(36) NOT NULL,
	"type" varchar(30) NOT NULL,
	"operator" varchar(20) NOT NULL,
	value jsonb NOT NULL,
	priority int4 DEFAULT 0 NULL,
	description varchar(200) NULL,
	metadata jsonb NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	min_purchase_amount numeric(10, 2) DEFAULT 0 NULL,
	max_purchase_amount numeric(10, 2) DEFAULT 0 NULL,
	product_ids _text NULL,
	category_ids _text NULL,
	gradient_level int4 DEFAULT 0 NULL,
	threshold_type varchar(20) NULL,
	threshold_value numeric(10, 2) DEFAULT 0 NULL,
	parent_id uuid NULL,
	"path" varchar(255) NULL,
	CONSTRAINT rules_pkey PRIMARY KEY (id),
	CONSTRAINT fk_promotion FOREIGN KEY (promotion_id) REFERENCES promotion_schema.promotion(id) ON DELETE SET NULL
);

CREATE TABLE promotion_schema.time_cycles (
	id varchar(36) NOT NULL,
	promotion_id varchar(36) NOT NULL,
	"type" varchar(20) NOT NULL,
	daily_start_time int4 DEFAULT 0 NULL,
	daily_end_time int4 DEFAULT 24 NULL,
	week_days _int4 NULL,
	month_days _int4 NULL,
	month_week_num int4 NULL,
	month_week_day int4 NULL,
	year_month int4 NULL,
	year_day int4 NULL,
	start_time timestamp NULL,
	end_time timestamp NULL,
	priority int4 DEFAULT 0 NOT NULL,
	metadata jsonb NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT time_cycles_pkey PRIMARY KEY (id),
	CONSTRAINT fk_promotion FOREIGN KEY (promotion_id) REFERENCES promotion_schema.promotion(id) ON DELETE SET NULL
);