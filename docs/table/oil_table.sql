
CREATE TABLE oil_schema.oil_prices (
	id bigserial NOT NULL,
	product_id int8 NOT NULL,
	price numeric(10, 2) NOT NULL,
	cost_price numeric(10, 2) NULL,
	suggest_price numeric(10, 2) NULL,
	wholesale_price numeric(10, 2) NULL,
	status varchar(20) DEFAULT '生效'::character varying NULL,
	pricing_policy_id int8 NULL,
	effective_at timestamptz NOT NULL,
	created_by varchar(50) NOT NULL,
	created_at timestamptz NULL,
	updated_at timestamptz NULL,
	CONSTRAINT oil_prices_pkey PRIMARY KEY (id)
);

CREATE TABLE oil_schema.oil_products (
	id bigserial NOT NULL,
	code varchar(50) NOT NULL,
	"name" varchar(100) NOT NULL,
	category varchar(50) NULL,
	grade varchar(20) NULL,
	description text NULL,
	unit varchar(20) DEFAULT 'L'::character varying NULL,
	default_price numeric NULL,
	current_price numeric NULL,
	is_active bool DEFAULT true NULL,
	specifications text NULL,
	image_url varchar(255) NULL,
	created_by text NULL,
	created_at timestamptz NULL,
	updated_at timestamptz NULL,
	CONSTRAINT oil_products_pkey PRIMARY KEY (id)
);

CREATE TABLE oil_schema.price_adjustment_items (
	id bigserial NOT NULL,
	process_id int8 NOT NULL,
	product_id int8 NOT NULL,
	old_price numeric NULL,
	adjustment_type varchar(20) NOT NULL,
	adjustment_value numeric NULL,
	new_price numeric NULL,
	price_change_amount numeric NULL,
	price_change_percentage numeric NULL,
	remarks text NULL,
	applied_at timestamptz NULL,
	created_at timestamptz NULL,
	updated_at timestamptz NULL,
	CONSTRAINT price_adjustment_items_pkey PRIMARY KEY (id)
);

CREATE TABLE oil_schema.price_adjustment_processes (
	id bigserial NOT NULL,
	"name" varchar(100) NOT NULL,
	description text NULL,
	initiated_by varchar(50) NOT NULL,
	approved_by varchar(50) NULL,
	submitted_by varchar(50) NULL,
	rejected_by varchar(50) NULL,
	applied_by varchar(50) NULL,
	status varchar(20) NOT NULL,
	adjustment_type varchar(20) NOT NULL,
	pricing_policy_id int8 NULL,
	effective_date timestamptz NULL,
	product_ids jsonb NULL,
	reason_code varchar(50) NULL,
	reason text NULL,
	rejection_reason text NULL,
	submitted_at timestamptz NULL,
	approved_at timestamptz NULL,
	rejected_at timestamptz NULL,
	applied_at timestamptz NULL,
	created_at timestamptz NULL,
	updated_at timestamptz NULL,
	CONSTRAINT price_adjustment_processes_pkey PRIMARY KEY (id)
);
CREATE TABLE oil_schema.price_histories (
	id bigserial NOT NULL,
	product_id int8 NOT NULL,
	old_price numeric(10, 2) NOT NULL,
	new_price numeric(10, 2) NOT NULL,
	change_amount numeric(10, 2) NULL,
	change_type varchar(50) NULL,
	process_id int8 NULL,
	change_time timestamptz NOT NULL,
	reason text NULL,
	remarks text NULL,
	changed_by varchar(50) NOT NULL,
	created_at timestamptz NULL,
	CONSTRAINT price_histories_pkey PRIMARY KEY (id)
);
CREATE TABLE oil_schema.pricing_strategies (
	id bigserial NOT NULL,
	"name" varchar(100) NOT NULL,
	description text NULL,
	strategy_type varchar(50) NOT NULL,
	parameters jsonb NULL,
	start_date timestamptz NOT NULL,
	end_date timestamptz NULL,
	active bool DEFAULT true NULL,
	priority int8 DEFAULT 0 NULL,
	applicable_to_all bool DEFAULT false NULL,
	created_by text NULL,
	created_at timestamptz NULL,
	updated_at timestamptz NULL,
	CONSTRAINT pricing_strategies_pkey PRIMARY KEY (id)
);

CREATE TABLE oil_schema.strategy_customer_mappings (
	id bigserial NOT NULL,
	pricing_strategy_id int8 NOT NULL,
	customer_id int8 NOT NULL,
	customer_group_id int8 NULL,
	created_at timestamptz NULL,
	updated_at timestamptz NULL,
	CONSTRAINT strategy_customer_mappings_pkey PRIMARY KEY (id)
);

CREATE TABLE oil_schema.strategy_product_mappings (
	id bigserial NOT NULL,
	pricing_strategy_id int8 NOT NULL,
	product_id int8 NOT NULL,
	created_at timestamptz NULL,
	updated_at timestamptz NULL,
	CONSTRAINT strategy_product_mappings_pkey PRIMARY KEY (id)
);
CREATE TABLE oil_schema.oil_thresholds (
	id bigserial NOT NULL,
	oil_product_id int8 NOT NULL,
	warning_level numeric NOT NULL,
	critical_level numeric NOT NULL,
	target_level numeric NOT NULL,
	max_level numeric NOT NULL,
	created_at timestamptz NULL,
	updated_at timestamptz NULL,
	CONSTRAINT oil_thresholds_pkey PRIMARY KEY (id),
	CONSTRAINT fk_oil_thresholds_oil_product FOREIGN KEY (oil_product_id) REFERENCES oil_schema.oil_products(id)
);
