-- core_schema.config_items definition

-- Drop table

-- DROP TABLE core_schema.config_items;

CREATE TABLE core_schema.config_items (
	id bigserial NOT NULL,
	config_category varchar(20) NOT NULL, -- 配置分类
	config_module varchar(50) NULL, -- 配置模块
	config_key varchar(200) NOT NULL, -- 配置键名
	config_name varchar(200) NOT NULL, -- 配置名称
	description text NULL, -- 配置描述
	data_type core_schema."data_type_enum" DEFAULT 'STRING'::core_schema.data_type_enum NULL, -- 数据类型
	config_value text NULL, -- 配置值
	is_enabled bool DEFAULT true NULL, -- 是否启用
	created_by int8 NOT NULL, -- 创建人ID
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- 创建时间
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- 更新时间
	CONSTRAINT config_items_pkey PRIMARY KEY (id),
	CONSTRAINT uk_config_key UNIQUE (config_key)
);



CREATE TABLE core_schema.dict_categories (
	id bigserial NOT NULL,
	category_name varchar(100) NOT NULL, -- 分类名称
	category_code varchar(50) NOT NULL, -- 分类编码，全局唯一
	description text NULL, -- 分类描述
	item_count int4 DEFAULT 0 NULL, -- 包含的项目数量
	status varchar(20) DEFAULT 'active'::character varying NULL, -- 状态：active,inactive
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 创建时间
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 更新时间
	created_by varchar(50) NULL, -- 创建人
	updated_by varchar(50) NULL, -- 更新人
	CONSTRAINT dict_categories_category_code_key UNIQUE (category_code),
	CONSTRAINT dict_categories_pkey PRIMARY KEY (id)
);

CREATE TABLE core_schema.operation_logs (
	id bigserial NOT NULL, -- 主键ID，自增
	log_id varchar(64) NOT NULL, -- 日志唯一标识，UUID格式
	log_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 操作发生时间
	user_id varchar(64) NULL, -- 操作用户ID
	user_name varchar(100) NULL, -- 操作用户名称
	session_id varchar(128) NULL, -- 用户会话ID
	operation_type varchar(50) NOT NULL, -- 操作类型，如CREATE/UPDATE/DELETE等
	resource_type varchar(50) NULL, -- 操作资源类型，如USER/ROLE/CONFIG等
	resource_id varchar(64) NULL, -- 操作资源的唯一标识
	operation_description text NULL, -- 操作描述信息
	request_ip inet NULL, -- 请求来源IP地址
	user_agent text NULL, -- 用户代理信息
	request_url varchar(500) NULL, -- 请求的URL路径
	request_method varchar(10) NULL, -- HTTP请求方法
	request_params jsonb NULL, -- 请求参数，JSON格式
	response_status int4 NULL, -- HTTP响应状态码
	business_context jsonb NULL, -- 业务上下文信息，JSON格式
	system_module varchar(50) NULL, -- 系统模块标识（SYSTEM/USER/AUTH/CONFIG/SECURITY/BUSINESS）
	log_level varchar(20) DEFAULT 'INFO'::character varying NULL, -- 日志级别，INFO/WARN/ERROR
	execution_time int4 NULL, -- 操作执行时间，单位毫秒
	trace_id varchar(64) NULL, -- 链路跟踪ID
	tags jsonb NULL, -- 日志标签，JSON数组格式
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- 记录创建时间
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NULL, -- 记录更新时间
	CONSTRAINT operation_logs_log_id_key UNIQUE (log_id),
	CONSTRAINT operation_logs_pkey PRIMARY KEY (id)
);


CREATE TABLE core_schema.permissions (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	code varchar(100) NOT NULL, -- 权限代码，格式：module:resource:action
	resource varchar(100) NOT NULL, -- 资源标识，如：user, role, order
	"action" varchar(50) NOT NULL, -- 操作类型：create/read/update/delete/execute/manage
	description text NULL,
	"module" varchar(50) NOT NULL, -- 所属模块，如：auth, order, inventory
	is_system bool DEFAULT false NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	created_by uuid NULL,
	updated_by uuid NULL,
	deleted_at timestamptz NULL,
	CONSTRAINT permissions_action_check CHECK (((action)::text = ANY (ARRAY[('create'::character varying)::text, ('read'::character varying)::text, ('update'::character varying)::text, ('delete'::character varying)::text, ('execute'::character varying)::text, ('manage'::character varying)::text]))),
	CONSTRAINT permissions_code_key UNIQUE (code),
	CONSTRAINT permissions_pkey PRIMARY KEY (id)
);


CREATE TABLE core_schema.stations (
	id bigserial NOT NULL,
	site_code varchar(50) NOT NULL, -- 站点编码，全局唯一
	site_name varchar(200) NOT NULL, -- 站点名称
	address jsonb NOT NULL, -- 详细地址信息
	latitude numeric(10, 8) NULL, -- 纬度
	longitude numeric(11, 8) NULL, -- 经度
	administrative_division jsonb NULL, -- 行政区划信息
	business_hours jsonb NULL, -- 营业时间配置
	business_status varchar(20) DEFAULT 'ACTIVE'::character varying NULL, -- 营业状态：ACTIVE-营业中, SUSPENDED-暂停营业, CLOSED-关闭
	contact_info jsonb NULL, -- 联系方式信息
	manager_id int8 NULL, -- 站长ID
	management_level varchar(20) NULL, -- 管理级别
	parent_organization varchar(100) NULL, -- 上级组织
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by int8 NULL, -- 创建人ID
	updated_by int8 NULL, -- 更新人ID
	deleted_at timestamp NULL, -- 软删除时间
	"version" int4 DEFAULT 1 NULL, -- 版本号，用于乐观锁
	CONSTRAINT stations_pkey PRIMARY KEY (id),
	CONSTRAINT stations_site_code_key UNIQUE (site_code)
);


CREATE TABLE core_schema.user_station_roles (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL, -- 主键ID
	user_id uuid NOT NULL, -- 用户ID，关联users表
	station_id int8 NOT NULL, -- 站点ID，关联stations表
	role_type varchar(50) DEFAULT 'manager'::character varying NOT NULL, -- 管理角色类型：manager-站长，assistant_manager-副站长，supervisor-主管，operator-操作员
	status varchar(20) DEFAULT 'active'::character varying NOT NULL, -- 关联状态：active-生效，inactive-失效，suspended-暂停
	granted_by uuid NULL, -- 授权人ID，记录是谁分配的这个角色
	granted_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 授权时间
	expires_at timestamptz NULL, -- 授权过期时间，为空表示永久有效
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 创建时间
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 更新时间
	created_by uuid NULL, -- 创建人ID
	updated_by uuid NULL, -- 更新人ID
	deleted_at timestamptz NULL, -- 软删除时间
	CONSTRAINT chk_role_type CHECK (((role_type)::text = ANY (ARRAY[('manager'::character varying)::text, ('assistant_manager'::character varying)::text, ('supervisor'::character varying)::text, ('operator'::character varying)::text]))),
	CONSTRAINT chk_status CHECK (((status)::text = ANY (ARRAY[('active'::character varying)::text, ('inactive'::character varying)::text, ('suspended'::character varying)::text]))),
	CONSTRAINT uk_user_station_active UNIQUE (user_id, station_id) DEFERRABLE INITIALLY DEFERRED,
	CONSTRAINT user_station_roles_pkey PRIMARY KEY (id)
);


CREATE TABLE core_schema.users (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	username varchar(50) NOT NULL, -- 用户名，3-20字符，全局唯一
	email varchar(100) NOT NULL, -- 邮箱地址，用于登录和通知
	phone varchar(20) NULL,
	password_hash varchar(255) NOT NULL, -- 密码哈希值，使用bcrypt算法
	salt varchar(32) NOT NULL, -- 密码盐值，增强安全性
	full_name varchar(100) NOT NULL,
	department varchar(100) NULL,
	status varchar(20) DEFAULT 'active'::character varying NOT NULL,
	last_login_at timestamptz NULL,
	password_expires_at timestamptz NULL,
	failed_login_attempts int4 DEFAULT 0 NULL, -- 连续失败登录次数，超过3次锁定账户
	locked_until timestamptz NULL, -- 账户锁定到期时间
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	created_by uuid NULL,
	updated_by uuid NULL,
	deleted_at timestamptz NULL,
	CONSTRAINT users_email_key UNIQUE (email),
	CONSTRAINT users_pkey PRIMARY KEY (id),
	CONSTRAINT users_status_check CHECK (((status)::text = ANY (ARRAY[('active'::character varying)::text, ('inactive'::character varying)::text, ('locked'::character varying)::text, ('disabled'::character varying)::text]))),
	CONSTRAINT users_username_key UNIQUE (username)
);

CREATE TABLE core_schema.dict_items (
	id bigserial NOT NULL,
	category_id int8 NOT NULL, -- 关联分类ID
	item_name varchar(100) NOT NULL, -- 项目名称
	item_code varchar(50) NOT NULL, -- 项目编码，在分类内唯一
	description text NULL, -- 项目描述
	value_type varchar(20) DEFAULT 'string'::character varying NULL, -- 值类型：string,number,boolean,date
	value_format varchar(100) NULL, -- 值格式：如date格式(YYYY-MM-DD)，number精度等
	min_value varchar(100) NULL, -- 最小值（用于number和date类型）
	max_value varchar(100) NULL, -- 最大值（用于number和date类型）
	regex_pattern varchar(500) NULL, -- 正则表达式验证模式（用于string类型）
	allowed_values text NULL, -- 允许的值列表（JSON格式，用于枚举类型）
	sort_order int4 DEFAULT 0 NULL, -- 排序序号
	status varchar(20) DEFAULT 'active'::character varying NULL, -- 状态：active,inactive
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 创建时间
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 更新时间
	created_by varchar(50) NULL, -- 创建人
	updated_by varchar(50) NULL, -- 更新人
	CONSTRAINT dict_items_pkey PRIMARY KEY (id),
	CONSTRAINT uk_dict_items_category_code UNIQUE (category_id, item_code),
	CONSTRAINT fk_dict_items_category FOREIGN KEY (category_id) REFERENCES core_schema.dict_categories(id) ON DELETE CASCADE
);


CREATE TABLE core_schema.dict_values (
	id bigserial NOT NULL,
	item_id int8 NOT NULL, -- 关联项目ID
	display_name varchar(200) NOT NULL, -- 显示名称
	value varchar(500) NOT NULL, -- 实际值
	description text NULL, -- 值描述
	is_default bool DEFAULT false NULL, -- 是否默认值
	sort_order int4 DEFAULT 0 NULL, -- 排序序号
	status varchar(20) DEFAULT 'active'::character varying NULL, -- 状态：active,inactive
	source_system varchar(20) DEFAULT 'local'::character varying NULL, -- 数据来源：local,hos,external
	sync_id varchar(100) NULL, -- 外部系统同步ID
	sync_time timestamptz NULL, -- 最后同步时间
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 创建时间
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 更新时间
	created_by varchar(50) NULL, -- 创建人
	updated_by varchar(50) NULL, -- 更新人
	CONSTRAINT dict_values_pkey PRIMARY KEY (id),
	CONSTRAINT uk_dict_values_item_value UNIQUE (item_id, value),
	CONSTRAINT fk_dict_values_item FOREIGN KEY (item_id) REFERENCES core_schema.dict_items(id) ON DELETE CASCADE
);


CREATE TABLE core_schema.login_logs (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	user_id uuid NULL, -- 用户ID，登录失败时可能为空
	username varchar(50) NOT NULL,
	ip_address inet NOT NULL,
	user_agent text NULL,
	login_result varchar(20) NOT NULL,
	failure_reason varchar(100) NULL, -- 登录失败原因：wrong_password, account_locked, account_disabled等
	session_id varchar(128) NULL,
	login_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT login_logs_login_result_check CHECK (((login_result)::text = ANY (ARRAY[('success'::character varying)::text, ('failed'::character varying)::text, ('blocked'::character varying)::text]))),
	CONSTRAINT login_logs_pkey PRIMARY KEY (id),
	CONSTRAINT login_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES core_schema.users(id)
);

CREATE TABLE core_schema.permission_logs (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	user_id uuid NOT NULL,
	session_id varchar(128) NULL,
	resource varchar(100) NOT NULL,
	"action" varchar(50) NOT NULL,
	permission_code varchar(100) NULL,
	"result" varchar(20) NOT NULL,
	denial_reason varchar(100) NULL, -- 权限拒绝原因：no_permission, session_expired, account_disabled等
	ip_address inet NULL,
	user_agent text NULL,
	request_path varchar(500) NULL, -- HTTP请求路径，用于审计
	request_method varchar(10) NULL,
	verified_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT permission_logs_pkey PRIMARY KEY (id),
	CONSTRAINT permission_logs_result_check CHECK (((result)::text = ANY (ARRAY[('granted'::character varying)::text, ('denied'::character varying)::text]))),
	CONSTRAINT permission_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES core_schema.users(id)
);

CREATE TABLE core_schema.roles (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	code varchar(50) NOT NULL, -- 角色代码，用于程序中的角色识别
	description text NULL,
	"level" int4 DEFAULT 1 NULL, -- 角色级别，数字越大权限越高
	parent_id uuid NULL, -- 父角色ID，支持角色继承
	is_system bool DEFAULT false NULL, -- 系统角色标识，系统角色不可删除
	status varchar(20) DEFAULT 'active'::character varying NOT NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	created_by uuid NULL,
	updated_by uuid NULL,
	deleted_at timestamptz NULL,
	CONSTRAINT roles_code_key UNIQUE (code),
	CONSTRAINT roles_name_key UNIQUE (name),
	CONSTRAINT roles_pkey PRIMARY KEY (id),
	CONSTRAINT roles_status_check CHECK (((status)::text = ANY (ARRAY[('active'::character varying)::text, ('inactive'::character varying)::text]))),
	CONSTRAINT roles_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES core_schema.roles(id)
);


CREATE TABLE core_schema.station_equipments (
	id bigserial NOT NULL,
	stid int8 NOT NULL,
	equipment_code varchar(50) NOT NULL, -- 设备编码
	equipment_name varchar(200) NOT NULL, -- 设备名称
	equipment_type varchar(50) NOT NULL, -- 设备类型：EDC_POS-EDC POS, EDC_PG-EDC PG
	equipment_model varchar(100) NULL, -- 设备型号
	serial_number varchar(100) NULL, -- 设备序列号/SN编号
	manufacturer varchar(100) NULL, -- 制造商
	install_location varchar(200) NULL, -- 安装位置
	install_date date NULL, -- 安装日期
	status varchar(20) DEFAULT 'NORMAL'::character varying NULL, -- 设备状态：NORMAL-正常, FAULT-故障, MAINTENANCE-维护中, OFFLINE-离线
	last_status_update timestamp NULL, -- 状态最后更新时间
	linked_equipment_id int8 NULL, -- 关联的设备ID，用于设备间的一对一关联
	ip_address varchar(45) NULL, -- 设备IP地址（支持IPv4和IPv6）
	port int4 NULL, -- 设备服务端口号
	network_config jsonb NULL, -- 网络配置参数（子网掩码、网关、DNS等）
	"configuration" jsonb NULL, -- 设备配置参数
	specifications jsonb NULL, -- 技术规格信息
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by int8 NULL,
	updated_by int8 NULL,
	deleted_at timestamp NULL,
	"version" int4 DEFAULT 1 NULL,
	CONSTRAINT chk_equipment_linking CHECK (((((equipment_type)::text = 'EDC_PG'::text) AND (linked_equipment_id IS NOT NULL)) OR (((equipment_type)::text = 'EDC_POS'::text) AND (linked_equipment_id IS NULL)))),
	CONSTRAINT chk_network_config CHECK (((((equipment_type)::text = 'EDC_PG'::text) AND (ip_address IS NOT NULL) AND (port IS NOT NULL)) OR ((equipment_type)::text = 'EDC_POS'::text))),
	CONSTRAINT station_equipments_pkey PRIMARY KEY (id),
	CONSTRAINT uk_site_equipment_code UNIQUE (stid, equipment_code),
	CONSTRAINT uk_site_equipment_ip UNIQUE (stid, ip_address),
	CONSTRAINT fk_linked_equipment FOREIGN KEY (linked_equipment_id) REFERENCES core_schema.station_equipments(id),
	CONSTRAINT station_equipments_stid_fkey FOREIGN KEY (stid) REFERENCES core_schema.stations(id) ON DELETE CASCADE
);


-- DROP TABLE core_schema.station_operation_params;

CREATE TABLE core_schema.station_operation_params (
	id bigserial NOT NULL,
	stid int8 NOT NULL,
	param_category varchar(50) NOT NULL, -- 参数分类：OPERATION_RULES-运营规则, SERVICE_PARAMS-服务参数, FINANCIAL_CONFIG-财务配置, MARKETING_STRATEGY-营销策略
	param_key varchar(100) NOT NULL, -- 参数键
	param_name varchar(200) NOT NULL, -- 参数名称
	param_description text NULL, -- 参数描述
	param_value jsonb NOT NULL, -- 参数值
	param_type varchar(20) DEFAULT 'STRING'::character varying NULL, -- 参数类型：STRING, NUMBER, BOOLEAN, JSON, ARRAY
	effective_time timestamp NULL, -- 生效时间
	expire_time timestamp NULL, -- 过期时间
	status varchar(20) DEFAULT 'ACTIVE'::character varying NULL, -- 参数状态：ACTIVE-生效中, INACTIVE-未生效, EXPIRED-已过期
	validation_rules jsonb NULL, -- 参数验证规则
	default_value jsonb NULL, -- 默认值
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by int8 NULL,
	updated_by int8 NULL,
	deleted_at timestamp NULL,
	"version" int4 DEFAULT 1 NULL,
	CONSTRAINT station_operation_params_pkey PRIMARY KEY (id),
	CONSTRAINT uk_site_param UNIQUE (stid, param_category, param_key),
	CONSTRAINT station_operation_params_stid_fkey FOREIGN KEY (stid) REFERENCES core_schema.stations(id) ON DELETE CASCADE
);


CREATE TABLE core_schema.tag_groups (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	code varchar(50) NOT NULL, -- 分组代码，用于程序中的分组识别
	description text NULL,
	parent_id uuid NULL, -- 父分组ID，支持分组层级
	"level" int4 DEFAULT 1 NULL, -- 分组级别，根据层级自动计算
	sort_order int4 DEFAULT 0 NULL, -- 排序权重，数字越小越靠前
	applicable_entities _text DEFAULT '{}'::text[] NULL, -- 适用的实体类型，如user、station、product等
	status varchar(20) DEFAULT 'active'::character varying NOT NULL,
	is_system bool DEFAULT false NULL, -- 系统分组标识，系统分组不可删除
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	created_by uuid NULL,
	updated_by uuid NULL,
	deleted_at timestamptz NULL,
	CONSTRAINT tag_groups_code_key UNIQUE (code),
	CONSTRAINT tag_groups_pkey PRIMARY KEY (id),
	CONSTRAINT tag_groups_status_check CHECK (((status)::text = ANY (ARRAY[('active'::character varying)::text, ('inactive'::character varying)::text]))),
	CONSTRAINT tag_groups_created_by_fkey FOREIGN KEY (created_by) REFERENCES core_schema.users(id),
	CONSTRAINT tag_groups_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES core_schema.tag_groups(id),
	CONSTRAINT tag_groups_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES core_schema.users(id)
);


CREATE TABLE core_schema.tags (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	group_id uuid NOT NULL,
	"name" varchar(100) NOT NULL,
	code varchar(100) NOT NULL, -- 标签代码，格式建议：group_code:tag_name
	description text NULL,
	sort_order int4 DEFAULT 0 NULL, -- 标签在分组内的排序权重
	usage_count int4 DEFAULT 0 NULL, -- 标签使用次数，用于热门标签排序
	is_system bool DEFAULT false NULL,
	is_auto bool DEFAULT false NULL, -- 自动标签标识，由系统规则自动添加
	status varchar(20) DEFAULT 'active'::character varying NOT NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	created_by uuid NULL,
	updated_by uuid NULL,
	deleted_at timestamptz NULL,
	CONSTRAINT tags_code_key UNIQUE (code),
	CONSTRAINT tags_pkey PRIMARY KEY (id),
	CONSTRAINT tags_status_check CHECK (((status)::text = ANY (ARRAY[('active'::character varying)::text, ('inactive'::character varying)::text]))),
	CONSTRAINT tags_created_by_fkey FOREIGN KEY (created_by) REFERENCES core_schema.users(id),
	CONSTRAINT tags_group_id_fkey FOREIGN KEY (group_id) REFERENCES core_schema.tag_groups(id),
	CONSTRAINT tags_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES core_schema.users(id)
);


CREATE TABLE core_schema.user_roles (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	user_id uuid NOT NULL,
	role_id uuid NOT NULL,
	granted_by uuid NULL, -- 授权人，记录谁给用户分配了这个角色
	granted_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	expires_at timestamptz NULL, -- 角色过期时间，支持临时角色分配
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT user_roles_pkey PRIMARY KEY (id),
	CONSTRAINT user_roles_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES core_schema.users(id),
	CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES core_schema.roles(id) ON DELETE CASCADE,
	CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES core_schema.users(id) ON DELETE CASCADE
);


CREATE TABLE core_schema.user_sessions (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	session_id varchar(128) NOT NULL, -- 会话标识符，用于前端Cookie或Token
	user_id uuid NOT NULL,
	ip_address inet NOT NULL, -- 登录IP地址，用于安全审计
	user_agent text NULL,
	login_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	last_activity_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 最后活动时间，用于会话超时判断
	expires_at timestamptz NOT NULL,
	status varchar(20) DEFAULT 'active'::character varying NOT NULL,
	logout_at timestamptz NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT user_sessions_pkey PRIMARY KEY (id),
	CONSTRAINT user_sessions_session_id_key UNIQUE (session_id),
	CONSTRAINT user_sessions_status_check CHECK (((status)::text = ANY (ARRAY[('active'::character varying)::text, ('expired'::character varying)::text, ('terminated'::character varying)::text]))),
	CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES core_schema.users(id) ON DELETE CASCADE
);


CREATE TABLE core_schema.entity_tags (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	entity_type varchar(20) NOT NULL, -- 实体类型：user-用户，station-油站，product-商品，order-订单等
	entity_id uuid NOT NULL, -- 实体ID，对应不同实体表的主键
	tag_id uuid NOT NULL,
	"source" varchar(20) DEFAULT 'manual'::character varying NOT NULL, -- 标签来源：manual-手动添加，auto-自动添加，import-批量导入，system-系统添加
	tagged_by uuid NULL, -- 打标签的操作人，自动标签时为空
	tagged_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	expires_at timestamptz NULL, -- 标签过期时间，过期后标签失效
	remarks text NULL, -- 打标签时的备注说明
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT entity_tags_entity_type_check CHECK (((entity_type)::text = ANY (ARRAY[('user'::character varying)::text, ('station'::character varying)::text, ('product'::character varying)::text, ('order'::character varying)::text]))),
	CONSTRAINT entity_tags_pkey PRIMARY KEY (id),
	CONSTRAINT entity_tags_source_check CHECK (((source)::text = ANY (ARRAY[('manual'::character varying)::text, ('auto'::character varying)::text, ('import'::character varying)::text, ('system'::character varying)::text]))),
	CONSTRAINT entity_tags_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES core_schema.tags(id) ON DELETE CASCADE,
	CONSTRAINT entity_tags_tagged_by_fkey FOREIGN KEY (tagged_by) REFERENCES core_schema.users(id)
);


CREATE TABLE core_schema.equipment_maintenance_records (
	id bigserial NOT NULL,
	equipment_id int8 NOT NULL,
	maintenance_type varchar(50) NOT NULL, -- 维护类型：ROUTINE-例行维护, REPAIR-故障维修, UPGRADE-升级改造
	maintenance_date timestamp NOT NULL, -- 维护时间
	technician_name varchar(100) NULL, -- 维护技术员
	technician_company varchar(200) NULL, -- 维护公司
	maintenance_content text NULL, -- 维护内容描述
	fault_description text NULL, -- 故障描述（维修时）
	solution_description text NULL, -- 解决方案描述
	maintenance_result varchar(20) DEFAULT 'SUCCESS'::character varying NULL, -- 维护结果：SUCCESS-成功, FAILED-失败, PARTIAL-部分完成
	next_maintenance_date date NULL, -- 下次维护日期
	maintenance_cost numeric(10, 2) NULL, -- 维护费用
	attachments jsonb NULL, -- 相关附件信息
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	created_by int8 NULL,
	CONSTRAINT equipment_maintenance_records_pkey PRIMARY KEY (id),
	CONSTRAINT equipment_maintenance_records_equipment_id_fkey FOREIGN KEY (equipment_id) REFERENCES core_schema.station_equipments(id) ON DELETE CASCADE
);


-- core_schema.param_change_history definition

-- Drop table

-- DROP TABLE core_schema.param_change_history;

CREATE TABLE core_schema.param_change_history (
	id bigserial NOT NULL,
	param_id int8 NOT NULL,
	change_type varchar(20) NOT NULL, -- 变更类型：CREATE, UPDATE, DELETE
	old_value jsonb NULL, -- 变更前值
	new_value jsonb NULL, -- 变更后值
	change_reason text NULL, -- 变更原因
	change_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	changed_by int8 NULL, -- 变更人ID
	CONSTRAINT param_change_history_pkey PRIMARY KEY (id),
	CONSTRAINT param_change_history_param_id_fkey FOREIGN KEY (param_id) REFERENCES core_schema.station_operation_params(id)
);


CREATE TABLE core_schema.role_permissions (
	id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
	role_id uuid NOT NULL,
	permission_id uuid NOT NULL,
	granted_by uuid NULL, -- 授权人，记录谁给角色分配了这个权限
	granted_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT role_permissions_pkey PRIMARY KEY (id),
	CONSTRAINT role_permissions_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES core_schema.users(id),
	CONSTRAINT role_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES core_schema.permissions(id) ON DELETE CASCADE,
	CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES core_schema.roles(id) ON DELETE CASCADE
);
