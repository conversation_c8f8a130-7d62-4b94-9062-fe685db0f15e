-- order_schema.fuel_transaction_order_links definition

-- Drop table

-- DROP TABLE order_schema.fuel_transaction_order_links;

CREATE TABLE order_schema.fuel_transaction_order_links (
	id bigserial NOT NULL,
	fuel_transaction_id int8 NOT NULL,
	order_id int8 NOT NULL,
	allocated_amount numeric(12, 2) NOT NULL,
	status varchar(20) DEFAULT 'active'::character varying NOT NULL,
	metadata jsonb NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	deactivated_at timestamptz NULL,
	CONSTRAINT fuel_transaction_order_links_pkey PRIMARY KEY (id),
	CONSTRAINT unique_fuel_transaction_order UNIQUE (fuel_transaction_id, order_id)
);

CREATE TABLE order_schema.fuel_transactions (
	id bigserial NOT NULL,
	transaction_number varchar(50) NOT NULL,
	station_id int8 NOT NULL,
	pump_id varchar(50) NOT NULL,
	nozzle_id varchar(50) NOT NULL,
	fuel_type varchar(50) NOT NULL,
	fuel_grade varchar(50) NOT NULL,
	tank int4 NOT NULL,
	unit_price numeric(10, 2) NOT NULL,
	volume numeric(10, 3) NOT NULL,
	amount numeric(12, 2) NOT NULL,
	total_volume numeric(10, 3) DEFAULT 0.0 NOT NULL,
	total_amount numeric(12, 2) DEFAULT 0.0 NOT NULL,
	status varchar(50) DEFAULT 'pending'::character varying NOT NULL,
	member_card_id varchar(100) NULL,
	member_id varchar(255) NULL,
	employee_id int8 NULL,
	fcc_transaction_id varchar(100) NULL,
	pos_terminal_id varchar(50) NULL,
	metadata jsonb NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	processed_at timestamptz NULL,
	cancelled_at timestamptz NULL,
	start_totalizer numeric(15, 3) NULL, -- 加油开始时的累计计数器读数
	end_totalizer numeric(15, 3) NULL, -- 加油结束时的累计计数器读数
	nozzle_start_time timestamptz NULL, -- 油枪开始加油时间
	nozzle_end_time timestamptz NULL, -- 油枪挂枪时间
	staff_card_id int8 NULL, -- 关联员工卡ID，新版本使用此字段替代employee_id
	CONSTRAINT fuel_transactions_pkey PRIMARY KEY (id),
	CONSTRAINT fuel_transactions_transaction_number_key UNIQUE (transaction_number)
);
CREATE TABLE order_schema.order_items (
	id bigserial NOT NULL,
	order_id int8 NOT NULL,
	product_id int8 NOT NULL,
	product_name varchar(100) NOT NULL,
	product_type varchar(50) NOT NULL,
	quantity numeric(10, 3) NOT NULL,
	unit_price numeric(12, 2) NOT NULL,
	total_price numeric(12, 2) NOT NULL,
	discount_amount numeric(12, 2) DEFAULT 0.0 NOT NULL,
	final_price numeric(12, 2) NOT NULL,
	tax_amount numeric(12, 2) DEFAULT 0.0 NOT NULL,
	tax_rate numeric(5, 2) DEFAULT 0.0 NOT NULL,
	fuel_grade varchar(50) NULL,
	pump_id varchar(50) NULL,
	metadata jsonb NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT order_items_pkey PRIMARY KEY (id)
);
-- DROP TABLE order_schema.order_payments;

CREATE TABLE order_schema.order_payments (
	id bigserial NOT NULL,
	order_id int8 NOT NULL,
	payment_method varchar(50) NOT NULL,
	payment_reference varchar(100) NULL,
	amount numeric(12, 2) NOT NULL,
	status varchar(20) DEFAULT 'pending'::character varying NOT NULL,
	transaction_id varchar(100) NULL,
	metadata jsonb NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	completed_at timestamptz NULL,
	CONSTRAINT order_payments_pkey PRIMARY KEY (id)
);

CREATE TABLE order_schema.order_promotions (
	id bigserial NOT NULL,
	order_id int8 NOT NULL,
	promotion_id int8 NOT NULL,
	promotion_name varchar(100) NOT NULL,
	promotion_type varchar(50) NOT NULL,
	discount_amount numeric(12, 2) DEFAULT 0.0 NOT NULL,
	free_item_id int8 NULL,
	free_item_name varchar(100) NULL,
	free_item_quantity numeric(10, 3) NULL,
	minimum_order_amount numeric(12, 2) NULL,
	metadata jsonb NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT order_promotions_pkey PRIMARY KEY (id)
);

CREATE TABLE order_schema.orders (
	id bigserial NOT NULL,
	order_number varchar(100) NOT NULL,
	customer_id int8 NULL,
	customer_name varchar(100) NULL,
	station_id int8 NOT NULL,
	status varchar(20) DEFAULT 'new'::character varying NOT NULL,
	total_amount numeric(12, 2) DEFAULT 0.0 NOT NULL,
	discount_amount numeric(12, 2) DEFAULT 0.0 NOT NULL,
	final_amount numeric(12, 2) DEFAULT 0.0 NOT NULL,
	tax_amount numeric(12, 2) DEFAULT 0.0 NOT NULL,
	paid_amount numeric(12, 2) DEFAULT 0.0 NOT NULL,
	metadata jsonb NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	completed_at timestamptz NULL,
	cancelled_at timestamptz NULL,
	employee_no varchar(50) NULL,
	staff_card_id int8 NULL, -- 关联员工卡ID，新版本使用此字段替代employee_no
	CONSTRAINT orders_order_number_key UNIQUE (order_number),
	CONSTRAINT orders_pkey PRIMARY KEY (id)
);
CREATE TABLE order_schema.shift_templates (
	id bigserial NOT NULL, -- 主键ID
	"name" varchar(100) NOT NULL, -- 班次名称，如早班、中班、晚班
	start_time varchar(5) NOT NULL, -- 班次开始时间，格式为HH:MM
	end_time varchar(5) NOT NULL, -- 班次结束时间，格式为HH:MM
	description text NULL, -- 班次描述
	status varchar(20) DEFAULT 'active'::character varying NOT NULL, -- 状态：active-活跃，inactive-停用
	metadata jsonb NULL, -- 元数据，JSON格式
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 创建时间
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL, -- 更新时间
	deleted_at timestamptz NULL, -- 删除时间（软删除）
	CONSTRAINT shift_templates_pkey PRIMARY KEY (id)
);

CREATE TABLE order_schema.shifts (
	id bigserial NOT NULL,
	shift_number varchar(60) NOT NULL,
	station_id int8 NOT NULL,
	start_time timestamptz DEFAULT now() NOT NULL,
	end_time timestamptz NULL,
	metadata jsonb NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	deleted_at timestamptz NULL,
	CONSTRAINT shifts_pkey PRIMARY KEY (id),
	CONSTRAINT shifts_shift_number_key UNIQUE (shift_number)
);
CREATE TABLE order_schema.staff_cards (
	id bigserial NOT NULL, -- 主键ID
	card_number varchar(50) NOT NULL, -- 员工卡号，业务层面的唯一标识
	user_id uuid NOT NULL, -- 关联auth_db.users表的用户ID
	station_id int8 NULL, -- 关联站点ID，NULL表示可在多站点使用
	card_type varchar(20) DEFAULT 'employee'::character varying NOT NULL, -- 卡片类型：employee-员工, manager-管理员, admin-系统管理员
	status varchar(20) DEFAULT 'active'::character varying NOT NULL, -- 状态：active-活跃, inactive-未激活, suspended-暂停, expired-过期
	valid_from timestamptz DEFAULT now() NOT NULL, -- 有效期开始时间
	valid_until timestamptz NULL, -- 有效期结束时间，NULL表示永久有效
	permissions jsonb NULL, -- 卡片特殊权限配置，JSON格式
	metadata jsonb NULL, -- 扩展元数据，包含迁移信息等
	created_at timestamptz DEFAULT now() NOT NULL, -- 创建时间
	updated_at timestamptz DEFAULT now() NOT NULL, -- 更新时间
	deleted_at timestamptz NULL, -- 删除时间（软删除）
	CONSTRAINT chk_staff_cards_card_type CHECK (((card_type)::text = ANY (ARRAY[('employee'::character varying)::text, ('manager'::character varying)::text, ('admin'::character varying)::text]))),
	CONSTRAINT chk_staff_cards_status CHECK (((status)::text = ANY (ARRAY[('active'::character varying)::text, ('inactive'::character varying)::text, ('suspended'::character varying)::text, ('expired'::character varying)::text]))),
	CONSTRAINT chk_staff_cards_valid_period CHECK (((valid_until IS NULL) OR (valid_until > valid_from))),
	CONSTRAINT staff_cards_card_number_key UNIQUE (card_number),
	CONSTRAINT staff_cards_pkey PRIMARY KEY (id)
);

CREATE TABLE order_schema.shift_fuel_details (
	id bigserial NOT NULL,
	shift_id int8 NOT NULL,
	fuel_grade varchar(50) NOT NULL, -- 油品标号
	fuel_type varchar(50) NULL, -- 油品类型
	fuel_name varchar(100) NULL, -- 油品显示名称
	total_volume numeric(12, 3) DEFAULT 0.0 NOT NULL, -- 总销量(升)
	gross_amount numeric(12, 2) DEFAULT 0.0 NOT NULL, -- 优惠前总金额
	total_discount_amount numeric(12, 2) DEFAULT 0.0 NOT NULL, -- 总优惠金额
	net_amount numeric(12, 2) DEFAULT 0.0 NOT NULL, -- 优惠后净金额
	average_price numeric(10, 2) DEFAULT 0.0 NOT NULL, -- 平均单价
	transaction_count int4 DEFAULT 0 NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	employee_id int8 NULL, -- 员工ID，关联employees表，支持按员工维度统计
	staff_card_id int8 NULL, -- 关联员工卡ID，用于按员工卡统计
	CONSTRAINT chk_shift_fuel_details_amount_logic CHECK ((gross_amount = (net_amount + total_discount_amount))),
	CONSTRAINT shift_fuel_details_pkey PRIMARY KEY (id),
	CONSTRAINT shift_fuel_details_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES order_schema.shifts(id) ON DELETE CASCADE
);

CREATE TABLE order_schema.shift_merchandise_details (
	id bigserial NOT NULL,
	shift_id int8 NOT NULL,
	product_id int8 NOT NULL, -- 商品ID
	product_name varchar(100) NOT NULL, -- 商品名称
	product_type varchar(50) NOT NULL, -- 商品类型
	product_category varchar(50) NULL, -- 商品分类
	total_quantity numeric(10, 3) DEFAULT 0.0 NOT NULL, -- 总销售数量
	unit_price numeric(12, 2) DEFAULT 0.0 NOT NULL, -- 单价
	total_amount numeric(12, 2) DEFAULT 0.0 NOT NULL, -- 总销售额
	total_discount_amount numeric(12, 2) DEFAULT 0.0 NOT NULL, -- 总优惠金额
	net_amount numeric(12, 2) DEFAULT 0.0 NOT NULL, -- 净销售额
	transaction_count int4 DEFAULT 0 NOT NULL, -- 交易次数
	created_at timestamptz DEFAULT now() NOT NULL,
	employee_id int8 NULL, -- 员工ID，关联employees表，支持按员工维度统计
	staff_card_id int8 NULL, -- 关联员工卡ID，用于按员工卡统计
	CONSTRAINT shift_merchandise_details_pkey PRIMARY KEY (id),
	CONSTRAINT shift_merchandise_details_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES order_schema.shifts(id) ON DELETE CASCADE
);

CREATE TABLE order_schema.shift_payment_details (
	id bigserial NOT NULL,
	shift_id int8 NOT NULL,
	payment_method varchar(50) NOT NULL, -- 支付方式代码
	payment_method_name varchar(100) NULL, -- 支付方式显示名称
	total_amount numeric(12, 2) DEFAULT 0.0 NOT NULL, -- 该支付方式总金额
	transaction_count int4 DEFAULT 0 NOT NULL, -- 该支付方式交易次数
	created_at timestamptz DEFAULT now() NOT NULL,
	employee_id int8 NULL, -- 员工ID，关联employees表，支持按员工维度统计
	staff_card_id int8 NULL, -- 关联员工卡ID，用于按员工卡统计
	CONSTRAINT shift_payment_details_pkey PRIMARY KEY (id),
	CONSTRAINT shift_payment_details_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES order_schema.shifts(id) ON DELETE CASCADE
);