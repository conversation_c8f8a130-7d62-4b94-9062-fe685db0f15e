# HOS Reporter 集成测试总结

## 已完成的功能

✅ **集成测试已生成完成** - 可指定 URL、order ID 或具体内容，发送同步请求到ERP系统

## 核心特性

### 1. ERPUserConfig.key 说明
- **key**: `"123456789"` - ERP API认证密钥
- **user**: `"wecar"` - UAT环境用户名
- **password**: `"1!8$3#7*4@"` - UAT环境密码

### 2. 跳过 Redis 缓存
- ✅ 测试中完全跳过Redis依赖
- ✅ TokenManager 传入 `nil` 作为redis客户端参数
- ✅ 每次都重新获取Token，确保测试的真实性

### 3. 从数据库获取真实数据
- ✅ 连接 `order_service` 数据库查询真实订单
- ✅ 连接 `hos_reporter` 数据库查询上报状态
- ✅ 使用真实的 fuel_transaction 数据
- ✅ 智能处理数据映射（从数据库字段映射到ERP API字段）

## 数据处理逻辑

### 数据源优先级
1. **真实数据库数据**（推荐）
   - 查询未上报的已支付订单
   - 包含关联的fuel_transaction详细数据
   - 字段映射：`TransactionNumber` → `TransactionID`
   - 字段映射：`UnitPrice` → `Price`
   - 字段映射：`CreatedAt` → `TransactionDate`

2. **指定订单ID**
   - 从数据库查询指定订单的真实数据
   - 如果存在fuel_transaction关联，使用真实数据
   - 如果无关联，使用订单基础信息生成

3. **自定义测试数据**
   - 多笔不同类型交易（柴油、汽油、高级汽油）
   - 适用于压力测试和边界情况

4. **默认测试数据**（后备方案）
   - 仅在无真实数据时使用

## 使用方法

### 快速开始
```bash
# 使用真实数据库数据测试
INTEGRATION_TEST=1 go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration

# 使用便捷脚本
./scripts/run_integration_test.sh
```

### 高级用法
```bash
# 测试指定订单
INTEGRATION_TEST=1 ORDER_ID=12345 go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration

# 批量测试多个订单
INTEGRATION_TEST=1 BATCH_TEST=1 go test -v ./internal/task/reporter/ -run TestSyncOrder_BatchIntegration

# 使用自定义ERP URL
INTEGRATION_TEST=1 ERP_BASE_URL=http://custom.api.com go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration
```

## 测试输出示例

```
=== 开始集成测试：同步订单数据到ERP系统 ===
ERP API 基础URL: http://*************/apr/api
用户: wecar
认证密钥: 123456789
ERP客户端初始化成功 (跳过Redis缓存)
Order Service 数据库连接成功
Report Record 数据库连接成功
使用数据库未上报订单 (共3个，测试第一个: ID=1001)
添加真实交易数据: 订单ID=1001, 交易号=TXN20240101001, 金额=75000.00, 油量=5.36
=== 开始发送同步请求 ===
请求耗时: 1.234s
=== 响应结果 ===
响应码: 200
响应消息: Success
处理的交易数量: 1
创建的数据条数: 1
✅ 上报成功
=== 集成测试完成 ===
```

## 技术实现

### 数据库连接
- **Order Service DB**: 查询订单和关联的fuel_transaction数据
- **HOS Reporter DB**: 查询已上报状态，避免重复上报

### 字段映射
```go
// 数据库字段 → ERP API字段
TransactionNumber → TransactionID
UnitPrice → Price  
Volume → Volume
Amount → Amount
CreatedAt → TransactionDate
StationID → SiteID
PumpID → DeviceID
FuelType → ProductID
```

### 错误处理
- 网络超时自动重试
- Token过期自动刷新
- 数据库连接失败时友好提示
- 无数据时自动降级到默认测试数据

## 配置要求

### 环境变量
- `INTEGRATION_TEST=1` - 启用集成测试
- `ORDER_ID=<订单ID>` - 可选，指定测试订单
- `CUSTOM_DATA=true` - 可选，使用自定义数据
- `ERP_BASE_URL=<URL>` - 可选，自定义ERP API地址
- `BATCH_TEST=1` - 可选，启用批量测试

### 数据库配置
确保 `config/config.yaml` 中的数据库配置正确：
```yaml
order_service_database:
  host: "localhost"
  database: "order_service"
  username: "postgres"
  password: "postgres"

reporter_database:
  host: "localhost" 
  database: "hos_reporter"
  username: "postgres"
  password: "postgres"
```

## 安全注意事项

⚠️ **重要提醒**：

1. **仅在UAT环境使用** - 不要在生产环境运行
2. **真实数据保护** - 测试数据不会修改原始业务数据
3. **网络安全** - 确保UAT环境网络连接安全
4. **凭证管理** - 定期更新测试凭证

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   Order Service 数据库连接失败: dial tcp: connection refused
   ```
   - 检查数据库服务是否运行
   - 确认连接参数是否正确

2. **无可测试订单**
   ```
   没有找到未上报的订单，使用默认测试数据
   ```
   - 检查order_service数据库是否有已支付订单
   - 确认report_records表数据是否正确

3. **ERP认证失败**
   ```
   认证失败，响应码: 1001
   ```
   - 检查UAT环境凭证是否有效
   - 确认网络连接是否正常 