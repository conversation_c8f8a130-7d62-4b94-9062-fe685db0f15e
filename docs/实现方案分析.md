# ERP数据转换实现方案分析

## 当前架构分析

### 现有问题
1. **性能瓶颈**：10万+数据时，NOT IN查询导致严重性能问题
2. **复杂映射**：跨多个schema的复杂联查（order_schema, core_schema, oil_schema等）
3. **内存占用**：一次性加载大量数据导致内存压力
4. **维护成本**：ent生成的代码量大，关联关系复杂

### 架构现状
```
[ERP字段映射] 
    ↓
[ent ORM] → [Repository] → [UseCase] → [ERP Client]
    ↓
[PostgreSQL多Schema查询]
```

## 方案对比分析

### 方案一：优化现有ent方案

#### 架构设计
```go
// 保持现有架构，优化查询和映射逻辑
type DataConverter struct {
    // 添加映射表
    pumpDispenserMap   map[string]int    // pump_id → dispenser_number
    nozzleNumberMap    map[string]int    // nozzle_id → nozzle_number
    
    // 缓存常用数据
    stationCache       map[int64]*Station
    userCache          map[int64]*User
}
```

#### 优势
- **渐进式改进**：不需要大规模重构
- **保持现有投资**：ent schema定义和代码生成保留
- **类型安全**：继续享受ent的类型安全特性
- **开发效率**：熟悉的开发模式

#### 劣势
- **性能天花板**：ORM层开销依然存在
- **复杂关联**：跨schema查询仍需要复杂的ent关联定义
- **内存占用**：ent实体对象相对较重
- **SQL控制力**：难以精确控制生成的SQL

#### 实现要点
```go
// 1. 添加映射表管理
type MappingManager struct {
    pumpToDispenser  map[string]int
    nozzleToNumber   map[string]int
    fuelTypeToProduct map[string]string
}

// 2. 优化查询策略 - 使用LEFT JOIN替代NOT IN
func (r *OrderRepoImpl) FindPaidNotReportOptimized(ctx context.Context, cursor *PaginationCursor) ([]*order_service.Order, error) {
    return r.client.Order.
        Query().
        Where(
            order.StatusEQ("completed"),
            order.IDGT(cursor.LastOrderID),
        ).
        // 使用子查询替代NOT IN
        Where(func(s *sql.Selector) {
            s.Where(sql.NotExists(
                sql.Select().
                From(sql.Table("report_records")).
                Where(sql.EQ("business_id", s.C("id"))),
            ))
        }).
        WithFuelTransactionLink(func(fql *order_service.FuelTransactionOrderLinkQuery) {
            fql.WithFuelTransaction()
        }).
        Limit(cursor.BatchSize).
        All(ctx)
}

// 3. 分层数据转换
func (dc *DataConverter) ConvertWithMapping(order *order_service.Order) erp.TransactionDataItem {
    item := dc.convertBasicFields(order)
    dc.enrichWithFuelTransaction(&item, order.Edges.FuelTransactionLink.Edges.FuelTransaction)
    dc.enrichWithOperatorInfo(&item, order)
    dc.enrichWithMappings(&item, order)
    return item
}
```

### 方案二：原生SQL + 轻量级映射

#### 架构设计
```go
type SQLDataService struct {
    db           *sql.DB
    queryBuilder *QueryBuilder
    mapper       *FieldMapper
}

type QueryBuilder struct {
    baseQuery    string
    joinClauses  []string
    whereClauses []string
}

type FieldMapper struct {
    pumpMap      map[string]int
    nozzleMap    map[string]int
    stationMap   map[int64]string
}
```

#### 优势
- **极致性能**：直接控制SQL，无ORM开销
- **精确控制**：可以编写最优化的联查SQL
- **内存效率**：只加载需要的字段，轻量级结构体
- **可扩展性**：容易处理复杂的跨schema查询

#### 劣势
- **开发成本**：需要手写大量SQL和映射代码
- **类型安全性**：失去编译时类型检查
- **维护成本**：SQL变更需要手动同步代码
- **错误处理**：需要更多的运行时错误处理

#### 实现要点
```go
// 1. 核心查询构建器
type TransactionQueryBuilder struct {
    db *sql.DB
}

func (qb *TransactionQueryBuilder) BuildOptimizedQuery() string {
    return `
    SELECT 
        o.id as order_id,
        o.order_number,
        o.station_id,
        o.total_amount,
        o.final_amount,
        o.created_at,
        o.staff_card_id,
        
        ft.transaction_number,
        ft.pump_id,
        ft.nozzle_id,
        ft.fuel_type,
        ft.unit_price,
        ft.volume,
        ft.amount,
        ft.start_totalizer,
        ft.end_totalizer,
        ft.nozzle_end_time,
        ft.member_card_id,
        
        sc.user_id,
        u.full_name as operator_name,
        
        s.address as station_address
    FROM order_schema.orders o
    LEFT JOIN order_schema.fuel_transaction_order_links ftol ON o.id = ftol.order_id
    LEFT JOIN order_schema.fuel_transactions ft ON ftol.fuel_transaction_id = ft.id  
    LEFT JOIN order_schema.staff_cards sc ON COALESCE(ft.staff_card_id, o.staff_card_id) = sc.id
    LEFT JOIN core_schema.users u ON sc.user_id = u.id
    LEFT JOIN core_schema.stations s ON o.station_id = s.id
    LEFT JOIN report_records rr ON o.id = rr.business_id AND rr.record_type = 'transaction'
    WHERE o.status = 'completed' 
        AND rr.business_id IS NULL
        AND o.id > $1
    ORDER BY o.id
    LIMIT $2
    `
}

// 2. 轻量级数据结构
type TransactionRecord struct {
    OrderID         int64     `db:"order_id"`
    OrderNumber     string    `db:"order_number"`
    StationID       int64     `db:"station_id"`
    TotalAmount     float64   `db:"total_amount"`
    PumpID          *string   `db:"pump_id"`
    NozzleID        *string   `db:"nozzle_id"`
    FuelType        *string   `db:"fuel_type"`
    UnitPrice       *float64  `db:"unit_price"`
    Volume          *float64  `db:"volume"`
    OperatorName    *string   `db:"operator_name"`
    StationAddress  *string   `db:"station_address"`
    // ... 其他字段
}

// 3. 高效映射器
type FastMapper struct {
    dispenserMap map[string]int
    nozzleMap    map[string]int
    productMap   map[string]string
}

func (m *FastMapper) MapToERPItem(record *TransactionRecord) erp.TransactionDataItem {
    item := erp.TransactionDataItem{
        TransactionID: fmt.Sprintf("TXN%d_%d", record.OrderID, record.OrderID*1000),
        SlipNumber:    fmt.Sprintf("SLIP_%d", record.OrderID),
        Amount:        record.TotalAmount,
        // 使用映射表
        DispenserNumber: m.mapPumpToDispenser(record.PumpID),
        NozzleNumber:    m.mapNozzleToNumber(record.NozzleID),
        ProductID:       m.mapFuelTypeToProduct(record.FuelType),
        OperatorID:      m.getOperatorID(record.OperatorName),
    }
    return item
}
```

### 方案三：混合方案（推荐）

#### 架构设计
```go
// 结合两种方案的优势
type HybridDataService struct {
    // 使用原生SQL做主查询和性能敏感操作
    sqlService   *SQLDataService
    // 使用ent处理复杂业务逻辑和事务
    entService   *EntDataService
    // 统一的映射层
    mapper       *UnifiedMapper
}
```

#### 优势
- **性能与开发效率平衡**：关键路径用SQL，复杂业务用ent
- **渐进式迁移**：可以逐步将性能瓶颈部分迁移到SQL
- **保持类型安全**：业务逻辑部分仍享受ent类型安全
- **灵活应对**：根据具体场景选择最适合的技术

#### 劣势
- **架构复杂性**：需要维护两套数据访问方式
- **学习成本**：团队需要掌握两种技术栈
- **一致性维护**：需要确保两种方式的数据一致性

#### 实现要点
```go
// 1. 统一接口设计
type DataService interface {
    FindPendingTransactions(ctx context.Context, limit int) ([]TransactionData, error)
    ConvertToERPRequest(data []TransactionData) *erp.DispenserTransactionRequest
}

// 2. SQL实现（性能关键路径）
type SQLDataService struct {
    db *sql.DB
}

func (s *SQLDataService) FindPendingTransactions(ctx context.Context, limit int) ([]TransactionData, error) {
    // 使用优化的SQL查询
    query := s.buildOptimizedQuery()
    rows, err := s.db.QueryContext(ctx, query, limit)
    // 直接映射到轻量级结构体
    return s.scanToTransactionData(rows)
}

// 3. Ent实现（复杂业务逻辑）
type EntDataService struct {
    client *order_service.Client
}

func (e *EntDataService) FindPendingTransactions(ctx context.Context, limit int) ([]TransactionData, error) {
    // 使用ent进行复杂查询和业务验证
    orders, err := e.client.Order.Query().
        WithFuelTransactionLink(func(fql *order_service.FuelTransactionOrderLinkQuery) {
            fql.WithFuelTransaction()
        }).
        Where(/* 复杂条件 */).
        All(ctx)
    
    return e.convertToTransactionData(orders), nil
}

// 4. 统一映射层
type UnifiedMapper struct {
    config *MappingConfig
}

type MappingConfig struct {
    PumpDispenserMap   map[string]int    `yaml:"pump_dispenser_mapping"`
    NozzleNumberMap    map[string]int    `yaml:"nozzle_number_mapping"`
    FuelProductMap     map[string]string `yaml:"fuel_product_mapping"`
}
```

## 推荐方案：混合方案 + 配置化映射

### 实施策略

#### 第一阶段：优化现有ent方案
1. **立即优化查询**：使用LEFT JOIN替代NOT IN
2. **添加映射配置**：将硬编码映射移到配置文件
3. **实现分页查询**：避免一次性加载大量数据
4. **添加缓存层**：缓存常用的映射数据

#### 第二阶段：引入SQL优化
1. **识别性能瓶颈**：通过监控找出最慢的查询
2. **SQL化关键查询**：将瓶颈查询改为原生SQL
3. **保持接口一致**：确保调用方无感知切换

#### 第三阶段：完善混合架构
1. **统一配置管理**：映射规则、查询策略等
2. **监控和切换**：支持动态切换查询策略
3. **性能基准测试**：建立完整的性能测试套件

### 配置化映射设计

```yaml
# mapping_config.yaml
field_mappings:
  pump_dispenser:
    "PUMP_001": 1
    "PUMP_002": 2
    # 支持正则表达式
    "PUMP_.*": "extract_number" # 提取pump_id中的数字
  
  nozzle_number:
    "NOZZLE_A01": 1
    "NOZZLE_A02": 2
    # 支持函数映射
    "NOZZLE_.*": "parse_nozzle_number"
  
  fuel_product:
    "PERTAMAX": "PERTAMAX"
    "PERTALITE": "PERTALITE"
    "SOLAR": "DIESEL"

query_strategies:
  pending_transactions:
    strategy: "sql"  # sql | ent | auto
    batch_size: 1000
    cache_ttl: "5m"
  
  order_details:
    strategy: "ent"
    include_edges: ["fuel_transaction_link"]

performance_thresholds:
  query_timeout: "30s"
  memory_limit: "100MB"
  switch_to_sql_threshold: 10000  # 超过1万条记录自动切换到SQL
```

### 实现示例

```go
// 统一的数据转换服务
type TransactionConverter struct {
    sqlService    *SQLDataService
    entService    *EntDataService
    mapper        *ConfigurableMapper
    config        *MappingConfig
}

func (tc *TransactionConverter) ConvertOrderToTransaction(ctx context.Context, orderID int64) (*erp.TransactionDataItem, error) {
    // 根据配置选择查询策略
    if tc.config.UseSQL {
        return tc.convertWithSQL(ctx, orderID)
    }
    return tc.convertWithEnt(ctx, orderID)
}

func (tc *TransactionConverter) convertWithSQL(ctx context.Context, orderID int64) (*erp.TransactionDataItem, error) {
    // 一次性查询所有需要的数据
    record, err := tc.sqlService.GetTransactionRecord(ctx, orderID)
    if err != nil {
        return nil, err
    }
    
    // 使用配置化映射器转换
    return tc.mapper.MapRecord(record), nil
}

// 配置化映射器
type ConfigurableMapper struct {
    config *MappingConfig
}

func (m *ConfigurableMapper) MapPumpToDispenser(pumpID *string) int {
    if pumpID == nil {
        return DefaultDispenserNum
    }
    
    // 检查直接映射
    if dispenser, exists := m.config.PumpDispenserMap[*pumpID]; exists {
        return dispenser
    }
    
    // 检查正则表达式映射
    for pattern, handler := range m.config.PumpPatternMap {
        if matched, _ := regexp.MatchString(pattern, *pumpID); matched {
            return m.handleMapping(handler, *pumpID)
        }
    }
    
    return DefaultDispenserNum
}

func (m *ConfigurableMapper) MapOperatorID(staffCardID *int64, operatorName *string) string {
    // 优先使用员工姓名
    if operatorName != nil && *operatorName != "" {
        return *operatorName
    }
    
    // 后备使用员工卡ID
    if staffCardID != nil {
        return fmt.Sprintf("CARD_%d", *staffCardID)
    }
    
    return DefaultOperatorID
}
```

## 总结

### 推荐实施路径
1. **短期（1-2周）**：优化现有ent方案，解决紧急性能问题
2. **中期（1-2月）**：引入配置化映射，提升维护性
3. **长期（3-6月）**：建立混合架构，根据需要选择最优技术

### 关键成功因素
1. **性能监控**：建立完善的性能监控体系
2. **配置管理**：映射规则可配置、可测试、可回滚
3. **分层设计**：清晰的数据访问层、业务逻辑层、映射层分离
4. **测试覆盖**：完整的单元测试、集成测试、性能测试

这种方案能够平衡性能、开发效率和维护成本，同时为未来的扩展提供良好的基础。 