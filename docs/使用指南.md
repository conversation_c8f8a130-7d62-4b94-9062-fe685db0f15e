# HOS Reporter 使用指南

## 概述

HOS Reporter 是一个定时上报数据到 ERP 系统的后台服务，支持交易数据、库存数据的上报和数据同步。

## 快速开始

### 启动服务
```bash
# 使用默认配置
go run ./cmd/hos-reporter/main.go

# 使用自定义配置
go run ./cmd/hos-reporter/main.go custom_config.yaml
```

### 停止服务
```bash
# 优雅停止
Ctrl+C 或发送 SIGTERM 信号
```

## 业务链路

### 数据上报流程
```
[定时触发] → [查询待上报数据] → [获取ERP Token] → [上报数据] → [更新状态]
     ↓              ↓                    ↓              ↓           ↓
  Scheduler    Repository          TokenManager    ERP API    Repository
```

### 数据同步流程
```
[定时触发] → [同步订单数据] → [同步库存数据] → [更新记录表]
     ↓              ↓                ↓              ↓
  Scheduler    Order Service    Inventory Data   Report Record
```

## 时序图

### 交易数据上报时序
```mermaid
sequenceDiagram
    participant S as Scheduler
    participant U as Usecase
    participant R as Repository
    participant T as TokenManager
    participant E as ERP API
    participant C as Cache

    S->>U: 触发上报任务
    U->>R: FindPending(limit)
    R-->>U: 返回待上报记录
    U->>T: GetToken("transaction")
    T->>C: 检查缓存
    alt Token 存在
        C-->>T: 返回缓存Token
    else Token 不存在
        T->>E: 调用 /ATG/GetToken
        E-->>T: 返回新Token
        T->>C: 缓存Token
    end
    T-->>U: 返回Token
    U->>E: PostTransactions(data)
    E-->>U: 返回响应
    U->>R: UpdateReportingStatus(status)
    R-->>U: 更新完成
    U-->>S: 任务完成
```

### 数据同步时序
```mermaid
sequenceDiagram
    participant S as Scheduler
    participant U as Usecase
    participant OR as OrderRepo
    participant RR as ReportRecordRepo
    participant DB as Database

    S->>U: 触发同步任务
    U->>OR: 查询订单数据
    OR->>DB: 查询订单表
    DB-->>OR: 返回订单数据
    OR-->>U: 返回订单列表
    U->>RR: 同步到report_record表
    RR->>DB: 插入/更新记录
    DB-->>RR: 操作完成
    RR-->>U: 同步完成
    U-->>S: 任务完成
```

## 配置说明

### 核心配置项
```yaml
reporter:
  transaction_interval: 30  # 交易数据上报间隔（秒）
  inventory_interval: 0     # 库存数据上报间隔（秒）
  data_sync_interval: 30    # 数据同步间隔（秒）
  batch_size: 1             # 每次上报的数据批次大小
```

### 数据库配置
```yaml
reporter_database:
  driver: "postgres"
  host: "localhost"
  port: 5432
  database: "hos_reporter"
  username: "postgres"
  password: "postgres"

order_service_database:
  driver: "postgres"
  host: "localhost"
  port: 5432
  database: "order_service"
  username: "postgres"
  password: "postgres"
```

### ERP API 配置
```yaml
erp_api:
  base_url_uat: "http://*************/apr/api"
  base_url_prod: "http://**************/apr/api"
  transaction_user:
    key: "123456789"
    user: "wecar"
    password: "1!8$3#7*4@"
```

## 数据库表结构

### report_records 表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键ID |
| record_type | varchar(255) | 记录类型: transaction, delivery, inventory |
| business_id | bigint | 业务数据ID |
| reporting_status | varchar(50) | 上报状态: pending, success, failed |
| last_reporting_attempt_at | timestamp | 上次尝试上报时间 |
| reporting_retry_count | integer | 重试次数 |
| error_message | text | 错误信息 |
| erp_response_code | varchar(100) | ERP响应码 |
| site_id | varchar(100) | 站点ID |
| priority | integer | 优先级 |

## 错误处理

### 常见错误码
- **200**: 成功
- **210**: 重复数据（视为成功）
- **1001/401**: Token 过期（自动重试）
- **其他**: 业务错误

### 重试机制
- 网络错误自动重试
- Token 过期自动刷新
- 失败记录增加重试计数
- 支持配置最大重试次数

## 监控和日志

### 关键日志
```
启动 HOS Reporter 服务...
配置加载成功
Redis 客户端初始化成功
PostgreSQL 客户端初始化成功
调度器已启动，开始执行定时任务
开始执行交易数据上报任务...
找到 X 条待上报的交易记录
成功上报 X 条交易数据
```

### 状态检查
```go
// 检查调度器运行状态
isRunning := scheduler.IsRunning()
```

## 开发指南

### 生成 Ent 代码
```bash
# 生成 order_service 相关代码
go run -mod=mod entgo.io/ent/cmd/ent generate --target ./internal/ent/order_service ./internal/ent/order_service/schema

# 生成 hos_reporter 相关代码
go run -mod=mod entgo.io/ent/cmd/ent generate --target ./internal/ent/hos_reporter ./internal/ent/hos_reporter/schema
```

### 测试
```bash
# 运行所有测试
go test ./...

# 运行特定测试
go test ./internal/task/reporter/
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置和网络连接
   - 确认数据库服务运行状态

2. **Redis 连接失败**
   - ✅ **自动降级**: 系统会自动切换到内存缓存，不影响服务运行
   - 检查 Redis 配置和网络连接
   - 确认 Redis 服务运行状态
   - 查看日志中的降级提示: "警告: Redis访问失败，降级为内存缓存"

3. **ERP API 调用失败**
   - 检查网络连接和 API 地址
   - 确认用户凭证正确性
   - 查看 Token 是否过期

4. **数据上报失败**
   - 检查业务数据完整性
   - 查看错误日志和重试次数
   - 确认 ERP 系统状态 