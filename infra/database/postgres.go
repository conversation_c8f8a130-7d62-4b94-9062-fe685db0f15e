package database

import (
	"database/sql"
	"fmt"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/hos_reporter"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service"

	"entgo.io/ent/dialect"
	sqldriver "entgo.io/ent/dialect/sql"
	_ "github.com/lib/pq" // PostgreSQL 驱动
)

// NewPostgresClient 创建并返回一个新的 PostgreSQL 数据库客户端 (hos_reporter)
func NewPostgresClient(cfg *config.RpDatabaseConfig) (*hos_reporter.Client, error) {
	// 构建数据源名称 (DSN)
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.Username, cfg.Password, cfg.Database, cfg.SSLMode)

	// 打开数据库连接
	drv, err := sqldriver.Open(dialect.Postgres, dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %w", err)
	}

	// 创建 ent 客户端
	client := hos_reporter.NewClient(hos_reporter.Driver(drv))

	return client, nil
}

// NewOrderServiceClient 创建并返回一个新的 PostgreSQL 数据库客户端 (order_service)
func NewOrderServiceClient(cfg *config.OdDatabaseConfig) (*order_service.Client, error) {
	// 构建数据源名称 (DSN) - 可能需要连接到不同的数据库
	// 这里假设order_service在同一个数据库服务器上，但可能是不同的数据库名
	orderDBName := cfg.Database
	if cfg.Database == "hos_reporter" {
		// 如果配置的是hos_reporter数据库，order_service可能在另一个数据库中
		// 根据实际情况调整，这里暂时使用相同的数据库
		orderDBName = cfg.Database
	}

	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=order_schema",
		cfg.Host, cfg.Port, cfg.Username, cfg.Password, orderDBName, cfg.SSLMode)

	// 打开数据库连接
	drv, err := sqldriver.Open(dialect.Postgres, dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open order service database connection: %w", err)
	}

	// 创建 ent 客户端
	client := order_service.NewClient(order_service.Driver(drv))

	return client, nil
}

// NewOrderServiceSQLDB 创建并返回一个原始的 SQL 数据库连接，用于复杂查询和联查
// 这个连接专门用于 IntegratedDataConverter 的 DatabaseJoinResolver 功能
func NewOrderServiceSQLDB(cfg *config.OdDatabaseConfig) (*sql.DB, error) {

	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s search_path=order_schema",
		cfg.Host, cfg.Port, cfg.Username, cfg.Password, cfg.Database, cfg.SSLMode)

	// 打开数据库连接
	drv, err := sqldriver.Open(dialect.Postgres, dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open order service database connection: %w", err)
	}
	return drv.DB(), nil
}
