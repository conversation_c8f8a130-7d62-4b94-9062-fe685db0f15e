# Redis 键前缀功能

## 概述

为了防止不同服务在共享 Redis 实例时发生键名冲突，`hos-reporter` 服务实现了自动键前缀功能。通过在配置中设置前缀，所有 Redis 操作都会自动为键名添加指定的前缀。

## 配置方法

在 `config/config.yaml` 文件中配置 Redis 前缀：

```yaml
redis:
  address: "localhost:6379"
  password: ""
  db: 0
  prefix: "hos-reporter"  # 设置前缀，建议使用服务名称
```

## 前缀规则

1. **自动添加冒号**：如果前缀不以冒号结尾，系统会自动添加冒号分隔符
   - 配置 `"hos-reporter"` → 实际前缀 `"hos-reporter:"`
   - 配置 `"hos-reporter:"` → 实际前缀 `"hos-reporter:"`

2. **键名格式**：最终存储的键格式为 `{prefix}:{原始键名}`
   - 原始键：`"user:123"`
   - 实际存储：`"hos-reporter:user:123"`

3. **空前缀**：如果不设置前缀或设置为空字符串，则不添加前缀

## 支持的 Redis 命令

前缀功能支持所有常用的 Redis 命令：

### 字符串操作
- `GET`, `SET`, `DEL`, `EXISTS`, `EXPIRE`, `TTL`
- `INCR`, `DECR`, `INCRBY`, `DECRBY`
- `MGET`, `MSET`

### Hash 操作
- `HGET`, `HSET`, `HDEL`, `HEXISTS`
- `HGETALL`, `HKEYS`, `HVALS`, `HLEN`

### List 操作
- `LPUSH`, `RPUSH`, `LPOP`, `RPOP`
- `LLEN`, `LRANGE`, `LINDEX`, `LSET`

### Set 操作
- `SADD`, `SREM`, `SMEMBERS`
- `SCARD`, `SISMEMBER`

### Sorted Set 操作
- `ZADD`, `ZREM`, `ZSCORE`
- `ZRANK`, `ZRANGE`, `ZCARD`

### 查询操作
- `KEYS`, `SCAN`

## 使用示例

### 基本使用

```go
// 创建 Redis 客户端（会自动应用前缀）
client, err := cache.NewRedisClient(&config.Redis)
if err != nil {
    log.Fatal(err)
}

// 正常使用，前缀会自动添加
err = client.Set(ctx, "user:123", "张三", time.Hour).Err()
// 实际存储的键：hos-reporter:user:123

value, err := client.Get(ctx, "user:123").Result()
// 自动查询键：hos-reporter:user:123
```

### Hash 操作

```go
// Hash 操作
err = client.HSet(ctx, "user:profile:123", map[string]interface{}{
    "name":  "张三",
    "email": "<EMAIL>",
}).Err()
// 实际存储的键：hos-reporter:user:profile:123

name, err := client.HGet(ctx, "user:profile:123", "name").Result()
```

### List 操作

```go
// List 操作
err = client.LPush(ctx, "user:orders:123", "order1", "order2").Err()
// 实际存储的键：hos-reporter:user:orders:123

length, err := client.LLen(ctx, "user:orders:123").Result()
```

## 测试和验证

### 运行测试

```bash
cd hos-reporter
go test ./infra/cache -v
```

### 手动验证

使用 Redis CLI 验证前缀是否正确添加：

```bash
# 连接到 Redis
redis-cli

# 查看所有带前缀的键
KEYS hos-reporter:*

# 直接查询带前缀的键
GET hos-reporter:user:123
```

## 注意事项

1. **向后兼容**：如果现有系统中已有数据，添加前缀后将无法访问原有数据
2. **配置一致性**：同一服务的所有实例必须使用相同的前缀配置
3. **前缀选择**：建议使用服务名称作为前缀，确保唯一性
4. **性能影响**：前缀功能对性能的影响微乎其微
5. **调试**：在调试时需要注意实际的键名包含前缀
