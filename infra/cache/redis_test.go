package cache

import (
	"context"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
)

// TestPrefixHook_BasicOperations 测试基本操作的前缀功能
func TestPrefixHook_BasicOperations(t *testing.T) {
	// 创建测试配置
	cfg := &config.RedisConfig{
		Address:  "localhost:6379",
		Password: "",
		DB:       1, // 使用测试数据库
		Prefix:   "test-prefix",
	}

	// 创建 Redis 客户端
	client, err := NewRedisClient(cfg)
	if err != nil {
		t.Skipf("Redis 连接失败，跳过测试: %v", err)
	}
	defer client.Close()

	ctx := context.Background()

	// 清理测试数据
	defer func() {
		// 删除测试键（需要手动添加前缀）
		client.Del(ctx, "test-prefix:test_key")
		client.Del(ctx, "test-prefix:test_hash")
		client.Del(ctx, "test-prefix:test_list")
		client.Del(ctx, "test-prefix:test_set")
	}()

	t.Run("测试 SET/GET 操作", func(t *testing.T) {
		// 设置值（应该自动添加前缀）
		err := client.Set(ctx, "test_key", "test_value", time.Hour).Err()
		if err != nil {
			t.Fatalf("SET 操作失败: %v", err)
		}

		// 获取值（应该自动添加前缀）
		value, err := client.Get(ctx, "test_key").Result()
		if err != nil {
			t.Fatalf("GET 操作失败: %v", err)
		}

		if value != "test_value" {
			t.Errorf("期望值 'test_value'，实际值 '%s'", value)
		}

		// 验证实际存储的键包含前缀
		// 创建一个没有前缀的客户端来验证
		rawClient := redis.NewClient(&redis.Options{
			Addr: cfg.Address,
			DB:   cfg.DB,
		})
		defer rawClient.Close()

		// 直接查询带前缀的键
		rawValue, err := rawClient.Get(ctx, "test-prefix:test_key").Result()
		if err != nil {
			t.Fatalf("直接查询带前缀键失败: %v", err)
		}

		if rawValue != "test_value" {
			t.Errorf("前缀键的值不匹配，期望 'test_value'，实际 '%s'", rawValue)
		}

		// 查询不带前缀的键应该失败
		_, err = rawClient.Get(ctx, "test_key").Result()
		if err != redis.Nil {
			t.Errorf("查询不带前缀的键应该返回 redis.Nil，实际: %v", err)
		}
	})

	t.Run("测试 Hash 操作", func(t *testing.T) {
		// Hash 操作
		err := client.HSet(ctx, "test_hash", "field1", "value1").Err()
		if err != nil {
			t.Fatalf("HSET 操作失败: %v", err)
		}

		value, err := client.HGet(ctx, "test_hash", "field1").Result()
		if err != nil {
			t.Fatalf("HGET 操作失败: %v", err)
		}

		if value != "value1" {
			t.Errorf("期望值 'value1'，实际值 '%s'", value)
		}
	})

	t.Run("测试 List 操作", func(t *testing.T) {
		// List 操作
		err := client.LPush(ctx, "test_list", "item1", "item2").Err()
		if err != nil {
			t.Fatalf("LPUSH 操作失败: %v", err)
		}

		length, err := client.LLen(ctx, "test_list").Result()
		if err != nil {
			t.Fatalf("LLEN 操作失败: %v", err)
		}

		if length != 2 {
			t.Errorf("期望长度 2，实际长度 %d", length)
		}
	})

	t.Run("测试 Set 操作", func(t *testing.T) {
		// Set 操作
		err := client.SAdd(ctx, "test_set", "member1", "member2").Err()
		if err != nil {
			t.Fatalf("SADD 操作失败: %v", err)
		}

		count, err := client.SCard(ctx, "test_set").Result()
		if err != nil {
			t.Fatalf("SCARD 操作失败: %v", err)
		}

		if count != 2 {
			t.Errorf("期望成员数 2，实际成员数 %d", count)
		}
	})
}

// TestPrefixHook_AutoSuffix 测试前缀自动添加冒号
func TestPrefixHook_AutoSuffix(t *testing.T) {
	tests := []struct {
		name           string
		prefix         string
		expectedPrefix string
	}{
		{
			name:           "无冒号后缀",
			prefix:         "hos-reporter",
			expectedPrefix: "hos-reporter:",
		},
		{
			name:           "已有冒号后缀",
			prefix:         "hos-reporter:",
			expectedPrefix: "hos-reporter:",
		},
		{
			name:           "空前缀",
			prefix:         "",
			expectedPrefix: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hook := NewPrefixHook(tt.prefix)
			if hook.prefix != tt.expectedPrefix {
				t.Errorf("期望前缀 '%s'，实际前缀 '%s'", tt.expectedPrefix, hook.prefix)
			}
		})
	}
}

// TestNewRedisClient_WithoutPrefix 测试不使用前缀的情况
func TestNewRedisClient_WithoutPrefix(t *testing.T) {
	cfg := &config.RedisConfig{
		Address:  "localhost:6379",
		Password: "",
		DB:       1,
		Prefix:   "", // 空前缀
	}

	client, err := NewRedisClient(cfg)
	if err != nil {
		t.Skipf("Redis 连接失败，跳过测试: %v", err)
	}
	defer client.Close()

	ctx := context.Background()

	// 测试不使用前缀的情况
	err = client.Set(ctx, "no_prefix_key", "value", time.Hour).Err()
	if err != nil {
		t.Fatalf("SET 操作失败: %v", err)
	}

	value, err := client.Get(ctx, "no_prefix_key").Result()
	if err != nil {
		t.Fatalf("GET 操作失败: %v", err)
	}

	if value != "value" {
		t.Errorf("期望值 'value'，实际值 '%s'", value)
	}

	// 清理
	client.Del(ctx, "no_prefix_key")
}
