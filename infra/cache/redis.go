package cache

import (
	"context"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
)

// PrefixHook Redis 前缀钩子，用于自动为所有键添加前缀
type PrefixHook struct {
	prefix string
}

// NewPrefixHook 创建新的前缀钩子
func NewPrefixHook(prefix string) *PrefixHook {
	if prefix != "" && !strings.HasSuffix(prefix, ":") {
		prefix += ":"
	}
	return &PrefixHook{prefix: prefix}
}

// DialHook 连接钩子（空实现）
func (h *PrefixHook) DialHook(next redis.DialHook) redis.DialHook {
	return next
}

// ProcessHook 处理钩子，为命令添加前缀
func (h *PrefixHook) ProcessHook(next redis.ProcessHook) redis.ProcessHook {
	return func(ctx context.Context, cmd redis.Cmder) error {
		h.addPrefix(cmd)
		return next(ctx, cmd)
	}
}

// ProcessPipelineHook 管道处理钩子，为管道中的命令添加前缀
func (h *PrefixHook) ProcessPipelineHook(next redis.ProcessPipelineHook) redis.ProcessPipelineHook {
	return func(ctx context.Context, cmds []redis.Cmder) error {
		for _, cmd := range cmds {
			h.addPrefix(cmd)
		}
		return next(ctx, cmds)
	}
}

// addPrefix 为命令的键添加前缀
func (h *PrefixHook) addPrefix(cmd redis.Cmder) {
	if h.prefix == "" {
		return
	}

	args := cmd.Args()
	if len(args) < 2 {
		return
	}

	// 获取命令名称
	cmdName := strings.ToUpper(args[0].(string))

	// 根据不同的命令类型处理键
	switch cmdName {
	case "GET", "SET", "DEL", "EXISTS", "EXPIRE", "TTL", "TYPE", "INCR", "DECR", "INCRBY", "DECRBY":
		// 单键命令，第二个参数是键
		if key, ok := args[1].(string); ok && !strings.HasPrefix(key, h.prefix) {
			args[1] = h.prefix + key
		}
	case "MGET", "MSET", "MDEL":
		// 多键命令，从第二个参数开始都是键
		for i := 1; i < len(args); i++ {
			if key, ok := args[i].(string); ok && !strings.HasPrefix(key, h.prefix) {
				args[i] = h.prefix + key
			}
		}
	case "HGET", "HSET", "HDEL", "HEXISTS", "HGETALL", "HKEYS", "HVALS", "HLEN":
		// Hash 命令，第二个参数是键
		if key, ok := args[1].(string); ok && !strings.HasPrefix(key, h.prefix) {
			args[1] = h.prefix + key
		}
	case "LPUSH", "RPUSH", "LPOP", "RPOP", "LLEN", "LRANGE", "LINDEX", "LSET":
		// List 命令，第二个参数是键
		if key, ok := args[1].(string); ok && !strings.HasPrefix(key, h.prefix) {
			args[1] = h.prefix + key
		}
	case "SADD", "SREM", "SMEMBERS", "SCARD", "SISMEMBER":
		// Set 命令，第二个参数是键
		if key, ok := args[1].(string); ok && !strings.HasPrefix(key, h.prefix) {
			args[1] = h.prefix + key
		}
	case "ZADD", "ZREM", "ZSCORE", "ZRANK", "ZRANGE", "ZCARD":
		// Sorted Set 命令，第二个参数是键
		if key, ok := args[1].(string); ok && !strings.HasPrefix(key, h.prefix) {
			args[1] = h.prefix + key
		}
	case "KEYS", "SCAN":
		// 模式匹配命令，需要为模式添加前缀
		if pattern, ok := args[1].(string); ok && !strings.HasPrefix(pattern, h.prefix) {
			args[1] = h.prefix + pattern
		}
	}
}

// NewRedisClient 根据配置创建并返回 Redis 客户端实例
func NewRedisClient(cfg *config.RedisConfig) (*redis.Client, error) {
	// 创建 Redis 客户端
	client := redis.NewClient(&redis.Options{
		Addr:     cfg.Address,
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	// 如果配置了前缀，添加前缀钩子
	if cfg.Prefix != "" {
		client.AddHook(NewPrefixHook(cfg.Prefix))
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := client.Ping(ctx).Err()
	if err != nil {
		return nil, err
	}

	return client, nil
}
