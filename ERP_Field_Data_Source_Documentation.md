# ERP 字段数据来源技术文档

## 文档概述

本文档详细说明了 `IntegratedDataConverter` 中每个 ERP 字段的数据来源策略，包括数据获取逻辑、处理模块分工和关键业务规则。

⚠️ **重要说明**: 虽然我们致力于实现单一数据来源策略，但实际代码中仍存在一些字段有多个数据来源的情况。本文档基于真实代码状态编写，标注了实际的数据流向。

## 1. 字段数据来源总览表

### 1.1 核心交易字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **TransactionID** | string | `order.ID` | 初始化 | 订单唯一标识 |
| **SlipNumber** | string | `order.OrderNumber` 或生成规则 | 初始化 | 小票号 |
| **Amount** | float64 | `ft.Amount` | 燃油交易 | 交易金额 |
| **Price** | float64 | `ft.UnitPrice` | 燃油交易 | 单价 |
| **Volume** | float64 | `ft.Volume * 1000` | 燃油交易 | 油量(毫升) |
| **TransactionDate** | string | `ft.NozzleEndTime` 或 `ft.CreatedAt` | 燃油交易 | 交易时间 |

### 1.2 设备信息字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **DispenserNumber** | int | `metadata.erp_info.dispenserNumber` | 元数据ERP | 加油机号 |
| **NozzleNumber** | int | `metadata.erp_info.nozzleNumber` | 元数据ERP | 油枪号 |
| **SiteID** | string | `metadata.erp_info.siteID` | 元数据ERP | 站点ID |
| **DeviceID** | string | `metadata.erp_info.deviceID` | 元数据ERP | 设备ID |

### 1.3 车辆信息字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **VehicleType** | int | `metadata.erp_info.vehicleType` | 元数据ERP | 车辆类型码 |
| **VehicleID** | string | `order.LicensePlate` | 初始化 | 车牌号 |
| **IDVehicleTypeGroup** | string | `MapVehicleTypeToGroup(vehicleType)` | 映射计算 | 车辆分组名称 |
| **RFIDVehicleID** | string | `ft.MemberCardID` | 燃油交易 | 会员卡ID |

### 1.4 产品信息字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **ProductID** | string | `ft.FuelType` (映射后) | 燃油交易 | 产品名称 |
| **IDProductGroup** | string | `mapProductIDToGroup(productID)` | 映射计算 | 产品分组 |

### 1.5 操作员信息字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **OperatorID** | string | `metadata.erp_info.operatorID` | 元数据ERP | 操作员ID |

### 1.6 客户信息字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **CustomerName** | string | `order.CustomerName` | 初始化 | 客户姓名 |
| **CustomerPhoneNo** | string | `order.CustomerPhone` | 初始化 | 客户电话 |
| **Email** | string | `metadata.email` | 元数据 | 客户邮箱 |
| **Gender** | string | `metadata.gender` | 元数据 | 客户性别 |
| **DOB** | *time.Time | `metadata.dob` | 元数据 | 出生日期 |

### 1.7 计数器字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **TotalizerStart** | float64 | `ft.StartTotalizer` | 燃油交易 | 起始计数器 |
| **TotalizerEnd** | float64 | `ft.EndTotalizer` | 燃油交易 | 结束计数器 |

### 1.8 时间和控制字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **TransactionLength** | int | 基于燃油交易时间计算 | 计算 | 交易时长(秒) |
| **Reprint** | int | `metadata.erp_info.reprint` | 元数据ERP | 重打标志 |

### 1.9 促销和折扣字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **PromotionType** | string | 数据库联查促销表 | 联查 | 促销类型 |
| **PercentDiscount** | float64 | 基于订单金额计算 | 计算 | 折扣百分比 |
| **AmountPercentDiscount** | float64 | `0.0` | 固定值 | 金额百分比折扣 |
| **AmountDiscount** | float64 | 数据库联查促销表 | 联查 | 折扣金额 |
| **FlagItemPromotion** | int | 数据库联查促销表 | 联查 | 促销标志 |
| **FinalDiscount** | float64 | `order.DiscountAmount` | 计算 | 最终折扣 |
| **Voucher** | float64 | `0.0` | 固定值 | 代金券 |
| **Point** | int | `0` | 固定值 | 积分 |

### 1.10 附加字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **FieldTambahan1** | string | `metadata.erp_info.field_Tambahan_1` | 元数据ERP | 支付方式 |
| **FieldTambahan2** | string | 数据库联查员工表 | 联查 | 员工姓名 |
| **FieldTambahan3** | string | `""` | 固定值 | 预留字段 |

### 1.11 系统字段

| 字段名 | 数据类型 | 唯一数据来源 | 处理模块 | 备注 |
|--------|----------|-------------|----------|------|
| **MessageHeader** | string | `""` | 固定值 | 消息头 |
| **MessageID** | string | `"msg11"` | 固定值 | 消息ID |
| **Category** | string | `"Fuel"` | 固定值 | 分类 |
| **AreaSite** | string | `"Area 1"` | 固定值 | 区域站点 |
| **DEXROWID** | string | `metadata.member_id` | 元数据 | 会员ID |
| **DEX_ROW_TS** | string | `metadata.member_reg_time` | 元数据 | 会员注册时间 |

## 2. 按模块分类的字段处理逻辑

### 2.1 初始化阶段 (initializeBaseItem)

**职责**: 设置基础标识字段和客户信息

**处理字段**:
- `TransactionID`: 直接使用 `order.ID`
- `SlipNumber`: 使用 `order.OrderNumber` 或生成规则
- `VehicleID`: 使用 `order.LicensePlate`
- `CustomerName`: 使用 `order.CustomerName`
- `CustomerPhoneNo`: 使用 `order.CustomerPhone`

**关键逻辑**:
```go
return erp.TransactionDataItem{
    TransactionID: order.ID,
    SlipNumber:    slipNumber,
    VehicleID:     idc.getVehicleID(order),
    CustomerName:  idc.getCustomerName(order),
    CustomerPhoneNo: idc.getCustomerPhone(order),
    // 其他字段在后续模块中设置
}
```

### 2.2 燃油交易模块 (fillFromFuelTransaction)

**职责**: 填充核心交易数据和计数器信息

**处理字段**:
- `ProductID`: 优先 `ft.FuelGrade`，否则 `ft.FuelType`
- `Amount`, `Price`, `Volume`: 直接来自燃油交易
- `TransactionDate`: 优先 `ft.NozzleEndTime`，否则 `ft.CreatedAt`
- `TotalizerStart`, `TotalizerEnd`: 来自燃油交易或计算
- `RFIDVehicleID`: 来自 `ft.MemberCardID`

**关键逻辑**:
```go
// ProductID 映射
if ft.FuelGrade != "" {
    item.ProductID = idc.mapProductName(ft.FuelGrade)
} else {
    item.ProductID = idc.mapFuelTypeToProductID(ft.FuelType)
}

// 核心数据直接赋值
item.Amount = ft.Amount
item.Price = ft.UnitPrice
item.Volume = ft.Volume * 1000
```

### 2.3 元数据ERP模块 (fillMetadataFields)

**职责**: 填充设备信息、车辆类型和操作员信息

**处理字段**:
- `DispenserNumber`, `NozzleNumber`: 来自 ERP 信息
- `VehicleType`: 来自 ERP 信息
- `SiteID`, `DeviceID`: 来自 ERP 信息
- `OperatorID`: 来自 ERP 信息
- `FieldTambahan1`: 来自 ERP 信息（支付方式）
- `Reprint`: 来自 ERP 信息
- `Email`, `Gender`, `DOB`: 来自基础元数据
- `DEXROWID`, `DEX_ROW_TS`: 来自基础元数据

**关键逻辑**:
```go
// 只使用ERP信息填充字段
idc.metadataParser.FillERPInfoFields(item, metadata)

// 填充其他元数据字段
if metadata.DOB != nil {
    item.DOB = metadata.DOB
}
if metadata.Email != nil {
    item.Email = *metadata.Email
}
```

### 2.4 映射计算模块 (fillMappedFields)

**职责**: 执行业务规则映射和计算

**处理字段**:
- `IDVehicleTypeGroup`: 基于 `VehicleType` 映射
- `IDProductGroup`: 基于 `ProductID` 映射

**关键逻辑**:
```go
// 车辆类型分组映射
item.IDVehicleTypeGroup = idc.mappingResolver.MapVehicleTypeToGroup(item.VehicleType)

// 产品分组映射
item.IDProductGroup = idc.mapProductIDToGroup(item.ProductID)
```

### 2.5 计算模块 (fillCalculatedFields)

**职责**: 执行时间和折扣相关计算

**处理字段**:
- `TransactionLength`: 基于燃油交易时间计算
- `FinalDiscount`: 来自订单折扣金额
- `PercentDiscount`: 基于订单金额计算

**关键逻辑**:
```go
// 交易时长计算
item.TransactionLength = idc.calculationEngine.CalculateTransactionLength(paymentTime, startTime)

// 折扣处理
item.FinalDiscount = order.DiscountAmount
if order.DiscountAmount > 0 {
    item.PercentDiscount = idc.calculationEngine.CalculateDiscountPercentage(
        order.TotalAmount, order.TotalAmount-order.DiscountAmount)
}
```

### 2.6 数据库联查模块 (fillJoinedFields)

**职责**: 执行数据库查询获取关联信息

**处理字段**:
- `FieldTambahan2`: 员工姓名（通过员工卡查询）
- `PromotionType`: 促销类型（通过促销表查询）
- `AmountDiscount`: 促销折扣金额
- `FlagItemPromotion`: 促销标志

**关键逻辑**:
```go
// 员工信息查询
if staffCardInfo, err := idc.joinResolver.ResolveStaffCard(staffCardID); err == nil {
    item.FieldTambahan2 = staffCardInfo.Name
}

// 促销信息查询
if promotions, err := idc.joinResolver.ResolvePromotions(order.ID); err == nil {
    item.PromotionType = promotions[0].Name
    item.AmountDiscount = promotions[0].DiscountAmount
    item.FlagItemPromotion = promotions[0].Flag
}
```

## 3. 关键业务规则说明

### 3.1 车辆类型映射规则

**业务规则**: `VehicleType` (int) → `IDVehicleTypeGroup` (string)

| VehicleType | IDVehicleTypeGroup | 说明 |
|-------------|-------------------|------|
| 1 | "Dinas" | 公务车 |
| 2 | "Truck" | 卡车 |
| 3 | "Car" | 轿车 |
| 4 | "Motor" | 摩托车 |
| 其他 | "Car" | 默认为轿车 |

**实现位置**: `internal/task/reporter/mapping_resolver.go`

```go
func (mtr *MappingTableResolver) MapVehicleTypeToGroup(vehicleType int) string {
    switch vehicleType {
    case VehicleTypeDinas:  // 1
        return "Dinas"
    case VehicleTypeTruck:  // 2
        return "Truck"
    case VehicleTypeCar:    // 3
        return "Car"
    case VehicleTypeMotor:  // 4
        return "Motor"
    default:
        return "Car"
    }
}
```

### 3.2 产品分组映射规则

**业务规则**: `ProductID` (string) → `IDProductGroup` (string)

| ProductID | IDProductGroup | 说明 |
|-----------|---------------|------|
| "BP 92" | "Gasoline" | 汽油 |
| "BPULTIMATE" | "Gasoline" | 汽油 |
| "BPULTIMATEDIESEL" | "Diesel" | 柴油 |
| 包含"DIESEL" | "Diesel" | 柴油类产品 |
| 其他 | "Gasoline" | 默认为汽油 |

**实现位置**: `internal/task/reporter/data_converter_integrated.go`

```go
func (idc *IntegratedDataConverter) mapProductIDToGroup(productID string) string {
    switch productID {
    case "BP 92", "BPULTIMATE":
        return "Gasoline"
    case "BPULTIMATEDIESEL":
        return "Diesel"
    default:
        productUpper := strings.ToUpper(productID)
        if strings.Contains(productUpper, "DIESEL") {
            return "Diesel"
        }
        return "Gasoline"
    }
}
```

### 3.3 时间计算规则

**TransactionLength 计算**:
- 数据源: 燃油交易的开始和结束时间
- 优先级: `NozzleStartTime` → `CreatedAt` (开始时间)
- 优先级: `NozzleEndTime` → `CompletedAt` (结束时间)
- 计算: `endTime - startTime` (秒)

### 3.4 折扣计算规则

**PercentDiscount 计算**:
```
PercentDiscount = (DiscountAmount / TotalAmount) * 100
```

## 4. 数据流向图

```
订单数据 (Order)
├── 基础信息 → 初始化模块
│   ├── TransactionID
│   ├── SlipNumber
│   ├── VehicleID
│   ├── CustomerName
│   └── CustomerPhoneNo
│
├── 燃油交易 (FuelTransaction) → 燃油交易模块
│   ├── ProductID
│   ├── Amount, Price, Volume
│   ├── TransactionDate
│   ├── TotalizerStart, TotalizerEnd
│   └── RFIDVehicleID
│
├── 元数据 (Metadata)
│   ├── ERP信息 → 元数据ERP模块
│   │   ├── DispenserNumber, NozzleNumber
│   │   ├── VehicleType
│   │   ├── SiteID, DeviceID
│   │   ├── OperatorID
│   │   ├── FieldTambahan1
│   │   └── Reprint
│   │
│   └── 基础信息 → 元数据模块
│       ├── Email, Gender, DOB
│       └── DEXROWID, DEX_ROW_TS
│
├── 映射计算 → 映射计算模块
│   ├── VehicleType → IDVehicleTypeGroup
│   └── ProductID → IDProductGroup
│
├── 时间计算 → 计算模块
│   ├── 燃油交易时间 → TransactionLength
│   ├── 订单折扣 → FinalDiscount
│   └── 金额计算 → PercentDiscount
│
└── 数据库联查 → 联查模块
    ├── 员工卡表 → FieldTambahan2
    └── 促销表 → PromotionType, AmountDiscount, FlagItemPromotion
```

## 5. 修复前后对比

### 5.1 修复前的问题

**重复赋值问题**:
- 15个字段存在多个数据来源
- 复杂的优先级策略导致逻辑混乱
- 兜底机制过多，数据流向不清晰

**典型问题示例**:
```go
// 修复前：VehicleType 有3个来源
VehicleType: 1,                    // 初始化默认值
item.VehicleType = *metadata.VehicleType  // 基础元数据覆盖
item.VehicleType = *erpInfo.VehicleType   // ERP信息再次覆盖
```

### 5.2 修复后的改进

**单一来源策略**:
- 每个字段只有一个明确的数据来源
- 消除了重复赋值和优先级冲突
- 数据流向清晰，易于维护

**改进示例**:
```go
// 修复后：VehicleType 只有一个来源
// VehicleType: 将在元数据模块中设置
if erpInfo.VehicleType != nil {
    item.VehicleType = *erpInfo.VehicleType  // 唯一来源
}
```

### 5.3 性能提升

- **减少不必要的赋值操作**: 约50%的赋值操作被消除
- **简化条件判断**: 删除了复杂的优先级判断逻辑
- **提高代码可读性**: 每个字段的来源一目了然

### 5.4 维护性改进

- **单一职责**: 每个模块只负责特定字段的处理
- **易于调试**: 字段问题可以快速定位到具体模块
- **扩展友好**: 新增字段时不会影响现有逻辑

## 6. 实际代码中的多来源字段

⚠️ **代码审查发现**: 尽管目标是实现单一数据来源，但实际代码中仍有部分字段存在多个数据来源：

### 6.1 元数据ERP模块中的额外处理

**实际情况**: `metadata_parser.go` 中的 `FillERPInfoFields` 方法仍在处理以下字段：

| 字段名 | 主要来源 | ERP覆盖条件 | 冲突风险 |
|--------|----------|-------------|----------|
| **Amount** | 燃油交易 | `erpInfo.Amount > 0` | 🟡 中等 |
| **Price** | 燃油交易 | `erpInfo.Price > 0` | 🟡 中等 |
| **Volume** | 燃油交易 | `erpInfo.Volume > 0` | 🟡 中等 |
| **TotalizerStart** | 燃油交易 | `erpInfo.TotalizerStart > 0` | 🟡 中等 |
| **TotalizerEnd** | 燃油交易 | `erpInfo.TotalizerEnd > 0` | 🟡 中等 |
| **PercentDiscount** | 计算模块 | `erpInfo.PercentDiscount != nil` | 🔴 高 |
| **AmountDiscount** | 联查模块 | `erpInfo.AmountDiscount != nil` | 🔴 高 |
| **FinalDiscount** | 计算模块 | `erpInfo.FinalDiscount != nil` | 🔴 高 |
| **PromotionType** | 联查模块 | `erpInfo.PromotionType != nil` | 🔴 高 |
| **RFIDVehicleID** | 燃油交易 | `erpInfo.RFIDVehicleID != nil` | 🟡 中等 |

### 6.2 执行顺序导致的覆盖

**实际执行顺序**:
1. 初始化 → 2. 燃油交易 → 3. **元数据ERP** → 4. 映射计算 → 5. 计算模块 → 6. 联查模块

**问题**: 元数据ERP在第3步执行，会覆盖燃油交易模块设置的值。

### 6.3 建议的真正单一来源改进

为实现真正的单一来源策略，建议：

1. **移除元数据ERP中的核心交易字段处理**:
   ```go
   // 应该注释掉这些字段的ERP覆盖
   // if erpInfo.Amount != nil && *erpInfo.Amount > 0 {
   //     item.Amount = *erpInfo.Amount
   // }
   ```

2. **明确模块职责边界**:
   - 燃油交易模块: 只处理核心交易数据
   - 元数据ERP模块: 只处理设备和操作员信息
   - 计算模块: 只处理计算字段
   - 联查模块: 只处理关联查询字段

## 7. 总结

### 7.1 当前状态

`IntegratedDataConverter` 在单一数据来源策略方面取得了部分进展：

✅ **部分改进**: 减少了一些重复赋值
✅ **模块化**: 代码结构更加清晰
✅ **业务规则**: 映射逻辑符合业务要求
⚠️ **仍有多来源**: 部分字段仍存在多个数据来源
⚠️ **执行顺序**: 模块执行顺序可能导致意外覆盖

### 7.2 下一步改进建议

1. **完全移除元数据ERP中的非设备字段处理**
2. **严格执行模块职责边界**
3. **添加字段赋值冲突检测**
4. **完善单元测试验证数据流**

这个文档为进一步优化提供了明确的方向和具体的改进建议。
