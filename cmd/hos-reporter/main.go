package main

import (
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/infra/cache"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/infra/database"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/task/reporter"
)

// go run ./cmd/hos-reporter/main.go custom_config.yaml
func main() {
	log.Println("启动 HOS Reporter 服务...")

	// a. 加载配置
	cfg := config.GetConfig()
	log.Println("配置加载成功")

	// b. 初始化日志 (使用标准库的 log，已经初始化)
	log.Println("日志系统已初始化")

	// c. 初始化 Redis 客户端
	redisClient, err := cache.NewRedisClient(&cfg.Redis)
	if err != nil {
		log.Fatalf("Redis 客户端初始化失败: %v", err)
	}
	defer redisClient.Close()
	log.Println("Redis 客户端初始化成功")

	// d. 初始化 reporter PostgreSQL 客户端
	rpDbClient, err := database.NewPostgresClient(&cfg.RpDatabase)
	if err != nil {
		log.Fatalf("PostgreSQL 客户端初始化失败: %v", err)
	}
	defer rpDbClient.Close()
	log.Println("PostgreSQL 客户端初始化成功")

	// d2. 初始化 Order Service 数据库客户端
	odDbClient, err := database.NewOrderServiceClient(&cfg.OdDatabase)
	if err != nil {
		log.Fatalf("Order Service 数据库客户端初始化失败: %v", err)
	}
	defer odDbClient.Close()
	log.Println("Order Service 数据库客户端初始化成功")

	// e. 初始化 erpAdapter (包括 tokenManager 和 erpClient)
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	tokenManager := erp.NewTokenManager(httpClient, &cfg.ERPAPI, redisClient)
	erpClient := erp.NewERPClient(httpClient, tokenManager, &cfg.ERPAPI)
	log.Printf("ERP 适配器初始化成功 (Base URL: %s)", config.GetBaseURL())

	// f. 初始化 repository
	reportRecordRepo := repository.NewReportRecordRepo(rpDbClient)
	orderRepo := repository.NewOrderRepo(odDbClient)
	log.Println("Repository 初始化成功")

	// g. 初始化 reporterUsecase - 使用新的 IntegratedDataConverter 架构
	// 获取 Order Service 的原始 SQL 数据库连接以支持联查功能
	odSQLDB, err := database.NewOrderServiceSQLDB(&cfg.OdDatabase)
	if err != nil {
		log.Fatalf("Order Service SQL 数据库连接初始化失败: %v", err)
	}
	defer odSQLDB.Close()
	log.Println("Order Service SQL 数据库连接初始化成功（用于联查）")

	reporterUsecase := reporter.NewReporterUsecase(orderRepo, reportRecordRepo, erpClient, odSQLDB)
	log.Println("Reporter Usecase 初始化成功 (使用 IntegratedDataConverter 架构)")

	// h. 初始化 scheduler
	scheduler := reporter.NewScheduler(reporterUsecase, &cfg.Reporter)
	log.Println("调度器初始化成功")

	// 启动调度器
	if err := scheduler.Start(); err != nil {
		log.Fatalf("调度器启动失败: %v", err)
	}
	log.Println("调度器已启动，开始执行定时任务")

	// 等待中断信号，优雅关闭
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	log.Println("HOS Reporter 服务运行中... (按 Ctrl+C 停止)")
	<-sigChan

	log.Println("收到停止信号，正在关闭服务...")
	scheduler.Stop()
	log.Println("HOS Reporter 服务已停止")
}
