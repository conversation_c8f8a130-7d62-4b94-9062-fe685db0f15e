<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HOS Reporter 数据转换测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header {
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .header h1 {
            font-size: 2em;
            color: #333;
            margin-bottom: 5px;
        }
        
        .header p {
            color: #666;
            font-size: 0.9em;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-group label {
            font-weight: 500;
            color: #555;
            white-space: nowrap;
        }
        
        .control-group input, .control-group select {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .control-group input:focus, .control-group select:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-info:hover {
            background: #138496;
        }
        
        .orders-section {
            margin-bottom: 30px;
        }

        .status-section {
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
        }

        .status-container {
            background: white;
            border-radius: 4px;
            padding: 15px;
            border: 1px solid #dee2e6;
        }

        .status-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .status-stat {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
            text-align: center;
            min-width: 100px;
        }

        .status-stat.success {
            background: #d4edda;
            color: #155724;
        }

        .status-stat.failed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-stat.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.success {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.failed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .section-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .orders-info {
            font-size: 0.9em;
            color: #666;
            font-weight: normal;
        }
        
        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        
        .pagination-info {
            color: #666;
            font-size: 14px;
        }
        
        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .page-numbers {
            display: flex;
            gap: 5px;
        }
        
        .page-number {
            padding: 6px 12px;
            border: 1px solid #dee2e6;
            background: white;
            color: #495057;
            text-decoration: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .page-number:hover {
            background: #e9ecef;
        }
        
        .page-number.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .page-number.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background: white;
            border: 1px solid #dee2e6;
        }
        
        .orders-table th {
            background: #f8f9fa;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            font-size: 14px;
            cursor: pointer;
            user-select: none;
            position: relative;
        }
        
        .orders-table th:hover {
            background: #e9ecef;
        }
        
        .orders-table th.sortable::after {
            content: ' ↕';
            color: #adb5bd;
            font-size: 12px;
        }
        
        .orders-table th.sort-asc::after {
            content: ' ↑';
            color: #007bff;
        }
        
        .orders-table th.sort-desc::after {
            content: ' ↓';
            color: #007bff;
        }
        
        .orders-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            font-size: 14px;
            vertical-align: middle;
        }
        
        .orders-table tr:hover {
            background: #f8f9fa;
        }
        
        .orders-table tr.selected {
            background: #e3f2fd;
        }
        
        .orders-table tr.selected:hover {
            background: #e1f5fe;
        }
        
        .order-row {
            cursor: pointer;
        }
        
        .order-id {
            font-weight: 600;
            color: #007bff;
        }
        
        .status-badge {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-paid {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .amount {
            font-weight: 600;
            color: #28a745;
        }
        
        .discount-amount {
            font-weight: 600;
            color: #e74c3c;
        }
        
        .promotion-name {
            font-size: 12px;
            color: #6c757d;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
        }
        
        .promotion-name:hover {
            color: #495057;
            text-decoration: underline;
        }
        
        .conversion-section {
            margin-top: 30px;
        }
        
        .conversion-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .result-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            background: white;
            border: 1px solid #dee2e6;
        }
        
        .result-table th {
            background: #f8f9fa;
            color: #495057;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            border-right: 1px solid #dee2e6;
        }
        
        .result-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            vertical-align: top;
            word-break: break-word;
        }
        
        .result-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .field-name {
            font-weight: 600;
            color: #333;
            min-width: 180px;
        }
        
        .field-value {
            font-family: 'Consolas', 'Monaco', monospace;
            max-width: 400px;
            word-wrap: break-word;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            border: 1px solid #f5c6cb;
        }
        
        .upload-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            background: #f8f9fa;
        }
        
        .upload-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        
        .upload-error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 13px;
            border: 1px solid #e9ecef;
        }
        
        details {
            margin-top: 10px;
        }
        
        details summary {
            cursor: pointer;
            padding: 8px;
            background: #e9ecef;
            border-radius: 4px;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 10px;
            }
            
            .control-group {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .orders-table {
                font-size: 12px;
            }
            
            .orders-table th,
            .orders-table td {
                padding: 8px 4px;
            }
            
            .section-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .pagination-container {
                flex-direction: column;
                gap: 10px;
            }
            
            .pagination-controls {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .page-numbers {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .page-number {
                padding: 4px 8px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>HOS Reporter 数据转换测试</h1>
            <p>ERP数据转换结果查看工具</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="limit">每页显示:</label>
                <select id="limit">
                    <option value="10">10</option>
                    <option value="20" selected>20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
            <div class="control-group">
                <label for="stationId">站点ID:</label>
                <input type="number" id="stationId" placeholder="可选">
            </div>
            <div class="control-group">
                <label for="orderIds">订单ID:</label>
                <input type="text" id="orderIds" placeholder="如: 137,338,3">
            </div>
            <div class="control-group">
                <label for="sortBy">排序字段:</label>
                <select id="sortBy">
                    <option value="id">订单ID</option>
                    <option value="station_id">站点ID</option>
                    <option value="total_amount">订单金额</option>
                    <option value="created_at" selected>创建时间</option>
                    <option value="status">订单状态</option>
                </select>
            </div>
            <div class="control-group">
                <label for="sortOrder">排序方式:</label>
                <select id="sortOrder">
                    <option value="desc" selected>降序</option>
                    <option value="asc">升序</option>
                </select>
            </div>
            <div class="control-group">
                <button class="btn" onclick="loadOrders()">查询订单</button>
                <button class="btn" onclick="resetFilters()">重置筛选</button>
            </div>
            <div class="control-group">
                <button class="btn btn-success" onclick="autoUploadRecent()" id="autoUploadBtn">
                    🚀 MVP自动上传最近10条
                </button>
                <button class="btn" onclick="showUploadStatus()" id="statusBtn">
                    📊 查看上传状态
                </button>
            </div>
            <div class="control-group">
                <button class="btn btn-warning" onclick="toggleAutoUpload()" id="timerBtn">
                    ⏰ 启动定时上传
                </button>
                <input type="number" id="intervalInput" value="120" min="10" max="3600"
                       style="width: 80px;" placeholder="秒">
                <button class="btn btn-info" onclick="updateInterval()">
                    ⚙️ 更新间隔
                </button>
                <span id="timerStatus" style="margin-left: 10px; font-weight: bold;">状态: 未知</span>
            </div>
        </div>

        <!-- MVP状态显示区域 -->
        <div class="status-section" id="statusSection" style="display: none;">
            <div class="section-title">
                <span>📊 上传状态监控</span>
                <button class="btn btn-info" onclick="refreshStatus()">刷新状态</button>
            </div>
            <div id="statusContainer" class="status-container">
                <!-- 状态内容将在这里显示 -->
            </div>
        </div>

        <div class="orders-section">
            <div class="section-title">
                <span>订单列表</span>
                <span id="ordersInfo" class="orders-info"></span>
            </div>
            <div id="ordersContainer" class="loading">
                正在加载订单列表...
            </div>
            <div id="paginationContainer" class="pagination-container" style="display: none;">
                <div class="pagination-info">
                    <span id="paginationInfo"></span>
                </div>
                <div class="pagination-controls">
                    <button class="btn" id="prevBtn" onclick="goToPage(currentPage - 1)" disabled>上一页</button>
                    <div id="pageNumbers" class="page-numbers"></div>
                    <button class="btn" id="nextBtn" onclick="goToPage(currentPage + 1)" disabled>下一页</button>
                </div>
            </div>
        </div>
        
        <div class="conversion-section" id="conversionSection" style="display: none;">
            <div class="conversion-header">
                <div class="section-title">ERP转换结果</div>
                <button class="btn btn-success" id="uploadBtn" onclick="uploadToERP()" style="display: none;">
                    上传到ERP
                </button>
            </div>
            <table class="result-table" id="resultTable">
                <thead>
                    <tr>
                        <th>字段名称</th>
                        <th>转换值</th>
                        <th>数据类型</th>
                        <th>字段说明</th>
                    </tr>
                </thead>
                <tbody id="resultTableBody">
                </tbody>
            </table>
            
            <div id="uploadResultSection" style="display: none;">
                <div class="section-title">ERP上传结果</div>
                <div id="uploadResult"></div>
            </div>
        </div>
    </div>

    <script>
        let orders = [];
        let allOrders = [];
        let selectedOrderId = null;
        let currentPage = 1;
        let totalPages = 1;
        let pageSize = 20;
        let currentSort = { field: 'created_at', order: 'desc' };

        async function loadOrders() {
            const limit = document.getElementById('limit').value;
            const stationId = document.getElementById('stationId').value;
            const orderIds = document.getElementById('orderIds').value;
            const sortBy = document.getElementById('sortBy').value;
            const sortOrder = document.getElementById('sortOrder').value;
            
            pageSize = parseInt(limit);
            currentSort = { field: sortBy, order: sortOrder };
            
            const params = new URLSearchParams();
            params.append('limit', '1000'); // 获取更多数据用于前端分页和排序
            if (stationId) params.append('station_id', stationId);
            if (orderIds) params.append('ids', orderIds);
            
            const container = document.getElementById('ordersContainer');
            container.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await fetch(`/api/orders?${params}`);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }
                
                allOrders = data;
                sortAndPaginateOrders();
                displayOrders(orders);
                updateOrdersInfo();
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
                hideOrdersInfo();
            }
        }

        function sortAndPaginateOrders() {
            // 排序
            const sortedOrders = [...allOrders].sort((a, b) => {
                const aValue = a[currentSort.field];
                const bValue = b[currentSort.field];
                
                let comparison = 0;
                if (aValue < bValue) comparison = -1;
                else if (aValue > bValue) comparison = 1;
                
                return currentSort.order === 'desc' ? -comparison : comparison;
            });
            
            // 分页
            totalPages = Math.ceil(sortedOrders.length / pageSize);
            currentPage = Math.min(currentPage, totalPages || 1);
            
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            orders = sortedOrders.slice(startIndex, endIndex);
        }

        function goToPage(page) {
            if (page < 1 || page > totalPages) return;
            currentPage = page;
            sortAndPaginateOrders();
            displayOrders(orders);
            updatePagination();
            // 保持选中状态
            if (selectedOrderId) {
                highlightSelectedOrder();
            }
        }

        function highlightSelectedOrder() {
            const orderRows = document.querySelectorAll('.order-row');
            orderRows.forEach(row => {
                const orderIdCell = row.querySelector('.order-id');
                if (orderIdCell && orderIdCell.textContent.includes(selectedOrderId)) {
                    row.classList.add('selected');
                }
            });
        }

        function resetFilters() {
            document.getElementById('stationId').value = '';
            document.getElementById('orderIds').value = '';
            document.getElementById('sortBy').value = 'created_at';
            document.getElementById('sortOrder').value = 'desc';
            document.getElementById('limit').value = '20';
            currentPage = 1;
            loadOrders();
        }

        function updateOrdersInfo() {
            const ordersInfo = document.getElementById('ordersInfo');
            const stationId = document.getElementById('stationId').value;
            const orderIds = document.getElementById('orderIds').value;
            
            let infoText = `共找到 ${allOrders.length} 个订单`;
            
            // 添加筛选信息
            const filters = [];
            if (stationId) filters.push(`站点ID: ${stationId}`);
            if (orderIds) filters.push(`订单ID: ${orderIds}`);
            
            if (filters.length > 0) {
                infoText += ` (筛选: ${filters.join(', ')})`;
            }
            
            // 添加排序信息
            const sortLabels = {
                'id': '订单ID',
                'station_id': '站点ID',
                'total_amount': '订单金额',
                'created_at': '创建时间',
                'status': '订单状态'
            };
            
            const sortLabel = sortLabels[currentSort.field] || currentSort.field;
            const orderLabel = currentSort.order === 'asc' ? '升序' : '降序';
            infoText += ` | 排序: ${sortLabel} ${orderLabel}`;
            
            ordersInfo.textContent = infoText;
            
            if (allOrders.length > 0 && totalPages > 1) {
                document.getElementById('paginationContainer').style.display = 'flex';
                updatePagination();
            } else {
                document.getElementById('paginationContainer').style.display = 'none';
            }
        }

        function hideOrdersInfo() {
            document.getElementById('ordersInfo').textContent = '';
            document.getElementById('paginationContainer').style.display = 'none';
        }

        function updatePagination() {
            const paginationInfo = document.getElementById('paginationInfo');
            const pageNumbers = document.getElementById('pageNumbers');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            if (!paginationInfo || !pageNumbers || !prevBtn || !nextBtn) return;
            
            // 更新分页信息
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, allOrders.length);
            paginationInfo.textContent = `显示第 ${startIndex}-${endIndex} 条，共 ${allOrders.length} 条`;
            
            // 更新按钮状态
            prevBtn.disabled = currentPage === 1 || totalPages <= 1;
            nextBtn.disabled = currentPage === totalPages || totalPages <= 1;
            
            // 更新页码
            pageNumbers.innerHTML = '';
            
            if (totalPages <= 1) return;
            
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
            
            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('span');
                pageBtn.className = `page-number ${i === currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => goToPage(i);
                pageNumbers.appendChild(pageBtn);
            }
        }

        function sortTable(field) {
            if (currentSort.field === field) {
                currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.order = 'desc';
            }
            
            // 更新UI控件
            document.getElementById('sortBy').value = field;
            document.getElementById('sortOrder').value = currentSort.order;
            
            currentPage = 1;
            sortAndPaginateOrders();
            displayOrders(orders);
            updatePagination();
            // 保持选中状态
            if (selectedOrderId) {
                highlightSelectedOrder();
            }
        }

        function displayOrders(orders) {
            const container = document.getElementById('ordersContainer');
            
            if (orders.length === 0) {
                const hasFilters = document.getElementById('stationId').value || 
                                  document.getElementById('orderIds').value;
                const message = hasFilters ? 
                    '没有找到符合筛选条件的订单，请尝试调整筛选条件' : 
                    '暂无订单数据';
                container.innerHTML = `<div class="error">${message}</div>`;
                return;
            }
            
            const table = document.createElement('table');
            table.className = 'orders-table';
            
            // 表头定义
            const columns = [
                { key: 'id', label: '订单ID', sortable: true },
                { key: 'station_id', label: '站点ID', sortable: true },
                { key: 'total_amount', label: '订单金额', sortable: true },
                { key: 'discount_amount', label: '折扣金额', sortable: true },
                { key: 'promotion_name', label: '优惠名称', sortable: false },
                { key: 'customer_name', label: '客户名称', sortable: false },
                { key: 'status', label: '订单状态', sortable: true },
                { key: 'created_at', label: '创建时间', sortable: true }
            ];
            
            // 创建表头
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            
            columns.forEach(column => {
                const th = document.createElement('th');
                th.textContent = column.label;
                
                if (column.sortable) {
                    th.className = 'sortable';
                    th.onclick = () => sortTable(column.key);
                    
                    // 添加排序指示器
                    if (currentSort.field === column.key) {
                        th.classList.add(`sort-${currentSort.order}`);
                    }
                }
                
                headerRow.appendChild(th);
            });
            
            thead.appendChild(headerRow);
            table.appendChild(thead);
            
            // 创建表体
            const tbody = document.createElement('tbody');
            tbody.id = 'ordersTableBody';
            
            orders.forEach(order => {
                const row = document.createElement('tr');
                row.className = 'order-row';
                row.onclick = () => selectOrder(order.id, row);
                
                const customerName = order.customer_name || '游客';
                const createdAt = new Date(order.created_at).toLocaleString('zh-CN');
                const statusClass = `status-${order.status}`;
                const discountAmount = order.discount_amount || 0;
                const promotionName = order.promotion_name || '无';
                
                row.innerHTML = `
                    <td><span class="order-id">#${order.id}</span></td>
                    <td>${order.station_id}</td>
                    <td><span class="amount">¥${order.total_amount.toFixed(2)}</span></td>
                    <td><span class="discount-amount">¥${discountAmount.toFixed(2)}</span></td>
                    <td><span class="promotion-name">${promotionName}</span></td>
                    <td>${customerName}</td>
                    <td><span class="status-badge ${statusClass}">${order.status}</span></td>
                    <td>${createdAt}</td>
                `;
                
                tbody.appendChild(row);
            });
            
            table.appendChild(tbody);
            container.innerHTML = '';
            container.appendChild(table);
        }

        async function selectOrder(orderId, rowElement) {
            selectedOrderId = orderId;
            
            // 更新选中状态
            document.querySelectorAll('.order-row').forEach(row => {
                row.classList.remove('selected');
            });
            rowElement.classList.add('selected');
            
            // 执行转换
            await convertOrder(orderId);
            
            // 显示上传按钮
            document.getElementById('uploadBtn').style.display = 'inline-block';
        }

        async function uploadToERP() {
            if (!selectedOrderId) {
                alert('请先选择一个订单');
                return;
            }
            
            const uploadBtn = document.getElementById('uploadBtn');
            const uploadResultSection = document.getElementById('uploadResultSection');
            const uploadResult = document.getElementById('uploadResult');
            
            // 禁用按钮并显示加载状态
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            
            // 显示结果区域
            uploadResultSection.style.display = 'block';
            uploadResult.innerHTML = '<div style="text-align: center; padding: 20px;">正在上传到ERP...</div>';
            
            try {
                const response = await fetch(`/api/upload?order_id=${selectedOrderId}`);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || '上传失败');
                }
                
                // 显示上传结果
                displayUploadResult(data);
                
            } catch (error) {
                uploadResult.innerHTML = `
                    <div class="upload-result upload-error">
                        <h4>上传失败</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                // 恢复按钮状态
                uploadBtn.disabled = false;
                uploadBtn.textContent = '上传到ERP';
            }
        }

        function displayUploadResult(data) {
            const uploadResult = document.getElementById('uploadResult');
            
            const resultClass = data.success ? 'upload-success' : 'upload-error';
            const statusText = data.success ? '上传成功' : '上传失败';
            
            uploadResult.innerHTML = `
                <div class="upload-result ${resultClass}">
                    <h4>${statusText}</h4>
                    <p><strong>状态码:</strong> ${data.status_code}</p>
                    <p><strong>状态信息:</strong> ${data.status_text}</p>
                </div>
                
                <div style="margin-top: 15px;">
                    <h4>ERP响应内容</h4>
                    <pre>${data.response_body}</pre>
                </div>
                
                <div style="margin-top: 15px;">
                    <details>
                        <summary>查看请求数据</summary>
                        <pre>${JSON.stringify(data.request_data, null, 2)}</pre>
                    </details>
                </div>
            `;
        }

        function fillResultTable(result) {
            const tableBody = document.getElementById('resultTableBody');
            tableBody.innerHTML = '';
            
            // 字段注释映射
            const fieldComments = {
                'TransactionID': '交易ID，来源：fuel_transaction.transaction_number',
                'SlipNumber': '订单编号，来源：order.order_number',
                'TransactionDate': '交易时间，来源：order_table.transaction.created_at',
                'message_Header': '消息头，固定值null',
                'message_ID': '消息ID，固定值"msg11"',
                'TransactionLength': '交易服务时长（秒），来源：order.payment_time - transaction.created_at',
                'SiteID': '站点ID，来源：order_table.order.site_id',
                'DeviceID': '设备ID，来源：order_table.order.metadata',
                'DispenserNumber': '加油机编号，来源：order_table.transaction (Pump + Nozzle 做映射)',
                'NozzleNumber': '油枪编号，来源：order_table.transaction',
                'area_Site': '站点地址，来源：order_table.transaction.site_id - core_table.sites.address',
                'OperatorID': '员工编号，来源：order_table.transaction',
                'customer_Name': '会员名称，来源：order_table.order.metadata',
                'customer_Phone_No': '会员手机号，来源：order_table.order.metadata',
                'Email': '会员邮箱，来源：order_table.order.metadata (可为null)',
                'Gender': '会员性别，来源：order_table.order.metadata ("male" or "female")',
                'DOB': '会员生日，来源：order_table.order.metadata (DateTime格式，可为null)',
                'VehicleType': '车辆类型，来源：order.metadata (需JSON解析)',
                'VehicleID': '车牌号，来源：order.metadata (需JSON解析)',
                'RFIDVehicleID': 'B2B会员卡ID (通常为Null)',
                'iD_VehicleTypeGroup': '车辆类型组，来源：order.metadata (Car or Motor，需JSON解析)',
                'ProductID': '油品代码，来源：order_table.transaction',
                'iD_ProductGroup': '油品类型组，来源：order_table.transaction.fuel_type - oil_table.oil_product.Category',
                'Category': '交易类型，固定值"fuel"',
                'Amount': '订单金额，来源：order_table.order.net_amount',
                'Price': '交易单价，来源：order_table.transaction.price',
                'Volume': '交易体积，来源：order_table.transaction.volume',
                'TotalizerStart': '起始计数器，来源：order_table.transaction.start_totalizer',
                'TotalizerEnd': '结束计数器，来源：order_table.transaction.end_totalizer',
                'promotion_Type': '优惠名称，来源：order_table.order_promotion.promotion_name',
                'percent_Discount': '百分比折扣，来源：order_table.order_promotion.promotion_type="percent".metadata',
                'amount_Percent_Discount': '百分比折扣金额，来源：order_table.order_promotion.promotion_type="percent".discount_amount',
                'amount_Discount': '金额折扣，来源：order_table.order_promotion.promotion_type="fixed_amount".discount_amount',
                'flag_Item_Promotion': '是否为优惠，0否1是',
                'final_Discount': '订单折扣金额，来源：order_table.order.discount_amount',
                'Voucher': '优惠券，固定值null',
                'Point': '积分，固定值null',
                'field_Tambahan_1': '支付方式名称，来源：payment_table.payment_methods.display_name',
                'field_Tambahan_2': '员工名称，来源：core_table.users.full_name',
                'field_Tambahan_3': 'Voucher code (代金券代码)，固定值null',
                'dex': '会员ID，来源：order_table.order.metadata (可为null)',
                'dex_row_ts': '会员注册时间，来源：order_table.order.metadata (可为null)',
                'Reprint': '是否重印，来源：order_table.metadata (固定值0)'
            };
            
            // 直接显示所有字段，按字母顺序排序
            const allFields = Object.keys(result).sort();
            
            allFields.forEach(field => {
                addFieldRow(field, result[field]);
            });
            
            function addFieldRow(fieldName, value) {
                const row = document.createElement('tr');
                
                // 格式化值显示
                const formatValue = (value) => {
                    if (value === null || value === undefined) return 'null';
                    if (value === '') return '(空字符串)';
                    if (typeof value === 'boolean') return value ? 'true' : 'false';
                    if (typeof value === 'number') return value.toLocaleString();
                    return String(value);
                };
                
                const valueStr = formatValue(value);
                const dataType = value === null || value === undefined ? 'null' : typeof value;
                const comment = fieldComments[fieldName] || '暂无说明';
                
                row.innerHTML = `
                    <td class="field-name">${fieldName}</td>
                    <td class="field-value">${valueStr}</td>
                    <td style="font-style: italic; color: #666;">${dataType}</td>
                    <td style="font-size: 13px; color: #555; line-height: 1.4;">${comment}</td>
                `;
                
                tableBody.appendChild(row);
            }
        }

        async function convertOrder(orderId) {
            const conversionSection = document.getElementById('conversionSection');
            conversionSection.style.display = 'block';
            
            const tableBody = document.getElementById('resultTableBody');
            tableBody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 20px;">正在转换数据...</td></tr>';
            
            try {
                const response = await fetch(`/api/convert?order_id=${orderId}`);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || '转换失败');
                }
                
                // 填充结果表格
                fillResultTable(data.result);
                
            } catch (error) {
                tableBody.innerHTML = `<tr><td colspan="4" style="text-align: center; padding: 20px; color: #dc3545;">错误: ${error.message}</td></tr>`;
            }
        }

        // 页面加载时自动加载订单
        window.onload = () => {
            // 初始化控件值
            document.getElementById('sortBy').value = currentSort.field;
            document.getElementById('sortOrder').value = currentSort.order;
            document.getElementById('limit').value = pageSize.toString();
            
            // 添加Enter键监听
            document.getElementById('stationId').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    loadOrders();
                }
            });
            
            document.getElementById('orderIds').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    loadOrders();
                }
            });
            
            loadOrders();

            // 初始化定时器状态
            updateTimerStatus();
        };

        // MVP功能：自动上传最近10条未上传的交易
        async function autoUploadRecent() {
            const btn = document.getElementById('autoUploadBtn');
            const originalText = btn.textContent;

            try {
                btn.disabled = true;
                btn.textContent = '🔄 正在处理...';

                console.log('开始MVP自动上传...');

                const response = await fetch('/api/auto-upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    console.log('MVP自动上传完成:', result);

                    // 显示结果
                    alert(`MVP自动上传完成！\n\n` +
                          `总订单数: ${result.total_orders}\n` +
                          `需要上传: ${result.pending_count}\n` +
                          `成功: ${result.success_count}\n` +
                          `失败: ${result.failed_count}\n\n` +
                          `点击"查看上传状态"查看详细信息`);

                    // 自动显示状态
                    showUploadStatus();
                } else {
                    console.error('MVP自动上传失败:', result);
                    alert(`MVP自动上传失败: ${result.error || '未知错误'}`);
                }
            } catch (error) {
                console.error('MVP自动上传请求失败:', error);
                alert(`MVP自动上传请求失败: ${error.message}`);
            } finally {
                btn.disabled = false;
                btn.textContent = originalText;
            }
        }

        // 显示上传状态
        async function showUploadStatus() {
            const statusSection = document.getElementById('statusSection');
            const statusContainer = document.getElementById('statusContainer');

            try {
                statusContainer.innerHTML = '<div class="loading">正在加载状态...</div>';
                statusSection.style.display = 'block';

                const response = await fetch('/api/upload-status');
                const result = await response.json();

                if (response.ok) {
                    renderUploadStatus(result);
                } else {
                    statusContainer.innerHTML = `<div class="error">加载状态失败: ${result.error || '未知错误'}</div>`;
                }
            } catch (error) {
                console.error('加载状态失败:', error);
                statusContainer.innerHTML = `<div class="error">加载状态失败: ${error.message}</div>`;
            }
        }

        // 渲染上传状态
        function renderUploadStatus(data) {
            const statusContainer = document.getElementById('statusContainer');
            const stats = data.statistics;
            const statuses = data.statuses;

            let html = `
                <div class="status-stats">
                    <div class="status-stat">
                        <div style="font-size: 18px; font-weight: bold;">${stats.total}</div>
                        <div>总计</div>
                    </div>
                    <div class="status-stat success">
                        <div style="font-size: 18px; font-weight: bold;">${stats.success}</div>
                        <div>成功</div>
                    </div>
                    <div class="status-stat failed">
                        <div style="font-size: 18px; font-weight: bold;">${stats.failed}</div>
                        <div>失败</div>
                    </div>
                    <div class="status-stat pending">
                        <div style="font-size: 18px; font-weight: bold;">${stats.pending}</div>
                        <div>处理中</div>
                    </div>
                </div>
            `;

            if (Object.keys(statuses).length > 0) {
                html += '<div class="status-list">';

                // 按时间排序
                const sortedStatuses = Object.entries(statuses).sort((a, b) =>
                    new Date(b[1].upload_time) - new Date(a[1].upload_time)
                );

                for (const [orderId, status] of sortedStatuses) {
                    const uploadTime = new Date(status.upload_time).toLocaleString('zh-CN');
                    const statusClass = status.status;
                    const statusText = {
                        'success': '✅ 成功',
                        'failed': '❌ 失败',
                        'pending': '🔄 处理中'
                    }[status.status] || status.status;

                    html += `
                        <div class="status-item">
                            <div>
                                <strong>订单: ${orderId}</strong>
                                ${status.response_code ? `(状态码: ${status.response_code})` : ''}
                                ${status.retry_count > 0 ? `(重试: ${status.retry_count}次)` : ''}
                            </div>
                            <div style="text-align: right;">
                                <div class="status-badge ${statusClass}">${statusText}</div>
                                <div style="font-size: 12px; color: #666; margin-top: 2px;">${uploadTime}</div>
                                ${status.last_error ? `<div style="font-size: 11px; color: #dc3545; margin-top: 2px;">${status.last_error}</div>` : ''}
                            </div>
                        </div>
                    `;
                }

                html += '</div>';
            } else {
                html += '<div style="text-align: center; color: #666; padding: 20px;">暂无上传记录</div>';
            }

            statusContainer.innerHTML = html;
        }

        // 刷新状态
        async function refreshStatus() {
            await showUploadStatus();
        }

        // 更新定时器状态
        async function updateTimerStatus() {
            try {
                const response = await fetch('/api/timer/status');
                const result = await response.json();

                const timerBtn = document.getElementById('timerBtn');
                const timerStatus = document.getElementById('timerStatus');
                const intervalInput = document.getElementById('intervalInput');

                if (result.enabled) {
                    timerBtn.textContent = '⏹️ 停止定时上传';
                    timerBtn.className = 'btn btn-warning';
                    timerStatus.textContent = `状态: 运行中 (${result.interval})`;
                    timerStatus.style.color = '#28a745';
                } else {
                    timerBtn.textContent = '⏰ 启动定时上传';
                    timerBtn.className = 'btn btn-success';
                    timerStatus.textContent = '状态: 已停止';
                    timerStatus.style.color = '#dc3545';
                }

                intervalInput.value = result.interval_seconds;
            } catch (error) {
                console.error('获取定时器状态失败:', error);
                document.getElementById('timerStatus').textContent = '状态: 获取失败';
            }
        }

        // 切换定时上传状态
        async function toggleAutoUpload() {
            const timerBtn = document.getElementById('timerBtn');
            const originalText = timerBtn.textContent;

            try {
                timerBtn.disabled = true;
                timerBtn.textContent = '🔄 处理中...';

                // 先获取当前状态
                const statusResponse = await fetch('/api/timer/status');
                const statusResult = await statusResponse.json();

                const endpoint = statusResult.enabled ? '/api/timer/stop' : '/api/timer/start';
                const response = await fetch(endpoint, { method: 'POST' });
                const result = await response.json();

                if (response.ok && result.success) {
                    console.log('定时器状态切换成功:', result);
                    alert(result.message);
                    await updateTimerStatus();
                } else {
                    console.error('定时器状态切换失败:', result);
                    alert(`操作失败: ${result.message || '未知错误'}`);
                }
            } catch (error) {
                console.error('定时器操作失败:', error);
                alert(`操作失败: ${error.message}`);
            } finally {
                timerBtn.disabled = false;
                timerBtn.textContent = originalText;
            }
        }

        // 更新定时器间隔
        async function updateInterval() {
            const intervalInput = document.getElementById('intervalInput');
            const intervalSeconds = parseInt(intervalInput.value);

            if (isNaN(intervalSeconds) || intervalSeconds < 10 || intervalSeconds > 3600) {
                alert('间隔时间必须在10秒到3600秒之间');
                return;
            }

            try {
                const response = await fetch('/api/timer/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        interval_seconds: intervalSeconds
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    console.log('定时器间隔更新成功:', result);
                    alert(`定时器间隔已更新为 ${intervalSeconds} 秒${result.restarted ? '，定时器已重启' : ''}`);
                    await updateTimerStatus();
                } else {
                    console.error('定时器间隔更新失败:', result);
                    alert(`更新失败: ${result.error || '未知错误'}`);
                }
            } catch (error) {
                console.error('定时器间隔更新失败:', error);
                alert(`更新失败: ${error.message}`);
            }
        }

        // 定期刷新定时器状态（每30秒）
        setInterval(updateTimerStatus, 30000);
    </script>
</body>
</html> 