# MVP定时自动上传功能说明

## 🎯 功能概述

这个MVP方案实现了每两分钟（可配置间隔）自动上传最近10条未上传交易到ERP系统的功能。

## 🚀 启动服务

```bash
cd cmd/test-server
go run main.go
```

或者编译后运行：
```bash
go build -o test-server .
./test-server
```

## 🌐 Web界面功能

访问 http://localhost:8082 使用Web界面：

### 手动操作按钮
- **🚀 MVP自动上传最近10条** - 立即执行一次批量上传
- **📊 查看上传状态** - 查看所有订单的上传状态和统计信息

### 定时器控制
- **⏰ 启动定时上传** / **⏹️ 停止定时上传** - 控制定时器开关
- **间隔输入框** - 设置定时间隔（10-3600秒）
- **⚙️ 更新间隔** - 应用新的定时间隔
- **状态显示** - 实时显示定时器状态

## 📡 API接口

### 手动上传
```bash
# 手动触发上传最近10条
curl -X POST http://localhost:8082/api/auto-upload

# 查看上传状态
curl http://localhost:8082/api/upload-status

# 查看特定订单状态
curl "http://localhost:8082/api/upload-status?order_id=ORDER_ID"
```

### 定时器控制
```bash
# 启动定时上传
curl -X POST http://localhost:8082/api/timer/start

# 停止定时上传
curl -X POST http://localhost:8082/api/timer/stop

# 查看定时器状态
curl http://localhost:8082/api/timer/status

# 配置定时间隔（例如设置为5分钟=300秒）
curl -X POST http://localhost:8082/api/timer/config \
  -H "Content-Type: application/json" \
  -d '{"interval_seconds": 300}'
```

## ⚙️ 配置说明

### 默认配置
- **定时间隔**: 2分钟（120秒）
- **批次大小**: 最近10条订单
- **重试次数**: 最多3次
- **成功状态码**: 200, 201
- **自动启动**: 是

### 可配置参数
- **间隔时间**: 10秒 - 3600秒（1小时）
- **启动/停止**: 可随时控制
- **实时更新**: 支持运行时修改间隔

## 🔍 工作流程

1. **定时触发**: 每隔设定时间自动执行
2. **查询订单**: 获取最近10条已完成订单
3. **过滤未上传**: 使用内存缓存判断是否已上传
4. **批量处理**: 逐个转换并上传到ERP
5. **状态更新**: 记录上传结果到内存缓存
6. **日志记录**: 详细的处理日志

## 📊 状态管理

### 上传状态
- **success** - 上传成功（200/201状态码）
- **failed** - 上传失败
- **pending** - 正在处理中

### 缓存信息
- 订单ID
- 上传状态
- 上传时间
- 重试次数
- 错误信息
- 响应状态码

## 🔧 故障排除

### 常见问题

1. **定时器不工作**
   - 检查定时器状态：访问 `/api/timer/status`
   - 查看控制台日志
   - 确认间隔时间设置正确

2. **上传失败**
   - 检查ERP接口URL是否正确
   - 查看错误日志
   - 验证网络连接

3. **重复上传**
   - 内存缓存会防止重复上传
   - 重启服务会清空缓存
   - 可通过状态接口查看缓存状态

### 日志说明

```
⏰ 定时触发自动上传...           # 定时器触发
🔍 定时任务：开始查询最近10条...   # 开始查询
📋 定时任务：查询到 X 条订单      # 查询结果
📤 定时任务：需要上传的订单数量: X # 过滤结果
🔄 定时任务：处理订单 1/X: ID    # 处理进度
✅ 定时任务完成 - 成功: X, 失败: X # 完成统计
```

## 🎯 使用建议

1. **测试环境**: 先在测试环境验证功能
2. **间隔设置**: 根据业务需求调整间隔时间
3. **监控日志**: 定期查看日志确保正常运行
4. **状态检查**: 使用Web界面监控上传状态
5. **错误处理**: 关注失败的订单并手动处理

## 🔄 扩展功能

当前MVP方案可以扩展为：
- 支持更大批次处理
- 数据库持久化状态
- 更复杂的重试策略
- 告警通知机制
- 性能监控指标

## 📞 技术支持

如有问题，请查看：
1. 控制台日志输出
2. Web界面状态显示
3. API接口返回信息
4. 网络连接状态
