package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/infra/cache"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/infra/database"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/task/reporter"
)

// UploadStatus 上传状态
type UploadStatus struct {
	OrderID      string    `json:"order_id"`
	Status       string    `json:"status"` // "success", "failed", "pending"
	UploadTime   time.Time `json:"upload_time"`
	RetryCount   int       `json:"retry_count"`
	LastError    string    `json:"last_error,omitempty"`
	ResponseCode int       `json:"response_code,omitempty"`
}

// MemoryCache 内存缓存
type MemoryCache struct {
	uploadStatus map[string]*UploadStatus
	mutex        sync.RWMutex
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache() *MemoryCache {
	return &MemoryCache{
		uploadStatus: make(map[string]*UploadStatus),
	}
}

// SetUploadStatus 设置上传状态
func (mc *MemoryCache) SetUploadStatus(orderID string, status *UploadStatus) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	mc.uploadStatus[orderID] = status
}

// GetUploadStatus 获取上传状态
func (mc *MemoryCache) GetUploadStatus(orderID string) (*UploadStatus, bool) {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	status, exists := mc.uploadStatus[orderID]
	return status, exists
}

// IsUploaded 检查是否已上传成功
func (mc *MemoryCache) IsUploaded(orderID string) bool {
	status, exists := mc.GetUploadStatus(orderID)
	return exists && status.Status == "success"
}

// checkUploadedInDB 检查订单是否已在数据库中记录为上传成功
func (s *TestServer) checkUploadedInDB(ctx context.Context, orderID string) bool {
	query := `
		SELECT COUNT(*)
		FROM public.report_records
		WHERE business_id = $1
		  AND record_type = 'transaction'
		  AND reporting_status = 'success'
	`

	var count int
	err := s.sqlDB.QueryRowContext(ctx, query, orderID).Scan(&count)
	if err != nil {
		log.Printf("⚠️ 检查订单 %s 数据库状态失败: %v", orderID, err)
		return false
	}

	return count > 0
}

// markUploadedInDB 在数据库中标记订单为已上传
func (s *TestServer) markUploadedInDB(ctx context.Context, orderID string, success bool) error {
	status := "success"
	if !success {
		status = "failed"
	}

	query := `
		INSERT INTO public.report_records
		(business_id, record_type, reporting_status, created_at, updated_at)
		VALUES ($1, 'transaction', $2, NOW(), NOW())
		ON CONFLICT (business_id, record_type)
		DO UPDATE SET
			reporting_status = $2,
			updated_at = NOW()
	`

	_, err := s.sqlDB.ExecContext(ctx, query, orderID, status)
	if err != nil {
		log.Printf("❌ 标记订单 %s 数据库状态失败: %v", orderID, err)
		return err
	}

	log.Printf("📝 订单 %s 已标记为 %s", orderID, status)
	return nil
}

// GetAllStatus 获取所有状态
func (mc *MemoryCache) GetAllStatus() map[string]*UploadStatus {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	result := make(map[string]*UploadStatus)
	for k, v := range mc.uploadStatus {
		result[k] = v
	}
	return result
}

type TestServer struct {
	db        *order_service.Client
	sqlDB     *sql.DB
	converter *reporter.IntegratedDataConverter // 使用新的 IntegratedDataConverter
	erpClient *erp.ERPClient
	config    *config.Config
	cache     *MemoryCache // 内存缓存

	// 定时器相关
	autoUploadEnabled  bool
	autoUploadInterval time.Duration
	stopChan           chan bool
}

type OrderInfo struct {
	ID                string    `json:"id"`
	StationID         int64     `json:"station_id"`
	TotalAmount       float64   `json:"total_amount"`
	DiscountAmount    float64   `json:"discount_amount"`
	CustomerName      *string   `json:"customer_name"`
	CreatedAt         time.Time `json:"created_at"`
	Status            string    `json:"status"`
	PromotionName     *string   `json:"promotion_name"`
	PromotionDiscount *float64  `json:"promotion_discount"`
}

type ConversionResult struct {
	OrderInfo     OrderInfo               `json:"order_info"`
	Result        erp.TransactionDataItem `json:"result"`
	ValidationMsg []string                `json:"validation_messages,omitempty"`
}

func main() {
	// 加载配置 - 使用与 hos-reporter 相同的配置加载方式
	cfg := config.GetConfig()
	log.Println("✅ 配置加载成功")

	// 连接数据库 - 使用 ent 客户端用于基础查询
	entClient, err := database.NewOrderServiceClient(&cfg.OdDatabase)
	if err != nil {
		log.Fatal("连接ent数据库失败:", err)
	}
	defer entClient.Close()

	// 连接数据库 - 使用原始 SQL 连接用于 IntegratedDataConverter 联查
	sqlDB, err := database.NewOrderServiceSQLDB(&cfg.OdDatabase)
	if err != nil {
		log.Fatal("连接SQL数据库失败:", err)
	}
	defer sqlDB.Close()

	// 初始化 IntegratedDataConverter
	converter := reporter.NewIntegratedDataConverter(sqlDB)
	log.Println("✅ IntegratedDataConverter 初始化成功（支持完整联查功能）")

	// 初始化ERP客户端
	httpClient := &http.Client{}

	// 初始化Redis客户端（可选，用于ERP Token管理）
	redisClient, err := cache.NewRedisClient(&cfg.Redis)
	if err != nil {
		log.Printf("⚠️  Redis连接失败，将降级为内存缓存: %v", err)
		redisClient = nil
	}

	// 创建ERP客户端（支持Redis降级为内存缓存）
	tokenManager := erp.NewTokenManager(httpClient, &cfg.ERPAPI, redisClient)
	erpClient := erp.NewERPClient(httpClient, tokenManager, &cfg.ERPAPI)

	if redisClient != nil {
		log.Println("✅ ERP客户端初始化成功（使用Redis缓存）")
	} else {
		log.Println("✅ ERP客户端初始化成功（使用内存缓存）")
	}

	// 初始化内存缓存
	cache := NewMemoryCache()
	log.Printf("✅ MVP内存缓存初始化成功")

	server := &TestServer{
		db:        entClient,
		sqlDB:     sqlDB,
		converter: converter,
		erpClient: erpClient,
		config:    cfg,
		cache:     cache,

		// 定时器配置
		autoUploadEnabled:  true,               // 默认启用自动上传
		autoUploadInterval: 2 * time.Minute,    // 默认2分钟间隔
		stopChan:           make(chan bool, 1), // 停止信号通道
	}

	log.Printf("✅ TestServer初始化完成")
	log.Printf("⏰ 自动上传间隔: %v", server.autoUploadInterval)

	// 设置路由
	log.Printf("🔧 注册路由...")
	http.HandleFunc("/", server.servePage)
	http.HandleFunc("/api/orders", server.getOrders)
	http.HandleFunc("/api/convert", server.convertOrder)
	http.HandleFunc("/api/upload", server.uploadToERP)
	http.HandleFunc("/api/auto-upload", server.autoUploadRecent)  // MVP临时方案
	http.HandleFunc("/api/upload-status", server.getUploadStatus) // 查看上传状态
	http.HandleFunc("/api/timer/start", server.startAutoUpload)   // 启动定时上传
	http.HandleFunc("/api/timer/stop", server.stopAutoUpload)     // 停止定时上传
	http.HandleFunc("/api/timer/status", server.getTimerStatus)   // 查看定时器状态
	http.HandleFunc("/api/timer/config", server.configTimer)      // 配置定时器间隔

	log.Printf("✅ 路由注册完成:")
	log.Printf("   📄 GET  /                       - 主页面")
	log.Printf("   📋 GET  /api/orders             - 查询订单")
	log.Printf("   🔄 POST /api/convert            - 转换订单")
	log.Printf("   📤 POST /api/upload             - 上传到ERP")
	log.Printf("   🚀 POST /api/auto-upload        - MVP自动上传最近10条")
	log.Printf("   📊 GET  /api/upload-status      - 查看上传状态")
	log.Printf("   ⏰ POST /api/timer/start        - 启动定时上传")
	log.Printf("   ⏹️  POST /api/timer/stop         - 停止定时上传")
	log.Printf("   📊 GET  /api/timer/status       - 查看定时器状态")
	log.Printf("   ⚙️  POST /api/timer/config       - 配置定时器间隔")

	log.Printf("🚀 测试服务器启动在 http://0.0.0.0:8082")
	log.Printf("✅ 使用新的 IntegratedDataConverter 架构")
	log.Printf("🌐 允许局域网访问")
	log.Printf("")
	log.Printf("🎯 MVP功能说明:")
	log.Printf("   - 自动查询最近10条已完成订单")
	log.Printf("   - 使用内存缓存跟踪上传状态")
	log.Printf("   - 支持重试机制（最多3次）")
	log.Printf("   - 200/201状态码表示成功，其他为失败")
	log.Printf("   - 可通过Web界面查看实时状态")
	log.Printf("")
	log.Printf("🔗 访问地址: http://localhost:8082")
	log.Printf("📱 移动端访问: http://[你的IP]:8082")
	log.Printf("")

	// 启动定时上传
	if server.autoUploadEnabled {
		go server.runAutoUploadTimer()
		log.Printf("⏰ 定时上传已启动，间隔: %v", server.autoUploadInterval)
	}

	log.Fatal(http.ListenAndServe("0.0.0.0:8082", nil))
}

func (s *TestServer) servePage(w http.ResponseWriter, r *http.Request) {
	htmlContent, err := ioutil.ReadFile("cmd/test-server/index.html")
	if err != nil {
		http.Error(w, "无法读取HTML文件", 500)
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write(htmlContent)
}

func (s *TestServer) getOrders(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	limit := 20
	if l := r.URL.Query().Get("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil {
			limit = parsed
		}
	}

	var stationID *int64
	if s := r.URL.Query().Get("station_id"); s != "" {
		if parsed, err := strconv.ParseInt(s, 10, 64); err == nil {
			stationID = &parsed
		}
	}

	var orderIDs []string
	if ids := r.URL.Query().Get("ids"); ids != "" {
		for _, idStr := range strings.Split(ids, ",") {
			orderIDs = append(orderIDs, idStr)
		}
	}

	query := s.db.Order.Query().Limit(limit)

	if stationID != nil {
		query = query.Where(order.StationIDEQ(*stationID))
	}

	if len(orderIDs) > 0 {
		query = query.Where(order.IDIn(orderIDs...))
	}

	orders, err := query.All(r.Context())
	if err != nil {
		http.Error(w, fmt.Sprintf(`{"error": "查询订单失败: %v"}`, err), 500)
		return
	}

	result := make([]OrderInfo, len(orders))
	for i, orderItem := range orders {
		// 简化实现：暂时不查询详细的优惠信息，只显示总折扣金额
		var promotionName *string
		var promotionDiscount *float64

		// 如果有折扣金额，显示为"折扣优惠"
		if orderItem.DiscountAmount > 0 {
			name := "折扣优惠"
			promotionName = &name
			promotionDiscount = &orderItem.DiscountAmount
		}

		result[i] = OrderInfo{
			ID:                orderItem.ID,
			StationID:         orderItem.StationID,
			TotalAmount:       orderItem.TotalAmount,
			DiscountAmount:    orderItem.DiscountAmount,
			CustomerName:      orderItem.CustomerName,
			CreatedAt:         orderItem.CreatedAt,
			Status:            orderItem.Status,
			PromotionName:     promotionName,
			PromotionDiscount: promotionDiscount,
		}
	}

	json.NewEncoder(w).Encode(result)
}

func (s *TestServer) convertOrder(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	orderIDStr := r.URL.Query().Get("order_id")

	// 获取订单数据（包含关联的 FuelTransaction 数据）
	orderItem, err := s.db.Order.Query().
		Where(order.IDEQ(orderIDStr)).
		WithFuelTransactionLink(func(query *order_service.FuelTransactionOrderLinkQuery) {
			query.WithFuelTransaction()
		}).
		Only(r.Context())
	if err != nil {
		http.Error(w, fmt.Sprintf(`{"error": "查询订单失败: %v"}`, err), 500)
		return
	}

	// 使用 IntegratedDataConverter 进行转换
	transactionItem, err := s.converter.ConvertOrderToTransactionItem(orderItem)

	if err != nil {
		http.Error(w, fmt.Sprintf(`{"error": "转换订单失败: %v"}`, err), 500)
		return
	}

	// 构建 ERP 请求
	cfg := s.config
	erpRequest := &erp.DispenserTransactionRequest{
		Key:              cfg.ERPAPI.TransactionUser.Key,
		User:             cfg.ERPAPI.TransactionUser.User,
		Password:         cfg.ERPAPI.TransactionUser.Password,
		TransactionCount: 1,
		MessageID:        "msg111",
		Transactions:     []erp.TransactionDataItem{transactionItem},
	}

	if len(erpRequest.Transactions) == 0 {
		http.Error(w, `{"error": "转换结果为空"}`, 500)
		return
	}

	// 验证转换结果
	var validationMsgs []string
	if issues := s.converter.ValidateTransactionItem(&transactionItem); len(issues) > 0 {
		validationMsgs = issues
	}

	result := ConversionResult{
		OrderInfo: OrderInfo{
			ID:           orderItem.ID,
			StationID:    orderItem.StationID,
			TotalAmount:  orderItem.TotalAmount,
			CustomerName: orderItem.CustomerName,
			CreatedAt:    orderItem.CreatedAt,
			Status:       orderItem.Status,
		},
		Result:        erpRequest.Transactions[0],
		ValidationMsg: validationMsgs,
	}

	json.NewEncoder(w).Encode(result)
}

func (s *TestServer) uploadToERP(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	orderIDStr := r.URL.Query().Get("order_id")

	// 获取订单数据（包含关联的 FuelTransaction 数据）
	orderItem, err := s.db.Order.Query().
		Where(order.IDEQ(orderIDStr)).
		WithFuelTransactionLink(func(query *order_service.FuelTransactionOrderLinkQuery) {
			query.WithFuelTransaction()
		}).
		Only(r.Context())
	if err != nil {
		http.Error(w, fmt.Sprintf(`{"error": "查询订单失败: %v"}`, err), 500)
		return
	}

	// 使用 IntegratedDataConverter 进行转换
	transactionItem, err := s.converter.ConvertOrderToTransactionItem(orderItem)
	if err != nil {
		http.Error(w, fmt.Sprintf(`{"error": "转换订单失败: %v"}`, err), 500)
		return
	}

	// 构建 ERP 请求
	cfg := s.config
	erpRequest := &erp.DispenserTransactionRequest{
		Key:              cfg.ERPAPI.TransactionUser.Key,
		User:             cfg.ERPAPI.TransactionUser.User,
		Password:         cfg.ERPAPI.TransactionUser.Password,
		TransactionCount: 1,
		MessageID:        "msg11",
		Transactions:     []erp.TransactionDataItem{transactionItem},
	}

	if len(erpRequest.Transactions) == 0 {
		http.Error(w, `{"error": "转换结果为空"}`, 500)
		return
	}

	// 验证转换结果
	var validationMsgs []string
	if issues := s.converter.ValidateTransactionItem(&transactionItem); len(issues) > 0 {
		validationMsgs = issues
		log.Printf("⚠️  转换质量警告: %v", validationMsgs)
	}

	// 调用ERP接口上传
	log.Printf("=== 发送ERP请求 ===")
	log.Printf("请求URL: /ATG/DispenserTransaction")
	log.Printf("请求数据: %+v", erpRequest)

	resp, err := s.erpClient.Post(r.Context(), "/ATG/DispenserTransaction", "transaction", erpRequest)
	if err != nil {
		log.Printf("❌ ERP请求失败: %v", err)
		http.Error(w, fmt.Sprintf(`{"error": "调用ERP接口失败: %v"}`, err), 500)
		return
	}
	defer resp.Body.Close()

	// 读取ERP响应
	responseBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Printf("❌ 读取ERP响应失败: %v", err)
		http.Error(w, fmt.Sprintf(`{"error": "读取ERP响应失败: %v"}`, err), 500)
		return
	}

	log.Printf("=== ERP响应结果 ===")
	log.Printf("状态码: %d", resp.StatusCode)
	log.Printf("状态文本: %s", resp.Status)
	log.Printf("响应体: %s", string(responseBody))

	// 判断上传结果并更新缓存
	isSuccess := resp.StatusCode >= 200 && resp.StatusCode < 300
	status := "failed"
	if isSuccess {
		status = "success"
	}

	// 更新缓存状态
	s.cache.SetUploadStatus(orderIDStr, &UploadStatus{
		OrderID:      orderIDStr,
		Status:       status,
		UploadTime:   time.Now(),
		ResponseCode: resp.StatusCode,
		LastError:    "",
	})

	// 构建响应结果
	result := map[string]interface{}{
		"success":             isSuccess,
		"status_code":         resp.StatusCode,
		"status_text":         resp.Status,
		"response_body":       string(responseBody),
		"request_data":        erpRequest,
		"validation_messages": validationMsgs,
	}

	json.NewEncoder(w).Encode(result)
}

// autoUploadRecent MVP临时方案：自动上传最近10条未上传的交易
func (s *TestServer) autoUploadRecent(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	log.Printf("🚀 === MVP自动上传开始 ===")
	log.Printf("📝 客户端IP: %s", r.RemoteAddr)
	log.Printf("🔍 开始查询最近10条已完成订单...")

	// 1. 查询最近10条已完成的订单
	orders, err := s.db.Order.Query().
		Where(order.StatusEQ("completed")).
		WithFuelTransactionLink(func(query *order_service.FuelTransactionOrderLinkQuery) {
			query.WithFuelTransaction()
		}).
		Order(order_service.Desc(order.FieldCreatedAt)).
		Limit(10).
		All(r.Context())

	if err != nil {
		log.Printf("❌ 查询订单失败: %v", err)
		http.Error(w, fmt.Sprintf(`{"error": "查询订单失败: %v"}`, err), 500)
		return
	}

	log.Printf("📋 查询到 %d 条已完成订单", len(orders))

	// 2. 过滤出未上传的订单
	var pendingOrders []*order_service.Order
	for _, orderItem := range orders {
		if !s.cache.IsUploaded(orderItem.ID) {
			pendingOrders = append(pendingOrders, orderItem)
		}
	}

	log.Printf("📤 需要上传的订单数量: %d", len(pendingOrders))

	if len(pendingOrders) == 0 {
		result := map[string]interface{}{
			"message":       "没有需要上传的订单",
			"total_orders":  len(orders),
			"pending_count": 0,
			"results":       []interface{}{},
		}
		json.NewEncoder(w).Encode(result)
		return
	}

	// 3. 批量上传订单
	batchResult := s.uploadOrdersBatch(r.Context(), pendingOrders)
	successCount := batchResult.SuccessCount
	failedCount := batchResult.FailedCount

	log.Printf("=== MVP自动上传完成 ===")
	log.Printf("✅ 成功: %d, ❌ 失败: %d", successCount, failedCount)

	// 4. 返回结果
	response := map[string]interface{}{
		"message":       fmt.Sprintf("批量上传完成，成功: %d, 失败: %d", successCount, failedCount),
		"total_orders":  len(orders),
		"pending_count": len(pendingOrders),
		"success_count": successCount,
		"failed_count":  failedCount,
		"results":       batchResult.Results,
		"batch_mode":    true,
	}

	json.NewEncoder(w).Encode(response)
}

// uploadSingleOrder 上传单个订单到ERP
func (s *TestServer) uploadSingleOrder(ctx context.Context, orderItem *order_service.Order) map[string]interface{} {
	orderID := orderItem.ID
	log.Printf("🔄 开始处理订单: %s", orderID)

	// 检查是否已经在处理中
	if status, exists := s.cache.GetUploadStatus(orderID); exists && status.Status == "pending" {
		log.Printf("⚠️ 订单 %s 正在处理中，跳过", orderID)
		return map[string]interface{}{
			"order_id": orderID,
			"success":  false,
			"message":  "订单正在处理中",
			"status":   status,
		}
	}

	// 设置为处理中状态
	s.cache.SetUploadStatus(orderID, &UploadStatus{
		OrderID:    orderID,
		Status:     "pending",
		UploadTime: time.Now(),
		RetryCount: 0,
	})

	// 1. 转换订单数据
	transactionItem, err := s.converter.ConvertOrderToTransactionItem(orderItem)
	if err != nil {
		log.Printf("❌ 订单 %s 转换失败: %v", orderID, err)
		s.cache.SetUploadStatus(orderID, &UploadStatus{
			OrderID:    orderID,
			Status:     "failed",
			UploadTime: time.Now(),
			LastError:  fmt.Sprintf("转换失败: %v", err),
		})
		return map[string]interface{}{
			"order_id": orderID,
			"success":  false,
			"message":  fmt.Sprintf("转换失败: %v", err),
		}
	}

	// 2. 构建ERP请求
	cfg := s.config
	erpRequest := &erp.DispenserTransactionRequest{
		Key:              cfg.ERPAPI.TransactionUser.Key,
		User:             cfg.ERPAPI.TransactionUser.User,
		Password:         cfg.ERPAPI.TransactionUser.Password,
		TransactionCount: 1,
		MessageID:        "msg11",
		Transactions:     []erp.TransactionDataItem{transactionItem},
	}

	log.Printf("📦 订单 %s ERP请求构建完成，用户: %s", orderID, cfg.ERPAPI.TransactionUser.User)

	// 3. 调用ERP接口（带重试机制）
	maxRetries := 3
	var lastErr error
	var resp *http.Response

	log.Printf("🌐 订单 %s 开始调用ERP接口: /ATG/DispenserTransaction", orderID)

	for retry := 0; retry < maxRetries; retry++ {
		if retry > 0 {
			log.Printf("🔄 订单 %s 重试第 %d 次", orderID, retry)
			time.Sleep(time.Duration(retry) * time.Second) // 递增延迟
		}

		log.Printf("📡 订单 %s 第 %d 次尝试调用ERP...", orderID, retry+1)
		resp, lastErr = s.erpClient.Post(ctx, "/ATG/DispenserTransaction", "transaction", erpRequest)
		if lastErr == nil {
			log.Printf("✅ 订单 %s ERP调用成功", orderID)
			break
		}

		log.Printf("⚠️ 订单 %s 第 %d 次尝试失败: %v", orderID, retry+1, lastErr)
	}

	if lastErr != nil {
		log.Printf("❌ 订单 %s 上传失败，已重试 %d 次: %v", orderID, maxRetries, lastErr)
		s.cache.SetUploadStatus(orderID, &UploadStatus{
			OrderID:    orderID,
			Status:     "failed",
			UploadTime: time.Now(),
			RetryCount: maxRetries,
			LastError:  fmt.Sprintf("网络错误: %v", lastErr),
		})
		return map[string]interface{}{
			"order_id": orderID,
			"success":  false,
			"message":  fmt.Sprintf("网络错误: %v", lastErr),
		}
	}
	defer resp.Body.Close()

	// 4. 读取响应
	responseBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Printf("❌ 订单 %s 读取响应失败: %v", orderID, err)
		s.cache.SetUploadStatus(orderID, &UploadStatus{
			OrderID:      orderID,
			Status:       "failed",
			UploadTime:   time.Now(),
			LastError:    fmt.Sprintf("读取响应失败: %v", err),
			ResponseCode: resp.StatusCode,
		})
		return map[string]interface{}{
			"order_id": orderID,
			"success":  false,
			"message":  fmt.Sprintf("读取响应失败: %v", err),
		}
	}

	// 5. 判断上传结果
	isSuccess := resp.StatusCode == 200 || resp.StatusCode == 201
	status := "failed"
	if isSuccess {
		status = "success"
	}

	log.Printf("📤 订单 %s 上传结果: 状态码=%d, 成功=%v", orderID, resp.StatusCode, isSuccess)
	log.Printf("📄 响应内容: %s", string(responseBody))

	// 6. 更新缓存状态
	s.cache.SetUploadStatus(orderID, &UploadStatus{
		OrderID:      orderID,
		Status:       status,
		UploadTime:   time.Now(),
		ResponseCode: resp.StatusCode,
		LastError:    "",
	})

	return map[string]interface{}{
		"order_id":      orderID,
		"success":       isSuccess,
		"status_code":   resp.StatusCode,
		"response_body": string(responseBody),
		"message":       fmt.Sprintf("状态码: %d", resp.StatusCode),
	}
}

// getUploadStatus 获取上传状态
func (s *TestServer) getUploadStatus(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	orderID := r.URL.Query().Get("order_id")

	if orderID != "" {
		// 查询特定订单的状态
		if status, exists := s.cache.GetUploadStatus(orderID); exists {
			json.NewEncoder(w).Encode(map[string]interface{}{
				"order_id": orderID,
				"status":   status,
				"found":    true,
			})
		} else {
			json.NewEncoder(w).Encode(map[string]interface{}{
				"order_id": orderID,
				"status":   nil,
				"found":    false,
				"message":  "订单状态未找到",
			})
		}
		return
	}

	// 返回所有状态
	allStatus := s.cache.GetAllStatus()

	// 统计信息
	stats := map[string]int{
		"total":   len(allStatus),
		"success": 0,
		"failed":  0,
		"pending": 0,
	}

	for _, status := range allStatus {
		stats[status.Status]++
	}

	response := map[string]interface{}{
		"statistics": stats,
		"statuses":   allStatus,
		"message":    fmt.Sprintf("共有 %d 个订单状态记录", len(allStatus)),
	}

	json.NewEncoder(w).Encode(response)
}

// BatchUploadResult 批量上传结果
type BatchUploadResult struct {
	TotalCount   int                      `json:"total_count"`
	SuccessCount int                      `json:"success_count"`
	FailedCount  int                      `json:"failed_count"`
	Results      []map[string]interface{} `json:"results"`
}

// uploadOrdersBatch 批量上传订单（放到同一个ERP请求中）
func (s *TestServer) uploadOrdersBatch(ctx context.Context, orders []*order_service.Order) *BatchUploadResult {
	result := &BatchUploadResult{
		TotalCount: len(orders),
		Results:    make([]map[string]interface{}, 0),
	}

	if len(orders) == 0 {
		return result
	}

	log.Printf("📦 开始批量上传 %d 个订单", len(orders))

	// 1. 批量转换所有订单
	var transactions []erp.TransactionDataItem
	var orderIDs []string
	var failedConversions []string

	for _, orderItem := range orders {
		orderID := orderItem.ID
		orderIDs = append(orderIDs, orderID)

		// 设置为处理中状态
		s.cache.SetUploadStatus(orderID, &UploadStatus{
			OrderID:    orderID,
			Status:     "pending",
			UploadTime: time.Now(),
			RetryCount: 0,
		})

		// 转换订单数据
		transactionItem, err := s.converter.ConvertOrderToTransactionItem(orderItem)
		if err != nil {
			log.Printf("❌ 订单 %s 转换失败: %v", orderID, err)
			failedConversions = append(failedConversions, orderID)

			s.cache.SetUploadStatus(orderID, &UploadStatus{
				OrderID:    orderID,
				Status:     "failed",
				UploadTime: time.Now(),
				LastError:  fmt.Sprintf("转换失败: %v", err),
			})
			continue
		}

		transactions = append(transactions, transactionItem)
	}

	log.Printf("📋 转换完成 - 成功: %d, 失败: %d", len(transactions), len(failedConversions))

	if len(transactions) == 0 {
		result.FailedCount = len(orders)
		return result
	}

	// 2. 构建批量ERP请求
	cfg := s.config
	erpRequest := &erp.DispenserTransactionRequest{
		Key:              cfg.ERPAPI.TransactionUser.Key,
		User:             cfg.ERPAPI.TransactionUser.User,
		Password:         cfg.ERPAPI.TransactionUser.Password,
		TransactionCount: len(transactions),
		MessageID:        "msg11",
		Transactions:     transactions,
	}

	log.Printf("🌐 发送批量ERP请求，包含 %d 笔交易", len(transactions))

	// 3. 调用ERP接口（带重试机制）
	maxRetries := 3
	var lastErr error
	var resp *http.Response

	for retry := 0; retry < maxRetries; retry++ {
		if retry > 0 {
			log.Printf("🔄 批量请求重试第 %d 次", retry)
			time.Sleep(time.Duration(retry) * time.Second)
		}

		log.Printf("📡 第 %d 次尝试调用ERP批量接口...", retry+1)
		resp, lastErr = s.erpClient.Post(ctx, "/ATG/DispenserTransaction", "transaction", erpRequest)
		if lastErr == nil {
			log.Printf("✅ ERP批量调用成功")
			break
		}

		log.Printf("⚠️ 第 %d 次尝试失败: %v", retry+1, lastErr)
	}

	// 4. 处理响应结果
	if lastErr != nil {
		log.Printf("❌ 批量上传失败，已重试 %d 次: %v", maxRetries, lastErr)

		// 所有订单标记为失败
		for _, orderID := range orderIDs {
			if !contains(failedConversions, orderID) {
				s.cache.SetUploadStatus(orderID, &UploadStatus{
					OrderID:    orderID,
					Status:     "failed",
					UploadTime: time.Now(),
					RetryCount: maxRetries,
					LastError:  fmt.Sprintf("网络错误: %v", lastErr),
				})
			}
		}

		result.FailedCount = len(orders)
		return result
	}
	defer resp.Body.Close()

	// 5. 读取响应
	responseBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Printf("❌ 读取批量响应失败: %v", err)
		result.FailedCount = len(orders)
		return result
	}

	// 6. 判断批量上传结果
	isSuccess := resp.StatusCode == 200 || resp.StatusCode == 201
	status := "failed"
	if isSuccess {
		status = "success"
	}

	log.Printf("📤 批量上传结果: 状态码=%d, 成功=%v", resp.StatusCode, isSuccess)
	log.Printf("📄 响应内容: %s", string(responseBody))

	// 7. 更新所有订单的缓存状态
	for _, orderID := range orderIDs {
		if !contains(failedConversions, orderID) {
			s.cache.SetUploadStatus(orderID, &UploadStatus{
				OrderID:      orderID,
				Status:       status,
				UploadTime:   time.Now(),
				ResponseCode: resp.StatusCode,
				LastError:    "",
			})

			if isSuccess {
				result.SuccessCount++
			} else {
				result.FailedCount++
			}
		}
	}

	// 添加转换失败的数量
	result.FailedCount += len(failedConversions)

	return result
}

// contains 辅助函数：检查字符串是否在切片中
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// runAutoUploadTimer 运行定时上传任务
func (s *TestServer) runAutoUploadTimer() {
	log.Printf("⏰ 定时上传器启动，间隔: %v", s.autoUploadInterval)

	ticker := time.NewTicker(s.autoUploadInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if s.autoUploadEnabled {
				log.Printf("⏰ 定时触发自动上传...")
				s.performAutoUpload()
			}
		case <-s.stopChan:
			log.Printf("⏹️ 定时上传器已停止")
			return
		}
	}
}

// performAutoUpload 执行自动上传（内部方法）
func (s *TestServer) performAutoUpload() {
	ctx := context.Background()

	log.Printf("🔍 定时任务：开始查询最近10条已完成订单...")

	// 查询最近10条已完成的订单
	orders, err := s.db.Order.Query().
		Where(order.StatusEQ("completed")).
		WithFuelTransactionLink(func(query *order_service.FuelTransactionOrderLinkQuery) {
			query.WithFuelTransaction()
		}).
		Order(order_service.Desc(order.FieldCreatedAt)).
		Limit(10).
		All(ctx)

	if err != nil {
		log.Printf("❌ 定时任务：查询订单失败: %v", err)
		return
	}

	log.Printf("📋 定时任务：查询到 %d 条已完成订单", len(orders))

	// 过滤出未上传的订单
	var pendingOrders []*order_service.Order
	for _, orderItem := range orders {
		if !s.cache.IsUploaded(orderItem.ID) {
			pendingOrders = append(pendingOrders, orderItem)
		}
	}

	log.Printf("📤 定时任务：需要上传的订单数量: %d", len(pendingOrders))

	if len(pendingOrders) == 0 {
		log.Printf("✅ 定时任务：没有需要上传的订单")
		return
	}

	// 批量上传订单（放到同一个请求中）
	result := s.uploadOrdersBatch(ctx, pendingOrders)

	log.Printf("✅ 定时任务完成 - 成功: %d, 失败: %d", result.SuccessCount, result.FailedCount)
}

// startAutoUpload 启动定时上传
func (s *TestServer) startAutoUpload(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if s.autoUploadEnabled {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success":  false,
			"message":  "定时上传已经在运行中",
			"status":   "running",
			"interval": s.autoUploadInterval.String(),
		})
		return
	}

	s.autoUploadEnabled = true
	go s.runAutoUploadTimer()

	log.Printf("⏰ 定时上传已启动，间隔: %v", s.autoUploadInterval)

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":  true,
		"message":  "定时上传已启动",
		"status":   "running",
		"interval": s.autoUploadInterval.String(),
	})
}

// stopAutoUpload 停止定时上传
func (s *TestServer) stopAutoUpload(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if !s.autoUploadEnabled {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": false,
			"message": "定时上传未在运行",
			"status":  "stopped",
		})
		return
	}

	s.autoUploadEnabled = false
	select {
	case s.stopChan <- true:
	default:
	}

	log.Printf("⏹️ 定时上传已停止")

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "定时上传已停止",
		"status":  "stopped",
	})
}

// getTimerStatus 获取定时器状态
func (s *TestServer) getTimerStatus(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	status := "stopped"
	if s.autoUploadEnabled {
		status = "running"
	}

	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":           status,
		"interval":         s.autoUploadInterval.String(),
		"enabled":          s.autoUploadEnabled,
		"interval_seconds": int(s.autoUploadInterval.Seconds()),
	})
}

// configTimer 配置定时器间隔
func (s *TestServer) configTimer(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "POST" {
		http.Error(w, "只支持POST方法", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		IntervalSeconds int `json:"interval_seconds"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, fmt.Sprintf("解析请求失败: %v", err), http.StatusBadRequest)
		return
	}

	if request.IntervalSeconds < 10 {
		http.Error(w, "间隔时间不能少于10秒", http.StatusBadRequest)
		return
	}

	if request.IntervalSeconds > 3600 {
		http.Error(w, "间隔时间不能超过1小时", http.StatusBadRequest)
		return
	}

	oldInterval := s.autoUploadInterval
	s.autoUploadInterval = time.Duration(request.IntervalSeconds) * time.Second

	// 如果定时器正在运行，需要重启
	wasRunning := s.autoUploadEnabled
	if wasRunning {
		s.autoUploadEnabled = false
		select {
		case s.stopChan <- true:
		default:
		}
		time.Sleep(100 * time.Millisecond) // 等待停止

		s.autoUploadEnabled = true
		go s.runAutoUploadTimer()
	}

	log.Printf("⚙️ 定时器间隔已更新: %v -> %v", oldInterval, s.autoUploadInterval)

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":          true,
		"message":          "定时器间隔已更新",
		"old_interval":     oldInterval.String(),
		"new_interval":     s.autoUploadInterval.String(),
		"interval_seconds": request.IntervalSeconds,
		"restarted":        wasRunning,
	})
}
