package sql

import (
	"database/sql"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestQueryBuilder_BuildPendingTransactionsQuery(t *testing.T) {
	qb := NewQueryBuilder(nil) // 只测试查询构建，不需要真实DB

	query := qb.BuildPendingTransactionsQuery()

	// 验证查询结构
	assert.Contains(t, query, "FROM order_schema.orders o")
	assert.Contains(t, query, "LEFT JOIN order_schema.fuel_transaction_order_links ftol")
	assert.Contains(t, query, "LEFT JOIN order_schema.fuel_transactions ft")
	assert.Contains(t, query, "LEFT JOIN core_schema.users u")
	assert.Contains(t, query, "LEFT JOIN core_schema.stations s")
	assert.Contains(t, query, "LEFT JOIN report_records rr")

	// 验证关键的性能优化条件
	assert.Contains(t, query, "rr.business_id IS NULL") // 核心优化：使用IS NULL而不是NOT IN
	assert.Contains(t, query, "o.status = 'completed'") // 状态过滤
	assert.Contains(t, query, "o.id > $1")              // 游标分页
	assert.Contains(t, query, "ORDER BY o.id")          // 排序保证
	assert.Contains(t, query, "LIMIT $2")               // 分页限制

	// 验证字段选择
	assert.Contains(t, query, "o.id as order_id")
	assert.Contains(t, query, "ft.transaction_number")
	assert.Contains(t, query, "u.full_name as operator_name")
	assert.Contains(t, query, "s.address as station_address")
}

func TestQueryBuilder_BuildTransactionByIDsQuery(t *testing.T) {
	qb := NewQueryBuilder(nil)

	tests := []struct {
		name      string
		orderIDs  []int64
		wantQuery bool
		wantArgs  int
	}{
		{
			name:      "empty order IDs",
			orderIDs:  []int64{},
			wantQuery: false,
			wantArgs:  0,
		},
		{
			name:      "single order ID",
			orderIDs:  []int64{123},
			wantQuery: true,
			wantArgs:  1,
		},
		{
			name:      "multiple order IDs",
			orderIDs:  []int64{123, 456, 789},
			wantQuery: true,
			wantArgs:  3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			query, args := qb.BuildTransactionByIDsQuery(tt.orderIDs)

			if !tt.wantQuery {
				assert.Empty(t, query)
				assert.Nil(t, args)
				return
			}

			// 验证查询结构
			assert.NotEmpty(t, query)
			assert.Len(t, args, tt.wantArgs)

			// 验证JOIN结构
			assert.Contains(t, query, "FROM order_schema.orders o")
			assert.Contains(t, query, "LEFT JOIN order_schema.fuel_transactions ft")
			assert.Contains(t, query, "LEFT JOIN payment_schema.payment_methods pm")

			// 验证IN条件
			assert.Contains(t, query, "WHERE o.id IN (")

			// 验证占位符数量
			placeholderCount := strings.Count(query, "$")
			assert.Equal(t, tt.wantArgs, placeholderCount)

			// 验证参数值
			for i, expectedID := range tt.orderIDs {
				assert.Equal(t, expectedID, args[i])
			}
		})
	}
}

func TestQueryBuilder_BuildPromotionInfoQuery(t *testing.T) {
	qb := NewQueryBuilder(nil)

	tests := []struct {
		name      string
		orderIDs  []int64
		wantQuery bool
	}{
		{
			name:      "empty order IDs",
			orderIDs:  []int64{},
			wantQuery: false,
		},
		{
			name:      "with order IDs",
			orderIDs:  []int64{123, 456},
			wantQuery: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			query, args := qb.BuildPromotionInfoQuery(tt.orderIDs)

			if !tt.wantQuery {
				assert.Empty(t, query)
				assert.Nil(t, args)
				return
			}

			// 验证查询结构
			assert.NotEmpty(t, query)
			assert.Len(t, args, len(tt.orderIDs))

			// 验证查询内容
			assert.Contains(t, query, "FROM order_schema.order_promotions op")
			assert.Contains(t, query, "WHERE op.order_id IN (")
			assert.Contains(t, query, "ORDER BY op.order_id, op.id")

			// 验证字段选择
			assert.Contains(t, query, "op.order_id")
			assert.Contains(t, query, "op.promotion_name")
			assert.Contains(t, query, "op.promotion_type")
			assert.Contains(t, query, "op.discount_amount as promotion_discount")
		})
	}
}

func TestNewQueryBuilder(t *testing.T) {
	// 模拟数据库连接（在真实测试中会使用测试数据库）
	var mockDB *sql.DB

	qb := NewQueryBuilder(mockDB)

	assert.NotNil(t, qb)
	assert.Equal(t, mockDB, qb.db)
}

// 测试SQL查询的语法正确性
func TestQuerySyntaxValidation(t *testing.T) {
	qb := NewQueryBuilder(nil)

	tests := []struct {
		name        string
		queryFunc   func() string
		description string
	}{
		{
			name:        "pending transactions query",
			queryFunc:   qb.BuildPendingTransactionsQuery,
			description: "待上报交易查询SQL语法",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			query := tt.queryFunc()

			// 基本语法检查
			assert.NotEmpty(t, query, "查询不能为空")

			// 检查SQL关键字配对
			selectCount := strings.Count(strings.ToUpper(query), "SELECT")
			fromCount := strings.Count(strings.ToUpper(query), "FROM")
			assert.Equal(t, selectCount, fromCount, "SELECT和FROM数量应该匹配")

			// 检查JOIN语法
			leftJoinCount := strings.Count(strings.ToUpper(query), "LEFT JOIN")
			assert.GreaterOrEqual(t, leftJoinCount, 1, "应该包含LEFT JOIN")

			// 检查WHERE条件
			assert.Contains(t, strings.ToUpper(query), "WHERE", "应该包含WHERE条件")

			// 检查占位符格式
			placeholders := []string{"$1", "$2"}
			for _, ph := range placeholders {
				if strings.Contains(query, ph) {
					// 如果包含占位符，检查格式正确性
					assert.True(t, strings.Contains(query, ph), "占位符格式应该正确")
				}
			}
		})
	}
}

// 测试查询性能相关的SQL特性
func TestQueryPerformanceFeatures(t *testing.T) {
	qb := NewQueryBuilder(nil)

	t.Run("pending transactions performance", func(t *testing.T) {
		query := qb.BuildPendingTransactionsQuery()

		// 确保使用了性能优化
		assert.Contains(t, query, "LEFT JOIN report_records rr", "应该使用LEFT JOIN而不是子查询")
		assert.Contains(t, query, "rr.business_id IS NULL", "应该使用IS NULL而不是NOT IN")
		assert.Contains(t, query, "o.id > $1", "应该使用游标分页而不是OFFSET")
		assert.Contains(t, query, "ORDER BY o.id", "应该按ID排序以支持游标分页")
		assert.Contains(t, query, "LIMIT $2", "应该限制结果数量")

		// 确保避免了性能杀手（排除注释中的文本）
		queryUpper := strings.ToUpper(query)
		commentRemoved := strings.ReplaceAll(queryUpper, "-- ", "")

		// 检查WHERE条件中是否真的使用了NOT IN（而不是注释）
		whereIndex := strings.Index(queryUpper, "WHERE")
		if whereIndex != -1 {
			whereClause := queryUpper[whereIndex:]
			assert.NotContains(t, whereClause, " NOT IN ", "WHERE条件中应该避免使用NOT IN")
		}

		assert.NotContains(t, commentRemoved, "OFFSET", "应该避免使用OFFSET分页")
		assert.NotContains(t, commentRemoved, "COUNT(*)", "应该避免在主查询中使用COUNT")
	})
}

// 测试参数绑定的正确性
func TestParameterBinding(t *testing.T) {
	qb := NewQueryBuilder(nil)

	t.Run("transaction by IDs parameter binding", func(t *testing.T) {
		orderIDs := []int64{100, 200, 300}
		query, args := qb.BuildTransactionByIDsQuery(orderIDs)

		// 验证参数数量匹配
		assert.Len(t, args, len(orderIDs))

		// 验证参数值正确
		for i, expectedID := range orderIDs {
			assert.Equal(t, expectedID, args[i])
		}

		// 验证占位符数量
		expectedPlaceholders := len(orderIDs)
		actualPlaceholders := 0
		for i := 1; i <= expectedPlaceholders; i++ {
			if strings.Contains(query, fmt.Sprintf("$%d", i)) {
				actualPlaceholders++
			}
		}
		assert.Equal(t, expectedPlaceholders, actualPlaceholders)
	})

	t.Run("promotion info parameter binding", func(t *testing.T) {
		orderIDs := []int64{500, 600}
		_, args := qb.BuildPromotionInfoQuery(orderIDs)

		assert.Len(t, args, len(orderIDs))

		for i, expectedID := range orderIDs {
			assert.Equal(t, expectedID, args[i])
		}
	})
}

// 基准测试
func BenchmarkQueryBuilder_BuildPendingTransactionsQuery(b *testing.B) {
	qb := NewQueryBuilder(nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = qb.BuildPendingTransactionsQuery()
	}
}

func BenchmarkQueryBuilder_BuildTransactionByIDsQuery(b *testing.B) {
	qb := NewQueryBuilder(nil)
	orderIDs := make([]int64, 100) // 100个订单ID
	for i := range orderIDs {
		orderIDs[i] = int64(i + 1)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		query, args := qb.BuildTransactionByIDsQuery(orderIDs)
		// 防止编译器优化掉基准测试
		_ = query
		_ = args
	}
}
