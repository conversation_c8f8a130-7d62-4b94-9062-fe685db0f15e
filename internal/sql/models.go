package sql

import (
	"database/sql/driver"
	"fmt"
	"time"
)

// TransactionRecord 交易记录的轻量级结构，直接对应SQL查询结果
type TransactionRecord struct {
	// 订单基础信息
	OrderID        string     `db:"order_id"`
	OrderNumber    string     `db:"order_number"`
	StationID      int64      `db:"station_id"`
	TotalAmount    float64    `db:"total_amount"`
	FinalAmount    float64    `db:"final_amount"`
	DiscountAmount float64    `db:"discount_amount"`
	CreatedAt      time.Time  `db:"created_at"`
	CompletedAt    *time.Time `db:"completed_at"`

	// 员工信息
	StaffCardID  *int64  `db:"staff_card_id"`
	EmployeeNo   *string `db:"employee_no"`
	OperatorName *string `db:"operator_name"`

	// 燃油交易信息
	TransactionNumber *string    `db:"transaction_number"`
	PumpID            *string    `db:"pump_id"`
	NozzleID          *string    `db:"nozzle_id"`
	FuelType          *string    `db:"fuel_type"`
	FuelGrade         *string    `db:"fuel_grade"`
	UnitPrice         *float64   `db:"unit_price"`
	Volume            *float64   `db:"volume"`
	TransactionAmount *float64   `db:"transaction_amount"`
	StartTotalizer    *float64   `db:"start_totalizer"`
	EndTotalizer      *float64   `db:"end_totalizer"`
	NozzleEndTime     *time.Time `db:"nozzle_end_time"`
	NozzleStartTime   *time.Time `db:"nozzle_start_time"`
	MemberCardID      *string    `db:"member_card_id"`

	// 站点信息
	StationAddress *string `db:"station_address"`

	// 客户信息（从订单metadata中提取）
	CustomerName   *string `db:"customer_name"`
	CustomerPhone  *string `db:"customer_phone"`
	CustomerEmail  *string `db:"customer_email"`
	CustomerGender *string `db:"customer_gender"`

	// 原始metadata字段（用于车辆信息解析）
	OrderMetadata *string `db:"order_metadata"`

	// 支付信息
	PaymentMethod     *string `db:"payment_method"`
	PaymentMethodName *string `db:"payment_method_name"`

	// 优惠信息
	PromotionName     *string  `db:"promotion_name"`
	PromotionType     *string  `db:"promotion_type"`
	PromotionDiscount *float64 `db:"promotion_discount"`
}

// GetTransactionDate 获取交易时间，优先使用油枪结束时间
func (tr *TransactionRecord) GetTransactionDate() time.Time {
	if tr.NozzleEndTime != nil {
		return *tr.NozzleEndTime
	}
	return tr.CreatedAt
}

// GetOperatorID 获取操作员标识
func (tr *TransactionRecord) GetOperatorID() string {
	if tr.OperatorName != nil && *tr.OperatorName != "" {
		return *tr.OperatorName
	}
	if tr.StaffCardID != nil {
		return fmt.Sprintf("CARD_%d", *tr.StaffCardID)
	}
	if tr.EmployeeNo != nil && *tr.EmployeeNo != "" {
		return *tr.EmployeeNo
	}
	return "OP001" // 默认值
}

// GetVehicleID 生成车辆ID
func (tr *TransactionRecord) GetVehicleID() string {
	return fmt.Sprintf("VEH_%d", tr.OrderID)
}

// GetTransactionID 生成交易ID
func (tr *TransactionRecord) GetTransactionID() string {
	return tr.OrderID
}

// GetSlipNumber 生成小票号
func (tr *TransactionRecord) GetSlipNumber() string {
	return fmt.Sprintf("SLIP_%d", tr.OrderID)
}

// GetSiteID 生成站点ID
func (tr *TransactionRecord) GetSiteID() string {
	return fmt.Sprintf("%03d", tr.StationID)
}

// GetDeviceID 生成设备ID
func (tr *TransactionRecord) GetDeviceID() string {
	return fmt.Sprintf("DEV%03d", tr.StationID)
}

// HasFuelTransaction 检查是否有燃油交易数据
func (tr *TransactionRecord) HasFuelTransaction() bool {
	return tr.TransactionNumber != nil && *tr.TransactionNumber != ""
}

// GetVolume 获取加油量，有真实数据时使用，否则通过金额计算
func (tr *TransactionRecord) GetVolume() float64 {
	if tr.Volume != nil {
		return *tr.Volume
	}

	// 通过金额和单价计算
	if tr.UnitPrice != nil && *tr.UnitPrice > 0 {
		return tr.TotalAmount / *tr.UnitPrice
	}

	// 使用默认单价计算
	return tr.TotalAmount / 15000.00
}

// GetUnitPrice 获取单价
func (tr *TransactionRecord) GetUnitPrice() float64 {
	if tr.UnitPrice != nil {
		return *tr.UnitPrice
	}
	return 15000.00 // 默认单价
}

// GetTotalizerStart 获取起始计数器
func (tr *TransactionRecord) GetTotalizerStart() float64 {
	if tr.StartTotalizer != nil {
		return *tr.StartTotalizer
	}

	// 计算后备值
	volume := tr.GetVolume()
	if tr.EndTotalizer != nil {
		return *tr.EndTotalizer - volume
	}

	return 1000000.00 // 默认起始值
}

// GetTotalizerEnd 获取结束计数器
func (tr *TransactionRecord) GetTotalizerEnd() float64 {
	if tr.EndTotalizer != nil {
		return *tr.EndTotalizer
	}

	// 计算后备值
	start := tr.GetTotalizerStart()
	volume := tr.GetVolume()
	return start + volume
}

// BatchTransactionRecords 批量交易记录
type BatchTransactionRecords struct {
	Records []TransactionRecord
	Total   int
	HasMore bool
}

// PaginationCursor 分页游标
type PaginationCursor struct {
	LastOrderID int64
	BatchSize   int
}

// NullTime 处理SQL中的NULL时间
type NullTime struct {
	Time  time.Time
	Valid bool
}

// Scan 实现sql.Scanner接口
func (nt *NullTime) Scan(value interface{}) error {
	if value == nil {
		nt.Time, nt.Valid = time.Time{}, false
		return nil
	}
	nt.Valid = true
	if t, ok := value.(time.Time); ok {
		nt.Time = t
		return nil
	}
	return fmt.Errorf("cannot scan %T into NullTime", value)
}

// Value 实现driver.Valuer接口
func (nt NullTime) Value() (driver.Value, error) {
	if !nt.Valid {
		return nil, nil
	}
	return nt.Time, nil
}

// NullFloat64 处理SQL中的NULL浮点数
type NullFloat64 struct {
	Float64 float64
	Valid   bool
}

// Scan 实现sql.Scanner接口
func (nf *NullFloat64) Scan(value interface{}) error {
	if value == nil {
		nf.Float64, nf.Valid = 0, false
		return nil
	}
	nf.Valid = true
	return nil // 这里应该实现具体的类型转换
}

// Value 实现driver.Valuer接口
func (nf NullFloat64) Value() (driver.Value, error) {
	if !nf.Valid {
		return nil, nil
	}
	return nf.Float64, nil
}
