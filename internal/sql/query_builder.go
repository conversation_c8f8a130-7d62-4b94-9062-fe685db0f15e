package sql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
)

// QueryBuilder SQL查询构建器
type QueryBuilder struct {
	db *sql.DB
}

// NewQueryBuilder 创建查询构建器
func NewQueryBuilder(db *sql.DB) *QueryBuilder {
	return &QueryBuilder{db: db}
}

// BuildPendingTransactionsQuery 构建待上报交易查询
// 这是核心查询，使用LEFT JOIN替代性能较差的NOT IN
func (qb *QueryBuilder) BuildPendingTransactionsQuery() string {
	return `
	SELECT 
		-- 订单基础信息
		o.id as order_id,
		o.order_number,
		o.station_id,
		o.total_amount,
		o.final_amount,
		o.discount_amount,
		o.created_at,
		o.completed_at,
		o.staff_card_id,
		o.employee_no,
		o.customer_name,
		o.metadata as order_metadata,
		
		-- 燃油交易信息
		ft.transaction_number,
		ft.pump_id,
		ft.nozzle_id,
		ft.fuel_type,
		ft.fuel_grade,
		ft.unit_price,
		ft.volume,
		ft.amount as transaction_amount,
		ft.start_totalizer,
		ft.end_totalizer,
		ft.nozzle_end_time,
		ft.member_card_id,
		
		-- 操作员信息（通过员工卡关联）
		u.full_name as operator_name,
		
		-- 站点地址信息
		s.address as station_address
		
	FROM order_schema.orders o
	
	-- 关联燃油交易（LEFT JOIN确保订单数据不丢失）
	LEFT JOIN order_schema.fuel_transaction_order_links ftol ON o.id = ftol.order_id
	LEFT JOIN order_schema.fuel_transactions ft ON ftol.fuel_transaction_id = ft.id
	
	-- 关联员工信息（优先使用交易中的员工卡，其次使用订单中的）
	LEFT JOIN order_schema.staff_cards sc ON COALESCE(ft.staff_card_id, o.staff_card_id) = sc.id
	LEFT JOIN core_schema.users u ON sc.user_id = u.id
	
	-- 关联站点信息
	LEFT JOIN core_schema.stations s ON o.station_id = s.id
	
	-- 关联上报记录（这是性能优化的关键）
	LEFT JOIN report_records rr ON o.id = rr.business_id AND rr.record_type = 'transaction'
	
	WHERE 
		o.status = 'completed'           -- 只处理已完成订单
		AND rr.business_id IS NULL       -- 未上报的订单（LEFT JOIN + IS NULL比NOT IN快）
		AND o.id > $1                    -- 游标分页
		
	ORDER BY o.id                        -- 确保结果有序
	LIMIT $2
	`
}

// BuildTransactionByIDsQuery 根据订单ID批量查询交易详情
func (qb *QueryBuilder) BuildTransactionByIDsQuery(orderIDs []int64) (string, []interface{}) {
	if len(orderIDs) == 0 {
		return "", nil
	}

	// 构建占位符 $1, $2, $3...
	placeholders := make([]string, len(orderIDs))
	args := make([]interface{}, len(orderIDs))

	for i, id := range orderIDs {
		placeholders[i] = fmt.Sprintf("$%d", i+1)
		args[i] = id
	}

	query := `
	SELECT 
		-- 订单基础信息
		o.id as order_id,
		o.order_number,
		o.station_id,
		o.total_amount,
		o.final_amount,
		o.discount_amount,
		o.created_at,
		o.completed_at,
		o.staff_card_id,
		o.employee_no,
		o.customer_name,
		o.metadata as order_metadata,
		
		-- 燃油交易信息
		ft.transaction_number,
		ft.pump_id,
		ft.nozzle_id,
		ft.fuel_type,
		ft.fuel_grade,
		ft.unit_price,
		ft.volume,
		ft.amount as transaction_amount,
		ft.start_totalizer,
		ft.end_totalizer,
		ft.nozzle_end_time,
		ft.member_card_id,
		
		-- 操作员信息
		u.full_name as operator_name,
		
		-- 站点地址信息
		s.address as station_address,
		
		-- 支付信息
		pm.display_name as payment_method_name
		
	FROM order_schema.orders o
	
	-- 关联燃油交易
	LEFT JOIN order_schema.fuel_transaction_order_links ftol ON o.id = ftol.order_id
	LEFT JOIN order_schema.fuel_transactions ft ON ftol.fuel_transaction_id = ft.id
	
	-- 关联员工信息
	LEFT JOIN order_schema.staff_cards sc ON COALESCE(ft.staff_card_id, o.staff_card_id) = sc.id
	LEFT JOIN core_schema.users u ON sc.user_id = u.id
	
	-- 关联站点信息
	LEFT JOIN core_schema.stations s ON o.station_id = s.id
	
	-- 关联支付信息
	LEFT JOIN order_schema.order_payments op ON o.id = op.order_id AND op.status = 'completed'
	LEFT JOIN payment_schema.payment_methods pm ON op.payment_method = pm.type
	
	WHERE o.id IN (` + strings.Join(placeholders, ",") + `)
	ORDER BY o.id
	`

	return query, args
}

// BuildPromotionInfoQuery 构建优惠信息查询
func (qb *QueryBuilder) BuildPromotionInfoQuery(orderIDs []int64) (string, []interface{}) {
	if len(orderIDs) == 0 {
		return "", nil
	}

	placeholders := make([]string, len(orderIDs))
	args := make([]interface{}, len(orderIDs))

	for i, id := range orderIDs {
		placeholders[i] = fmt.Sprintf("$%d", i+1)
		args[i] = id
	}

	query := `
	SELECT 
		op.order_id,
		op.promotion_name,
		op.promotion_type,
		op.discount_amount as promotion_discount
	FROM order_schema.order_promotions op
	WHERE op.order_id IN (` + strings.Join(placeholders, ",") + `)
	ORDER BY op.order_id, op.id
	`

	return query, args
}

// QueryPendingTransactions 查询待上报交易（分页）
func (qb *QueryBuilder) QueryPendingTransactions(ctx context.Context, cursor *PaginationCursor) (*BatchTransactionRecords, error) {
	query := qb.BuildPendingTransactionsQuery()

	rows, err := qb.db.QueryContext(ctx, query, cursor.LastOrderID, cursor.BatchSize+1) // +1 用于判断是否还有更多数据
	if err != nil {
		return nil, fmt.Errorf("查询待上报交易失败: %w", err)
	}
	defer rows.Close()

	var records []TransactionRecord
	for rows.Next() {
		var record TransactionRecord
		err := qb.scanTransactionRecord(rows, &record)
		if err != nil {
			return nil, fmt.Errorf("扫描交易记录失败: %w", err)
		}
		records = append(records, record)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历查询结果失败: %w", err)
	}

	// 检查是否还有更多数据
	hasMore := len(records) > cursor.BatchSize
	if hasMore {
		records = records[:cursor.BatchSize] // 移除多查的那一条记录
	}

	return &BatchTransactionRecords{
		Records: records,
		Total:   len(records),
		HasMore: hasMore,
	}, nil
}

// QueryTransactionsByIDs 根据订单ID批量查询交易详情
func (qb *QueryBuilder) QueryTransactionsByIDs(ctx context.Context, orderIDs []int64) ([]TransactionRecord, error) {
	if len(orderIDs) == 0 {
		return nil, nil
	}

	query, args := qb.BuildTransactionByIDsQuery(orderIDs)

	rows, err := qb.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("根据ID查询交易失败: %w", err)
	}
	defer rows.Close()

	var records []TransactionRecord
	for rows.Next() {
		var record TransactionRecord
		err := qb.scanTransactionRecordWithPayment(rows, &record)
		if err != nil {
			return nil, fmt.Errorf("扫描交易记录失败: %w", err)
		}
		records = append(records, record)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历查询结果失败: %w", err)
	}

	return records, nil
}

// QueryPromotionInfo 查询订单优惠信息
func (qb *QueryBuilder) QueryPromotionInfo(ctx context.Context, orderIDs []int64) (map[int64][]TransactionRecord, error) {
	if len(orderIDs) == 0 {
		return nil, nil
	}

	query, args := qb.BuildPromotionInfoQuery(orderIDs)

	rows, err := qb.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询优惠信息失败: %w", err)
	}
	defer rows.Close()

	promotionMap := make(map[int64][]TransactionRecord)

	for rows.Next() {
		var orderID int64
		var record TransactionRecord

		err := rows.Scan(
			&orderID,
			&record.PromotionName,
			&record.PromotionType,
			&record.PromotionDiscount,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描优惠信息失败: %w", err)
		}

		promotionMap[orderID] = append(promotionMap[orderID], record)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历优惠查询结果失败: %w", err)
	}

	return promotionMap, nil
}

// scanTransactionRecord 扫描基础交易记录
func (qb *QueryBuilder) scanTransactionRecord(rows *sql.Rows, record *TransactionRecord) error {
	return rows.Scan(
		&record.OrderID,
		&record.OrderNumber,
		&record.StationID,
		&record.TotalAmount,
		&record.FinalAmount,
		&record.DiscountAmount,
		&record.CreatedAt,
		&record.CompletedAt,
		&record.StaffCardID,
		&record.EmployeeNo,
		&record.CustomerName,

		// 燃油交易信息（可能为空）
		&record.TransactionNumber,
		&record.PumpID,
		&record.NozzleID,
		&record.FuelType,
		&record.FuelGrade,
		&record.UnitPrice,
		&record.Volume,
		&record.TransactionAmount,
		&record.StartTotalizer,
		&record.EndTotalizer,
		&record.NozzleEndTime,
		&record.MemberCardID,

		// 员工信息
		&record.OperatorName,

		// 站点信息
		&record.StationAddress,
	)
}

// scanTransactionRecordWithPayment 扫描包含支付信息的交易记录
func (qb *QueryBuilder) scanTransactionRecordWithPayment(rows *sql.Rows, record *TransactionRecord) error {
	return rows.Scan(
		&record.OrderID,
		&record.OrderNumber,
		&record.StationID,
		&record.TotalAmount,
		&record.FinalAmount,
		&record.DiscountAmount,
		&record.CreatedAt,
		&record.CompletedAt,
		&record.StaffCardID,
		&record.EmployeeNo,
		&record.CustomerName,
		&record.OrderMetadata, // 添加metadata字段扫描

		// 燃油交易信息
		&record.TransactionNumber,
		&record.PumpID,
		&record.NozzleID,
		&record.FuelType,
		&record.FuelGrade,
		&record.UnitPrice,
		&record.Volume,
		&record.TransactionAmount,
		&record.StartTotalizer,
		&record.EndTotalizer,
		&record.NozzleEndTime,
		&record.MemberCardID,

		// 员工信息
		&record.OperatorName,

		// 站点信息
		&record.StationAddress,

		// 支付信息
		&record.PaymentMethodName,
	)
}
