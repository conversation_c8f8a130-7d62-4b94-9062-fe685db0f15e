// Package repository 定义了数据访问层的接口
package repository

import (
	"context"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/hos_reporter"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service"
)

// ReportRecordRepo 定义了上报记录仓储的接口
type ReportRecordRepo interface {
	// FindPending 查询指定数量的待上报记录
	FindPending(ctx context.Context, limit int) ([]*hos_reporter.ReportRecord, error)

	// Add 添加待上报数据
	Add(ctx context.Context, data *hos_reporter.ReportRecord) error

	// GetByType 检查指定记录类型的记录
	GetByType(ctx context.Context, recordType string) ([]*hos_reporter.ReportRecord, error)

	// UpdateReportingStatus 批量更新记录的上报状态
	UpdateReportingStatus(ctx context.Context, ids []int64, status string, retryCount int) error
}

// OrderRepo 定义了订单仓储的接口
type OrderRepo interface {
	// FindPaidNotReport 查询已支付未上报的订单
	FindPaidNotReport(ctx context.Context, orderIds []string) ([]*order_service.Order, error)

	// GetByIDs 根据ID列表查询订单
	GetByIDs(ctx context.Context, ids []string) ([]*order_service.Order, error)
}
