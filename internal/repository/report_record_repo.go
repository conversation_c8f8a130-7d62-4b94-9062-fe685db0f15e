package repository

import (
	"context"
	"fmt"
	"time"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/hos_reporter"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/hos_reporter/reportrecord"
)

// ReportRecordRepoImpl 是 ReportRecordRepo 接口的具体实现
type ReportRecordRepoImpl struct {
	client *hos_reporter.Client
}

// NewReportRecordRepo 创建新的 ReportRecordRepo 实例
func NewReportRecordRepo(client *hos_reporter.Client) ReportRecordRepo {
	return &ReportRecordRepoImpl{
		client: client,
	}
}

// FindPending 查询指定数量的待上报记录
func (r *ReportRecordRepoImpl) FindPending(ctx context.Context, limit int) ([]*hos_reporter.ReportRecord, error) {
	return r.client.ReportRecord.
		Query().
		Where(reportrecord.ReportingStatusIn(reportrecord.ReportingStatusPending, reportrecord.ReportingStatusFailed)).
		Limit(limit).
		Order(hos_reporter.Asc(reportrecord.FieldCreatedAt)). // 按创建时间升序，优先处理旧记录
		All(ctx)
}

// Add 添加待上报数据
func (r *ReportRecordRepoImpl) Add(ctx context.Context, data *hos_reporter.ReportRecord) error {
	create := r.client.ReportRecord.
		Create().
		SetRecordType(data.RecordType).
		SetBusinessID(data.BusinessID).
		SetReportingStatus(data.ReportingStatus).
		SetReportingRetryCount(data.ReportingRetryCount).
		SetPriority(data.Priority)

	// 处理可选字段
	if !data.LastReportingAttemptAt.IsZero() {
		create = create.SetLastReportingAttemptAt(data.LastReportingAttemptAt)
	}
	if data.ErrorMessage != "" {
		create = create.SetErrorMessage(data.ErrorMessage)
	}
	if data.ErpResponseCode != "" {
		create = create.SetErpResponseCode(data.ErpResponseCode)
	}
	if data.SiteID != "" {
		create = create.SetSiteID(data.SiteID)
	}

	_, err := create.Save(ctx)
	return err
}

// GetByType 检查指定记录类型的记录
func (r *ReportRecordRepoImpl) GetByType(ctx context.Context, recordType string) ([]*hos_reporter.ReportRecord, error) {
	records, err := r.client.ReportRecord.
		Query().
		Where(
			reportrecord.RecordTypeEQ(recordType),
		).
		All(ctx)

	if err != nil {
		return nil, err
	}

	return records, nil
}

// UpdateReportingStatus 批量更新记录的上报状态
func (r *ReportRecordRepoImpl) UpdateReportingStatus(ctx context.Context, ids []int64, status string, retryCount int) error {
	// 将 string 状态转换为 enum 类型
	var reportingStatus reportrecord.ReportingStatus
	switch status {
	case "pending":
		reportingStatus = reportrecord.ReportingStatusPending
	case "success":
		reportingStatus = reportrecord.ReportingStatusSuccess
	case "failed":
		reportingStatus = reportrecord.ReportingStatusFailed
	default:
		return fmt.Errorf("无效的上报状态: %s", status)
	}

	_, err := r.client.ReportRecord.
		Update().
		Where(reportrecord.IDIn(ids...)).
		SetReportingStatus(reportingStatus).
		SetReportingRetryCount(retryCount).
		SetLastReportingAttemptAt(time.Now()).
		Save(ctx)

	return err
}
