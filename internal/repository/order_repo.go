package repository

import (
	"context"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
)

// OrderRepoImpl 是 OrderRepo 接口的具体实现
type OrderRepoImpl struct {
	client *order_service.Client
}

// NewOrderRepo 创建新的 OrderRepo 实例
func NewOrderRepo(client *order_service.Client) OrderRepo {
	return &OrderRepoImpl{
		client: client,
	}
}

// FindPaidNotReport 查询已支付未上报的订单
func (r *OrderRepoImpl) FindPaidNotReport(ctx context.Context, orderIds []string) ([]*order_service.Order, error) {
	// Convert int64 IDs to strings to match the Order entity's string ID field
	stringOrderIds := orderIds

	return r.client.Order.
		Query().
		WithFuelTransactionLink(func(fql *order_service.FuelTransactionOrderLinkQuery) {
			fql.WithFuelTransaction()
		}).
		Where(order.StatusEQ("completed"), order.IDNotIn(stringOrderIds...)).
		All(ctx)
}

// GetByIDs 根据ID列表查询订单
func (r *OrderRepoImpl) GetByIDs(ctx context.Context, ids []string) ([]*order_service.Order, error) {
	// Convert int64 IDs to strings to match the Order entity's string ID field
	stringIds := ids

	return r.client.Order.
		Query().
		WithFuelTransactionLink(func(fql *order_service.FuelTransactionOrderLinkQuery) {
			fql.WithFuelTransaction()
		}).
		Where(order.IDIn(stringIds...)).
		All(ctx)
}
