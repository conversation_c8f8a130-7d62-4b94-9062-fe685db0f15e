package mapping

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"sync"

	"gopkg.in/yaml.v3"
)

// FieldMappings 包含所有字段映射配置
type FieldMappings struct {
	PumpDispenser map[string]interface{}      `yaml:"pump_dispenser"`
	NozzleNumber  map[string]interface{}      `yaml:"nozzle_number"`
	FuelProduct   map[string]interface{}      `yaml:"fuel_product"`
	VehicleType   map[string]interface{}      `yaml:"vehicle_type"`
	VehicleGroup  map[interface{}]interface{} `yaml:"vehicle_group"`
}

// Defaults 包含默认值配置
type Defaults struct {
	SiteIDPrefix        string `yaml:"site_id_prefix"`
	DeviceIDPrefix      string `yaml:"device_id_prefix"`
	OperatorIDPrefix    string `yaml:"operator_id_prefix"`
	TransactionIDPrefix string `yaml:"transaction_id_prefix"`
	SlipNumberPrefix    string `yaml:"slip_number_prefix"`
	VehicleIDPrefix     string `yaml:"vehicle_id_prefix"`

	DispenserNumber      int     `yaml:"dispenser_number"`
	NozzleNumber         int     `yaml:"nozzle_number"`
	VehicleType          int     `yaml:"vehicle_type"`
	Reprint              int     `yaml:"reprint"`
	TransactionLength    int     `yaml:"transaction_length"`
	DefaultPrice         float64 `yaml:"default_price"`
	DefaultOperator      string  `yaml:"default_operator"`
	DefaultCategory      string  `yaml:"default_category"`
	DefaultArea          string  `yaml:"default_area"`
	DefaultPromotionType string  `yaml:"default_promotion_type"`
}

// ExtractionRule 数据提取规则
type ExtractionRule struct {
	Pattern  string `yaml:"pattern"`
	Fallback int    `yaml:"fallback"`
}

// Config 完整的映射配置
type Config struct {
	FieldMappings   FieldMappings             `yaml:"field_mappings"`
	Defaults        Defaults                  `yaml:"defaults"`
	ExtractionRules map[string]ExtractionRule `yaml:"extraction_rules"`
}

// 全局配置实例
var (
	globalMappingConfig *Config
	configOnce          sync.Once
)

// LoadConfig 加载映射配置
func LoadConfig(configPath ...string) (*Config, error) {
	var err error
	configOnce.Do(func() {
		var path string
		if len(configPath) > 0 {
			path = configPath[0]
		} else {
			// 默认配置文件路径
			path = filepath.Join("config", "field_mappings.yaml")
		}

		globalMappingConfig, err = loadConfigFromFile(path)
	})

	if err != nil {
		return nil, err
	}

	return globalMappingConfig, nil
}

// GetConfig 获取全局配置实例
func GetConfig() *Config {
	if globalMappingConfig == nil {
		config, err := LoadConfig()
		if err != nil {
			// 如果加载失败，返回默认配置
			return getDefaultConfig()
		}
		return config
	}
	return globalMappingConfig
}

// loadConfigFromFile 从文件加载配置
func loadConfigFromFile(path string) (*Config, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	return &config, nil
}

// getDefaultConfig 返回默认配置
func getDefaultConfig() *Config {
	return &Config{
		FieldMappings: FieldMappings{
			PumpDispenser: map[string]interface{}{
				"1": 1, "2": 2, "3": 3, "4": 4,
				"_default": "extract_number",
			},
			NozzleNumber: map[string]interface{}{
				"1": 1, "2": 2, "3": 3, "4": 4,
				"_default": "extract_number",
			},
			FuelProduct: map[string]interface{}{
				"PERTAMAX":  "PERTAMAX",
				"PERTALITE": "PERTALITE",
				"SOLAR":     "DIESEL",
				"_default":  "PERTALITE",
			},
			VehicleType: map[string]interface{}{
				"CAR": 1, "MOTOR": 2, "TRUCK": 3,
				"_default": 1,
			},
			VehicleGroup: map[interface{}]interface{}{
				1: "Car", 2: "Motor", 3: "Truck",
				"_default": "Car",
			},
		},
		Defaults: Defaults{
			SiteIDPrefix:         "SITE",
			DeviceIDPrefix:       "DEV",
			TransactionIDPrefix:  "TXN",
			SlipNumberPrefix:     "SLIP",
			VehicleIDPrefix:      "VEH",
			DispenserNumber:      1,
			NozzleNumber:         1,
			VehicleType:          1,
			Reprint:              0,
			TransactionLength:    5,
			DefaultPrice:         15000.00,
			DefaultOperator:      "OP001",
			DefaultCategory:      "Fuel",
			DefaultArea:          "Jakarta",
			DefaultPromotionType: "None",
		},
		ExtractionRules: map[string]ExtractionRule{
			"extract_number": {
				Pattern:  `\d+`,
				Fallback: 1,
			},
		},
	}
}

// MapValue 根据映射规则转换值
func (c *Config) MapValue(mappingType string, input interface{}) interface{} {
	var mapping map[string]interface{}
	var defaultValue interface{}

	switch mappingType {
	case "pump_dispenser":
		mapping = c.FieldMappings.PumpDispenser
		defaultValue = c.Defaults.DispenserNumber
	case "nozzle_number":
		mapping = c.FieldMappings.NozzleNumber
		defaultValue = c.Defaults.NozzleNumber
	case "fuel_product":
		mapping = c.FieldMappings.FuelProduct
		defaultValue = "PERTALITE" // 燃油类型的默认值
	case "vehicle_type":
		mapping = c.FieldMappings.VehicleType
		defaultValue = c.Defaults.VehicleType
	default:
		return input
	}

	inputStr := fmt.Sprintf("%v", input)

	// 首先尝试精确匹配
	if value, exists := mapping[inputStr]; exists {
		return value
	}

	// 如果没有精确匹配，尝试默认规则
	if defaultRule, exists := mapping["_default"]; exists {
		if _, ok := defaultRule.(string); ok {
			// 应用提取规则
			result := c.applyDefaultRule(defaultRule, inputStr)
			// 如果提取成功且不等于原始输入，返回提取结果
			if result != inputStr {
				return result
			}
		} else {
			// 如果默认规则不是字符串，直接返回该值
			return defaultRule
		}
	}

	// 所有方法都失败时，返回类型对应的默认值
	return defaultValue
}

// applyDefaultRule 应用默认规则
func (c *Config) applyDefaultRule(rule interface{}, input string) interface{} {
	ruleStr, ok := rule.(string)
	if !ok {
		return input
	}

	extractionRule, exists := c.ExtractionRules[ruleStr]
	if !exists {
		return input
	}

	// 使用正则表达式提取
	re, err := regexp.Compile(extractionRule.Pattern)
	if err != nil {
		return extractionRule.Fallback
	}

	match := re.FindString(input)
	if match != "" {
		if num, err := strconv.Atoi(match); err == nil {
			return num
		}
	}

	return extractionRule.Fallback
}

// MapVehicleGroup 映射车辆类型组
func (c *Config) MapVehicleGroup(vehicleType interface{}) string {
	if value, exists := c.FieldMappings.VehicleGroup[vehicleType]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}

	if defaultValue, exists := c.FieldMappings.VehicleGroup["_default"]; exists {
		if str, ok := defaultValue.(string); ok {
			return str
		}
	}

	return c.Defaults.DefaultCategory
}
