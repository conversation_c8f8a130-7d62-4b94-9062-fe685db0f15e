package mapping

import (
	"os"
	"path/filepath"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestLoadConfig(t *testing.T) {
	tests := []struct {
		name    string
		setup   func() (string, func())
		wantErr bool
	}{
		{
			name: "load valid config",
			setup: func() (string, func()) {
				// 创建临时配置文件
				tmpDir := t.TempDir()
				configPath := filepath.Join(tmpDir, "test_config.yaml")

				configContent := `
field_mappings:
  pump_dispenser:
    "1": 1
    "2": 2
    "_default": "extract_number"
  nozzle_number:
    "N01": 1
    "N02": 2
    "_default": "extract_number"
  fuel_product:
    "PERTAMAX": "PERTAMAX"
    "PERTALITE": "PERTALITE"
    "_default": "PERTALITE"

defaults:
  dispenser_number: 1
  default_operator: "OP001"

extraction_rules:
  extract_number:
    pattern: "\\d+"
    fallback: 1
`
				err := os.WriteFile(configPath, []byte(configContent), 0644)
				require.NoError(t, err)

				return configPath, func() {
					// 清理全局状态
					globalMappingConfig = nil
					configOnce = sync.Once{}
				}
			},
			wantErr: false,
		},
		{
			name: "config file not found",
			setup: func() (string, func()) {
				return "/nonexistent/config.yaml", func() {
					globalMappingConfig = nil
					configOnce = sync.Once{}
				}
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			configPath, cleanup := tt.setup()
			defer cleanup()

			config, err := LoadConfig(configPath)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, config)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, config)

				// 验证配置结构
				assert.NotEmpty(t, config.FieldMappings.PumpDispenser)
				assert.NotEmpty(t, config.FieldMappings.FuelProduct)
				assert.Equal(t, 1, config.Defaults.DispenserNumber)
			}
		})
	}
}

func TestGetConfig(t *testing.T) {
	// 清理全局状态
	globalMappingConfig = nil
	configOnce = sync.Once{}

	config := GetConfig()
	assert.NotNil(t, config)

	// 验证默认配置
	assert.Equal(t, "SITE", config.Defaults.SiteIDPrefix)
	assert.Equal(t, 1, config.Defaults.DispenserNumber)
	assert.Equal(t, "OP001", config.Defaults.DefaultOperator)
}

func TestMapValue(t *testing.T) {
	config := getDefaultConfig()

	tests := []struct {
		name        string
		mappingType string
		input       interface{}
		expected    interface{}
	}{
		// Pump Dispenser 映射测试
		{
			name:        "pump exact match",
			mappingType: "pump_dispenser",
			input:       "1",
			expected:    1,
		},
		{
			name:        "pump number extraction",
			mappingType: "pump_dispenser",
			input:       "PUMP_123",
			expected:    123,
		},
		{
			name:        "pump fallback",
			mappingType: "pump_dispenser",
			input:       "INVALID_PUMP",
			expected:    1, // fallback value
		},

		// Nozzle Number 映射测试
		{
			name:        "nozzle exact match",
			mappingType: "nozzle_number",
			input:       "2",
			expected:    2,
		},
		{
			name:        "nozzle number extraction",
			mappingType: "nozzle_number",
			input:       "NOZZLE_456",
			expected:    456,
		},

		// Fuel Product 映射测试
		{
			name:        "fuel exact match",
			mappingType: "fuel_product",
			input:       "PERTAMAX",
			expected:    "PERTAMAX",
		},
		{
			name:        "fuel default",
			mappingType: "fuel_product",
			input:       "UNKNOWN_FUEL",
			expected:    "PERTALITE",
		},

		// Vehicle Type 映射测试
		{
			name:        "vehicle type exact match",
			mappingType: "vehicle_type",
			input:       "CAR",
			expected:    1,
		},
		{
			name:        "vehicle type default",
			mappingType: "vehicle_type",
			input:       "UNKNOWN",
			expected:    1,
		},

		// 无效映射类型
		{
			name:        "invalid mapping type",
			mappingType: "invalid_type",
			input:       "test",
			expected:    "test",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := config.MapValue(tt.mappingType, tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestMapVehicleGroup(t *testing.T) {
	config := getDefaultConfig()

	tests := []struct {
		name        string
		vehicleType interface{}
		expected    string
	}{
		{
			name:        "car type",
			vehicleType: 1,
			expected:    "Car",
		},
		{
			name:        "motor type",
			vehicleType: 2,
			expected:    "Motor",
		},
		{
			name:        "truck type",
			vehicleType: 3,
			expected:    "Truck",
		},
		{
			name:        "unknown type",
			vehicleType: 99,
			expected:    "Car", // default
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := config.MapVehicleGroup(tt.vehicleType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestApplyDefaultRule(t *testing.T) {
	config := getDefaultConfig()

	tests := []struct {
		name     string
		rule     interface{}
		input    string
		expected interface{}
	}{
		{
			name:     "extract number from string",
			rule:     "extract_number",
			input:    "PUMP_123",
			expected: 123,
		},
		{
			name:     "extract first number",
			rule:     "extract_number",
			input:    "ABC123DEF456",
			expected: 123,
		},
		{
			name:     "no number found",
			rule:     "extract_number",
			input:    "NO_NUMBERS",
			expected: 1, // fallback
		},
		{
			name:     "invalid rule",
			rule:     "invalid_rule",
			input:    "test",
			expected: "test",
		},
		{
			name:     "non-string rule",
			rule:     123,
			input:    "test",
			expected: "test",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := config.applyDefaultRule(tt.rule, tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// 基准测试
func BenchmarkMapValue(b *testing.B) {
	config := getDefaultConfig()

	b.Run("exact_match", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			config.MapValue("pump_dispenser", "1")
		}
	})

	b.Run("extraction", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			config.MapValue("pump_dispenser", "PUMP_123")
		}
	})
}
