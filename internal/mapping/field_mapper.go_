package mapping

import (
	"encoding/json"
	"fmt"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/sql"
)

// FieldMapper 字段映射器，负责将SQL查询结果转换为ERP API格式
type FieldMapper struct {
	config *Config
}

// NewFieldMapper 创建字段映射器
func NewFieldMapper(config *Config) *FieldMapper {
	if config == nil {
		config = GetConfig()
	}
	return &FieldMapper{config: config}
}

// MapToERPItem 将TransactionRecord转换为ERP TransactionDataItem
func (fm *FieldMapper) MapToERPItem(record *sql.TransactionRecord) erp.TransactionDataItem {
	item := erp.TransactionDataItem{
		// 基础标识信息 - 使用真实数据源
		TransactionID: fm.getTransactionNumber(record.TransactionNumber), // fuel_transaction.transaction_number
		SlipNumber:    record.OrderNumber,                                // order.order_number

		// 设备信息 - 使用配置化映射
		DispenserNumber: fm.mapPumpToDispenser(record.PumpID),
		NozzleNumber:    fm.mapNozzleToNumber(record.NozzleID),

		// 车辆信息 - 从order.metadata解析
		VehicleType:        fm.getVehicleTypeFromMetadata(record), // order_table.order.metadata
		VehicleID:          fm.getVehicleIDFromMetadata(record),   // order_table.order.metadata (车牌号)
		IDVehicleTypeGroup: fm.getVehicleTypeGroup(record),        // order_table.order.metadata (Car or Motor)

		// 产品信息 - 使用配置化映射
		ProductID:      fm.mapFuelTypeToProduct(record.FuelType),
		IDProductGroup: fm.config.Defaults.DefaultCategory,

		// 操作员信息
		OperatorID: record.GetOperatorID(),

		// 金额和数量
		Amount: record.TotalAmount,
		Price:  record.GetUnitPrice(),
		Volume: record.GetVolume(),

		// 时间信息
		TransactionDate: record.GetTransactionDate().Format("2006-01-02 15:04:05"),

		// 计数器信息
		TotalizerStart: record.GetTotalizerStart(),
		TotalizerEnd:   record.GetTotalizerEnd(),

		// 站点信息
		SiteID:   record.GetSiteID(),
		DeviceID: record.GetDeviceID(),
		AreaSite: fm.getAreaSite(record.StationAddress),

		// RFID信息
		RFIDVehicleID: fm.getRFIDVehicleID(record.MemberCardID),

		// 客户信息
		CustomerName:    fm.getCustomerName(record.CustomerName),
		CustomerPhoneNo: fm.getStringValue(record.CustomerPhone),
		Email:           fm.getStringValue(record.CustomerEmail),
		Gender:          fm.getStringValue(record.CustomerGender),
		DOB:             nil, // 避免DateTime转换错误

		// 支付信息
		FieldTambahan1: fm.getStringValue(record.PaymentMethodName), // 支付方式名称
		FieldTambahan2: fm.getStringValue(record.OperatorName),      // 员工名称
		FieldTambahan3: "",                                          // Voucher code (代金券代码)

		// 优惠信息
		PromotionType:         fm.getPromotionType(record.PromotionName),
		PercentDiscount:       0.0, // 需要从metadata解析
		AmountPercentDiscount: 0.0,
		AmountDiscount:        fm.getPromotionDiscount(record.PromotionDiscount),
		FlagItemPromotion:     fm.getFlagItemPromotion(record.PromotionName),
		FinalDiscount:         record.DiscountAmount,

		// 固定值
		Reprint:           fm.config.Defaults.Reprint,
		TransactionLength: fm.calculateTransactionLength(record), // order.payment_time - transaction.created_at (秒)
		Voucher:           0.00,
		Point:             0,
		MessageHeader:     "",      // 固定值null (空字符串)
		MessageID:         "msg11", // 固定值"msg11" - 修复缺失字段
		Category:          fm.config.Defaults.DefaultCategory,
		DEXROWID:          "", // 会员ID (可为null)
		DEX_ROW_TS:        "", // 会员注册时间 (可为null)
	}

	return item
}

// BatchMapToERPItems 批量转换TransactionRecord为ERP格式
func (fm *FieldMapper) BatchMapToERPItems(records []sql.TransactionRecord) []erp.TransactionDataItem {
	items := make([]erp.TransactionDataItem, 0, len(records))

	for _, record := range records {
		item := fm.MapToERPItem(&record)
		items = append(items, item)
	}

	return items
}

// ConvertToERPRequest 转换为完整的ERP请求
func (fm *FieldMapper) ConvertToERPRequest(records []sql.TransactionRecord, apiKey, user, password string) *erp.DispenserTransactionRequest {
	items := fm.BatchMapToERPItems(records)

	return &erp.DispenserTransactionRequest{
		Key:              apiKey,
		User:             user,
		Password:         password,
		TransactionCount: len(items),
		MessageID:        fmt.Sprintf("batch_%d", len(items)),
		Transactions:     items,
	}
}

// 私有映射方法

// mapPumpToDispenser 映射Pump ID到Dispenser Number
func (fm *FieldMapper) mapPumpToDispenser(pumpID *string) int {
	if pumpID == nil {
		return fm.config.Defaults.DispenserNumber
	}

	result := fm.config.MapValue("pump_dispenser", *pumpID)
	if intVal, ok := result.(int); ok {
		return intVal
	}

	return fm.config.Defaults.DispenserNumber
}

// mapNozzleToNumber 映射Nozzle ID到Nozzle Number
func (fm *FieldMapper) mapNozzleToNumber(nozzleID *string) int {
	if nozzleID == nil {
		return fm.config.Defaults.NozzleNumber
	}

	result := fm.config.MapValue("nozzle_number", *nozzleID)
	if intVal, ok := result.(int); ok {
		return intVal
	}

	return fm.config.Defaults.NozzleNumber
}

// mapFuelTypeToProduct 映射燃油类型到产品ID
func (fm *FieldMapper) mapFuelTypeToProduct(fuelType *string) string {
	if fuelType == nil {
		return "PERTALITE" // 默认产品
	}

	result := fm.config.MapValue("fuel_product", *fuelType)
	if strVal, ok := result.(string); ok {
		return strVal
	}

	return "PERTALITE"
}

// getAreaSite 获取站点区域信息
func (fm *FieldMapper) getAreaSite(stationAddress *string) string {
	if stationAddress != nil && *stationAddress != "" {
		// 从地址JSON中提取区域信息的逻辑
		// 这里简化处理，实际应该解析JSON
		return *stationAddress
	}
	return fm.config.Defaults.DefaultArea
}

// getRFIDVehicleID 获取RFID车辆ID
func (fm *FieldMapper) getRFIDVehicleID(memberCardID *string) string {
	if memberCardID != nil && *memberCardID != "" {
		return *memberCardID
	}
	return ""
}

// getCustomerName 获取客户姓名
func (fm *FieldMapper) getCustomerName(customerName *string) string {
	if customerName != nil && *customerName != "" {
		return *customerName
	}
	return "Guest Customer"
}

// getStringValue 安全获取字符串值（避免空字符串导致的ERP错误）
func (fm *FieldMapper) getStringValue(value *string) string {
	if value != nil && *value != "" {
		return *value
	}
	return "" // ERP会处理为null
}

// getPromotionType 获取优惠类型
func (fm *FieldMapper) getPromotionType(promotionName *string) string {
	if promotionName != nil && *promotionName != "" {
		return *promotionName
	}
	return fm.config.Defaults.DefaultPromotionType
}

// getPromotionDiscount 获取优惠金额
func (fm *FieldMapper) getPromotionDiscount(promotionDiscount *float64) float64 {
	if promotionDiscount != nil {
		return *promotionDiscount
	}
	return 0.0
}

// getFlagItemPromotion 判断是否有优惠
func (fm *FieldMapper) getFlagItemPromotion(promotionName *string) int {
	if promotionName != nil && *promotionName != "" {
		return 1 // 有优惠
	}
	return 0 // 无优惠
}

// Helper functions for testing and debugging

// GetMappingConfig 获取映射配置（用于测试）
func (fm *FieldMapper) GetMappingConfig() *Config {
	return fm.config
}

// ValidateMapping 验证映射结果（用于调试）
func (fm *FieldMapper) ValidateMapping(record *sql.TransactionRecord, item *erp.TransactionDataItem) []string {
	var issues []string

	// 检查必填字段
	if item.TransactionID == "" {
		issues = append(issues, "TransactionID不能为空")
	}

	if item.SlipNumber == "" {
		issues = append(issues, "SlipNumber不能为空")
	}

	if item.Amount <= 0 {
		issues = append(issues, "Amount必须大于0")
	}

	// 检查映射逻辑
	if record.PumpID != nil {
		mappedDispenser := fm.mapPumpToDispenser(record.PumpID)
		if mappedDispenser != item.DispenserNumber {
			issues = append(issues, fmt.Sprintf("Pump映射不一致: %s -> %d vs %d",
				*record.PumpID, mappedDispenser, item.DispenserNumber))
		}
	}

	return issues
}

// MapTransactionWithPromotion 映射包含优惠信息的交易
func (fm *FieldMapper) MapTransactionWithPromotion(record *sql.TransactionRecord, promotions []sql.TransactionRecord) erp.TransactionDataItem {
	item := fm.MapToERPItem(record)

	// 合并优惠信息
	if len(promotions) > 0 {
		totalDiscount := 0.0
		promotionNames := make([]string, 0, len(promotions))

		for _, promo := range promotions {
			if promo.PromotionDiscount != nil {
				totalDiscount += *promo.PromotionDiscount
			}
			if promo.PromotionName != nil && *promo.PromotionName != "" {
				promotionNames = append(promotionNames, *promo.PromotionName)
			}
		}

		// 更新优惠信息
		if len(promotionNames) > 0 {
			item.PromotionType = promotionNames[0] // 使用第一个优惠名称
			item.FlagItemPromotion = 1
		}

		if totalDiscount > 0 {
			item.AmountDiscount = totalDiscount
			item.FinalDiscount = totalDiscount
		}
	}

	return item
}

// getTransactionNumber 安全获取交易号 - fuel_transaction.transaction_number
func (fm *FieldMapper) getTransactionNumber(transactionNumber *string) string {
	if transactionNumber != nil {
		return *transactionNumber
	}
	// 如果没有fuel_transaction，生成备用ID
	return "TXN_FALLBACK"
}

// getVehicleTypeFromMetadata 从order.metadata解析车辆类型
func (fm *FieldMapper) getVehicleTypeFromMetadata(record *sql.TransactionRecord) int {
	if record.OrderMetadata == nil {
		return 0 // 如果不存在返回0，而不是默认值
	}

	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(*record.OrderMetadata), &metadata); err != nil {
		return 0 // JSON解析失败返回0
	}

	// 首先检查erp_info中的vehicleType
	if erpInfo, exists := metadata["erp_info"]; exists {
		if erpMap, ok := erpInfo.(map[string]interface{}); ok {
			if vehicleType, exists := erpMap["vehicleType"]; exists {
				if vt, ok := vehicleType.(float64); ok {
					return int(vt)
				}
			}
		}
	}

	// 检查member_info中的vehicle_type
	if memberInfo, exists := metadata["member_info"]; exists {
		if memberMap, ok := memberInfo.(map[string]interface{}); ok {
			if vehicleType, exists := memberMap["vehicle_type"]; exists {
				if str, ok := vehicleType.(string); ok {
					switch str {
					case "Car":
						return 1
					case "Motor", "Motorcycle":
						return 2
					case "Truck":
						return 3
					}
				}
			}
		}
	}

	return 0 // 如果未找到或无法解析，返回0
}

// getVehicleIDFromMetadata 从order.metadata解析车牌号
func (fm *FieldMapper) getVehicleIDFromMetadata(record *sql.TransactionRecord) string {
	if record.OrderMetadata == nil {
		return "" // 如果不存在返回空字符串，而不是默认值
	}

	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(*record.OrderMetadata), &metadata); err != nil {
		return "" // JSON解析失败返回空字符串
	}

	// 首先检查erp_info中的vehicleID
	if erpInfo, exists := metadata["erp_info"]; exists {
		if erpMap, ok := erpInfo.(map[string]interface{}); ok {
			if vehicleID, exists := erpMap["vehicleID"]; exists {
				if str, ok := vehicleID.(string); ok && str != "" {
					return str
				}
			}
		}
	}

	// 检查member_info中的vehicle_plate
	if memberInfo, exists := metadata["member_info"]; exists {
		if memberMap, ok := memberInfo.(map[string]interface{}); ok {
			if vehiclePlate, exists := memberMap["vehicle_plate"]; exists {
				if str, ok := vehiclePlate.(string); ok && str != "" && str != "Car" {
					return str
				}
			}
		}
	}

	return "" // 如果未找到或为空，返回空字符串
}

// getVehicleTypeGroup 从order.metadata解析车辆类型组
func (fm *FieldMapper) getVehicleTypeGroup(record *sql.TransactionRecord) string {
	if record.OrderMetadata == nil {
		return "" // 如果不存在返回空字符串，而不是默认值
	}

	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(*record.OrderMetadata), &metadata); err != nil {
		return "" // JSON解析失败返回空字符串
	}

	// 首先检查erp_info中的iD_VehicleTypeGroup
	if erpInfo, exists := metadata["erp_info"]; exists {
		if erpMap, ok := erpInfo.(map[string]interface{}); ok {
			if vehicleTypeGroup, exists := erpMap["iD_VehicleTypeGroup"]; exists {
				if str, ok := vehicleTypeGroup.(string); ok && str != "" {
					return str
				}
			}
		}
	}

	// 检查member_info中的vehicle_type
	if memberInfo, exists := metadata["member_info"]; exists {
		if memberMap, ok := memberInfo.(map[string]interface{}); ok {
			if vehicleType, exists := memberMap["vehicle_type"]; exists {
				if str, ok := vehicleType.(string); ok && str != "" {
					return str
				}
			}
		}
	}

	// 如果没有直接的分组字段，尝试从车辆类型推断
	vehicleType := fm.getVehicleTypeFromMetadata(record)
	switch vehicleType {
	case 1:
		return "Car"
	case 2:
		return "Motor"
	case 3:
		return "Truck"
	default:
		return "" // 如果未找到或无法推断，返回空字符串
	}
}

// calculateTransactionLength 计算交易服务时长（秒）
func (fm *FieldMapper) calculateTransactionLength(record *sql.TransactionRecord) int {
	// order.payment_time - transaction.created_at (秒)
	if record.NozzleStartTime != nil && record.NozzleEndTime != nil {
		duration := record.NozzleEndTime.Sub(*record.NozzleStartTime)
		return int(duration.Seconds())
	}
	// 如果没有具体时间，返回默认值
	return fm.config.Defaults.TransactionLength
}
