package erp

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
)

// PostTransactions 上报交易数据到 ERP
// 调用 ERP 的 /ATG/DispenserTransaction 接口
// 如果遇到 Token 过期错误，会自动清除缓存并重试一次
func (c *ERPClient) PostTransactions(ctx context.Context, req *DispenserTransactionRequest) (*DispenserTransactionResponse, error) {
	return c.postTransactionsWithRetry(ctx, req, true)
}

// postTransactionsWithRetry 内部方法，支持重试逻辑
// isRetry: 是否为重试调用，用于防止无限重试
func (c *ERPClient) postTransactionsWithRetry(ctx context.Context, req *DispenserTransactionRequest, isRetry bool) (*DispenserTransactionResponse, error) {
	// 调用通用的 Post 方法发送请求
	resp, err := c.Post(ctx, "/ATG/DispenserTransaction", "transaction", req)
	if err != nil {
		return nil, fmt.Errorf("发送交易上报请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	// 解析成功响应
	var result DispenserTransactionResponse
	log.Println("响应 body", string(body))
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 处理业务响应码
	switch result.Code {
	case 200:
		// 成功
		return &result, nil
	case 210:
		// 重复数据，也视为成功
		return &result, nil
	case 1001, 401:
		// Token 过期，如果是重试调用，则清除缓存并重试一次
		if isRetry {
			// 清除过期的 Token 缓存
			if clearErr := c.tokenManager.ClearTokenCache(ctx, "transaction"); clearErr != nil {
				return nil, fmt.Errorf("清除过期Token缓存失败: %w", clearErr)
			}

			// 重试一次
			return c.postTransactionsWithRetry(ctx, req, false)
		}
		// 如果是重试调用仍然失败，返回错误
		return nil, fmt.Errorf("访问令牌已过期且重试失败 (错误码: %d): %s", result.Code, result.Message)
	default:
		// 其他业务错误
		return nil, fmt.Errorf("ERP 业务错误 (错误码: %d): %s", result.Code, result.Message)
	}
}
