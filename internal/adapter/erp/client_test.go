package erp

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
)

// TestERPClient_Post_Integration 集成测试：验证ERPClient能否正确发送请求
func TestERPClient_Post_Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	// 创建测试配置
	cfg := &config.ERPAPIConfig{
		BaseURLUAT: "http://*************/apr/api",
		TransactionUser: config.ERPUserConfig{
			Key:      "123456789",
			User:     "wecar",
			Password: "1!8$3#7*4@",
		},
		ATGUser: config.ERPUserConfig{
			Key:      "123456789",
			User:     "wecar",
			Password: "1!8$3#7*4@",
		},
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 创建Redis客户端
	redisClient := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1,
	})

	// 创建TokenManager和ERPClient
	tokenManager := NewTokenManager(httpClient, cfg, redisClient)
	erpClient := NewERPClient(httpClient, tokenManager, cfg)

	t.Run("发送简单请求", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// 创建一个简单的请求体
		requestBody := map[string]interface{}{
			"test": "data",
		}

		// 尝试发送请求到一个不存在的端点（这样可以测试请求发送逻辑）
		resp, err := erpClient.Post(ctx, "/test", "transaction", requestBody)

		// 由于使用测试凭证，预期会在获取Token阶段失败
		if err != nil {
			t.Logf("预期的错误（使用测试凭证）: %v", err)
			// 验证错误是来自Token获取而不是其他地方
			if resp != nil {
				t.Error("错误时不应该返回响应")
			}
		} else {
			// 如果请求成功，验证响应
			defer resp.Body.Close()
			if resp == nil {
				t.Error("成功时应该返回响应")
			}
		}
	})

	t.Run("空请求体", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// 发送空请求体
		_, err := erpClient.Post(ctx, "/test", "transaction", nil)

		// 应该在Token获取阶段失败
		if err == nil {
			t.Error("使用测试凭证应该失败")
		}
	})

	t.Run("不支持的用户类型", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		_, err := erpClient.Post(ctx, "/test", "invalid_type", nil)

		if err == nil {
			t.Error("应该返回错误")
		}

		expectedError := "获取访问令牌失败: 获取新Token失败: 不支持的用户类型: invalid_type"
		if err.Error() != expectedError {
			t.Errorf("期望错误: %s, 实际得到: %s", expectedError, err.Error())
		}
	})
}

// TestERPClient_Post_MockServer 使用Mock服务器测试ERPClient
func TestERPClient_Post_MockServer(t *testing.T) {
	// 创建Mock HTTP服务器
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		if r.Method != "POST" {
			t.Errorf("期望POST方法，得到: %s", r.Method)
		}

		// 验证请求头
		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("期望Content-Type为application/json，得到: %s", r.Header.Get("Content-Type"))
		}

		authHeader := r.Header.Get("Authorization")
		if authHeader != "Bearer test_token_12345" {
			t.Errorf("期望Authorization为'Bearer test_token_12345'，得到: %s", authHeader)
		}

		// 验证请求体
		body, err := io.ReadAll(r.Body)
		if err != nil {
			t.Fatalf("读取请求体失败: %v", err)
		}

		var requestData map[string]interface{}
		if err := json.Unmarshal(body, &requestData); err != nil {
			t.Fatalf("解析请求体失败: %v", err)
		}

		if requestData["test"] != "data" {
			t.Errorf("期望test字段为'data'，得到: %v", requestData["test"])
		}

		// 返回成功响应
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status": "success",
		})
	}))
	defer mockServer.Close()

	// 创建测试配置，使用Mock服务器的URL
	cfg := &config.ERPAPIConfig{
		BaseURLUAT: mockServer.URL,
		TransactionUser: config.ERPUserConfig{
			Key:      "test_key",
			User:     "test_user",
			Password: "test_password",
		},
	}

	config.SetConfigForTesting(&config.Config{
		ERPAPI: *cfg,
	})
	defer config.ResetConfigForTesting()

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 创建Redis客户端
	redisClient := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1,
	})

	// 手动在Redis中设置一个测试Token
	ctx := context.Background()
	testToken := "test_token_12345"
	redisClient.Set(ctx, "erp_token:transaction", testToken, time.Hour)

	// 创建TokenManager和ERPClient
	tokenManager := NewTokenManager(httpClient, cfg, redisClient)
	erpClient := NewERPClient(httpClient, tokenManager, cfg)

	t.Run("成功发送请求", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		// 创建请求体
		requestBody := map[string]interface{}{
			"test": "data",
		}

		// 发送请求
		resp, err := erpClient.Post(ctx, "/test", "transaction", requestBody)
		if err != nil {
			t.Fatalf("发送请求失败: %v", err)
		}
		defer resp.Body.Close()

		// 验证响应状态码
		if resp.StatusCode != http.StatusOK {
			t.Errorf("期望状态码200，得到: %d", resp.StatusCode)
		}

		// 验证响应体
		var responseData map[string]string
		if err := json.NewDecoder(resp.Body).Decode(&responseData); err != nil {
			t.Fatalf("解析响应体失败: %v", err)
		}

		if responseData["status"] != "success" {
			t.Errorf("期望status为'success'，得到: %s", responseData["status"])
		}
	})

	// 清理Redis测试数据
	redisClient.Del(ctx, "erp_token:transaction")
}

// TestERPClient_NewERPClient 测试构造函数
func TestERPClient_NewERPClient(t *testing.T) {
	httpClient := &http.Client{}
	tokenManager := &TokenManager{}
	cfg := &config.ERPAPIConfig{}

	client := NewERPClient(httpClient, tokenManager, cfg)

	if client.httpClient != httpClient {
		t.Error("httpClient设置不正确")
	}
	if client.tokenManager != tokenManager {
		t.Error("tokenManager设置不正确")
	}
	if client.erpConfig != cfg {
		t.Error("erpConfig设置不正确")
	}
}
