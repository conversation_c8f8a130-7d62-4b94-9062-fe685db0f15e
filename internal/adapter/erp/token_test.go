package erp

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
)

// TestTokenManager_FetchNewToken_Integration 集成测试：验证能否从UAT环境获取Token
// 注意：此测试需要真实的ERP API环境和有效的凭证才能通过
func TestTokenManager_FetchNewToken_Integration(t *testing.T) {
	// 跳过集成测试，除非设置了环境变量
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	// 创建测试配置
	cfg := &config.ERPAPIConfig{
		BaseURLUAT: "http://*************/apr/api",
		TransactionUser: config.ERPUserConfig{
			Key:      "123456789", // 真实的测试凭证
			User:     "wecar",
			Password: "1!8$3#7*4@",
		},
		ATGUser: config.ERPUserConfig{
			Key:      "123456789", // 真实的测试凭证
			User:     "wecar",
			Password: "1!8$3#7*4@",
		},
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 创建Redis客户端（用于测试，但在FetchNewToken中不使用）
	redisClient := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   0,
	})

	// 创建TokenManager实例
	tokenManager := NewTokenManager(httpClient, cfg, redisClient)

	// 测试获取transaction用户的Token
	t.Run("获取transaction用户Token", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		token, err := tokenManager.FetchNewToken(ctx, "transaction")

		// 由于使用的是测试凭证，这个测试预期会失败
		// 但我们可以验证代码逻辑是否正确执行
		if err != nil {
			t.Logf("预期的错误（使用测试凭证）: %v", err)
			// 验证错误是由于认证失败而不是代码逻辑错误
			if token != "" {
				t.Errorf("当发生错误时，token应该为空，但得到: %s", token)
			}
		} else {
			// 如果使用了真实凭证，验证返回的token不为空
			if token == "" {
				t.Error("成功时token不应该为空")
			} else {
				t.Logf("成功获取token: %s", token[:10]+"...") // 只显示前10个字符
			}
		}
	})

	// 测试获取ATG用户的Token
	t.Run("获取ATG用户Token", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		token, err := tokenManager.FetchNewToken(ctx, "atg")

		// 同样预期会失败，但验证代码逻辑
		if err != nil {
			t.Logf("预期的错误（使用测试凭证）: %v", err)
			if token != "" {
				t.Errorf("当发生错误时，token应该为空，但得到: %s", token)
			}
		} else {
			if token == "" {
				t.Error("成功时token不应该为空")
			} else {
				t.Logf("成功获取token: %s", token[:10]+"...")
			}
		}
	})

	// 测试不支持的用户类型
	t.Run("不支持的用户类型", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		token, err := tokenManager.FetchNewToken(ctx, "invalid_user_type")

		if err == nil {
			t.Error("应该返回错误，但没有")
		}
		if token != "" {
			t.Errorf("错误时token应该为空，但得到: %s", token)
		}

		expectedError := "不支持的用户类型: invalid_user_type"
		if err.Error() != expectedError {
			t.Errorf("期望错误信息: %s, 实际得到: %s", expectedError, err.Error())
		}
	})
}

// TestTokenManager_GetToken_CacheLogic 测试GetToken方法的缓存逻辑
func TestTokenManager_GetToken_CacheLogic(t *testing.T) {
	// 创建测试配置
	cfg := &config.ERPAPIConfig{
		BaseURLUAT: "http://*************/apr/api",
		TransactionUser: config.ERPUserConfig{
			Key:      "test_key",
			User:     "test_user",
			Password: "test_password",
		},
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 创建Redis客户端（需要运行的Redis服务）
	redisClient := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1, // 使用不同的数据库避免冲突
	})

	// 清理测试数据
	ctx := context.Background()
	testKey := "erp_token:transaction"
	redisClient.Del(ctx, testKey)

	// 创建TokenManager实例
	tokenManager := NewTokenManager(httpClient, cfg, redisClient)

	t.Run("缓存未命中时获取新Token", func(t *testing.T) {
		// 确保缓存为空
		redisClient.Del(ctx, testKey)

		// 调用GetToken - 这应该会尝试获取新Token
		// 由于使用测试凭证，预期会失败，但我们可以验证缓存逻辑
		_, err := tokenManager.GetToken(ctx, "transaction")
		if err == nil {
			t.Error("使用测试凭证应该失败")
		}

		// 验证错误是来自FetchNewToken而不是缓存逻辑
		if err.Error() != "获取新Token失败: HTTP请求失败，状态码: 222" {
			t.Logf("实际错误: %v", err)
		}
	})

	t.Run("手动设置缓存后应该返回缓存的Token", func(t *testing.T) {
		// 手动在Redis中设置一个测试Token
		testToken := "test_cached_token_12345"
		err := redisClient.Set(ctx, testKey, testToken, time.Hour).Err()
		if err != nil {
			t.Fatalf("设置测试缓存失败: %v", err)
		}

		// 调用GetToken - 应该返回缓存的Token
		cachedToken, err := tokenManager.GetToken(ctx, "transaction")
		if err != nil {
			t.Fatalf("从缓存获取Token失败: %v", err)
		}

		if cachedToken != testToken {
			t.Errorf("期望Token: %s, 实际得到: %s", testToken, cachedToken)
		}
	})

	t.Run("Redis key格式正确", func(t *testing.T) {
		// 测试不同用户类型的Redis key格式
		testCases := []struct {
			userType    string
			expectedKey string
		}{
			{"transaction", "erp_token:transaction"},
			{"atg", "erp_token:atg"},
		}

		for _, tc := range testCases {
			// 手动设置缓存
			testToken := "test_token_" + tc.userType
			err := redisClient.Set(ctx, tc.expectedKey, testToken, time.Hour).Err()
			if err != nil {
				t.Fatalf("设置缓存失败: %v", err)
			}

			// 获取Token
			token, err := tokenManager.GetToken(ctx, tc.userType)
			if err != nil {
				t.Errorf("用户类型 %s 获取Token失败: %v", tc.userType, err)
				continue
			}

			if token != testToken {
				t.Errorf("用户类型 %s: 期望Token %s, 实际得到 %s", tc.userType, testToken, token)
			}

			// 清理
			redisClient.Del(ctx, tc.expectedKey)
		}
	})

	t.Run("不支持的用户类型", func(t *testing.T) {
		_, err := tokenManager.GetToken(ctx, "invalid_type")
		if err == nil {
			t.Error("应该返回错误")
		}

		expectedError := "获取新Token失败: 不支持的用户类型: invalid_type"
		if err.Error() != expectedError {
			t.Errorf("期望错误: %s, 实际得到: %s", expectedError, err.Error())
		}
	})

	// 清理所有测试数据
	redisClient.Del(ctx, "erp_token:transaction", "erp_token:atg")
}
