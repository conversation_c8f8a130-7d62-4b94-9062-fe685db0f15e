package erp

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
)

// TokenEntry Token缓存条目，包含Token和过期时间
type TokenEntry struct {
	Token     string
	ExpiresAt time.Time
}

// IsExpired 检查Token是否已过期
func (te *TokenEntry) IsExpired() bool {
	return time.Now().After(te.ExpiresAt)
}

// TokenManager Token 管理器，负责获取和缓存 ERP API 的访问令牌
// 支持Redis缓存，当Redis不可用时降级为内存缓存
type TokenManager struct {
	httpClient   *http.Client
	erpConfig    *config.ERPAPIConfig
	redisClient  *redis.Client
	memoryCache  map[string]*TokenEntry // 内存缓存，key为userType
	cacheMutex   sync.RWMutex           // 保护内存缓存的读写锁
	redisHealthy bool                   // Redis健康状态
}

// NewTokenManager 创建新的 Token 管理器实例
func NewTokenManager(httpClient *http.Client, erpConfig *config.ERPAPIConfig, redisClient *redis.Client) *TokenManager {
	return &TokenManager{
		httpClient:   httpClient,
		erpConfig:    erpConfig,
		redisClient:  redisClient,
		memoryCache:  make(map[string]*TokenEntry),
		redisHealthy: redisClient != nil,
	}
}

// GetToken 获取访问令牌，优先从Redis缓存读取，Redis失效时使用内存缓存
// userType: "transaction" 或 "atg"，用于确定使用哪个用户凭证
func (tm *TokenManager) GetToken(ctx context.Context, userType string) (string, error) {
	// 1. 如果Redis客户端为nil，直接使用内存缓存
	if tm.redisClient == nil {
		return tm.getTokenFromMemory(ctx, userType)
	}

	// 2. 尝试从Redis获取Token
	if tm.redisHealthy {
		token, err := tm.getTokenFromRedis(ctx, userType)
		if err == nil {
			return token, nil
		}

		// Redis访问失败，标记为不健康并降级到内存缓存
		log.Printf("警告: Redis访问失败，降级为内存缓存: %v", err)
		tm.redisHealthy = false
	}

	// 3. 降级到内存缓存
	return tm.getTokenFromMemory(ctx, userType)
}

// getTokenFromRedis 从Redis获取Token
func (tm *TokenManager) getTokenFromRedis(ctx context.Context, userType string) (string, error) {
	redisKey := fmt.Sprintf("erp_token:%s", userType)

	// 尝试从Redis读取Token
	token, err := tm.redisClient.Get(ctx, redisKey).Result()
	if err == nil && token != "" {
		return token, nil
	}

	// Redis读取失败，检查是否为连接问题
	if err != nil && err != redis.Nil {
		// Redis连接失败，标记为不健康并返回错误
		tm.redisHealthy = false
		return "", fmt.Errorf("Redis访问失败: %w", err)
	}

	// Redis中没有Token，获取新Token并缓存
	newToken, err := tm.FetchNewToken(ctx, userType)
	if err != nil {
		return "", fmt.Errorf("获取新Token失败: %w", err)
	}

	// 缓存到Redis
	expiration := tm.getTokenExpiration()
	if err := tm.redisClient.Set(ctx, redisKey, newToken, expiration).Err(); err != nil {
		log.Printf("警告: Redis缓存Token失败: %v", err)
		// Redis缓存失败，标记为不健康并降级到内存缓存
		tm.redisHealthy = false
		tm.cacheTokenInMemory(userType, newToken, expiration)
	}

	return newToken, nil
}

// getTokenFromMemory 从内存缓存获取Token
func (tm *TokenManager) getTokenFromMemory(ctx context.Context, userType string) (string, error) {
	tm.cacheMutex.RLock()
	entry, exists := tm.memoryCache[userType]
	tm.cacheMutex.RUnlock()

	// 检查内存缓存中是否有有效Token
	if exists && !entry.IsExpired() {
		return entry.Token, nil
	}

	// 内存缓存中没有有效Token，获取新Token
	newToken, err := tm.FetchNewToken(ctx, userType)
	if err != nil {
		return "", fmt.Errorf("获取新Token失败: %w", err)
	}

	// 缓存到内存
	expiration := tm.getTokenExpiration()
	tm.cacheTokenInMemory(userType, newToken, expiration)

	return newToken, nil
}

// cacheTokenInMemory 将Token缓存到内存
func (tm *TokenManager) cacheTokenInMemory(userType string, token string, expiration time.Duration) {
	tm.cacheMutex.Lock()
	defer tm.cacheMutex.Unlock()

	tm.memoryCache[userType] = &TokenEntry{
		Token:     token,
		ExpiresAt: time.Now().Add(expiration),
	}

	log.Printf("Token已缓存到内存，用户类型: %s，过期时间: %v", userType, tm.memoryCache[userType].ExpiresAt)
}

// getTokenExpiration 获取Token过期时间
func (tm *TokenManager) getTokenExpiration() time.Duration {
	if tm.isUATEnvironment() {
		return 25 * 24 * time.Hour // UAT环境：25天
	}
	return 5 * 30 * 24 * time.Hour // 生产环境：5个月
}

// isUATEnvironment 判断当前是否为UAT环境
// 通过检查BaseURL来判断环境类型
func (tm *TokenManager) isUATEnvironment() bool {
	// 如果BaseURLUAT包含本地地址或测试地址，或者是原始UAT地址，都视为UAT环境
	uatURL := tm.erpConfig.BaseURLUAT
	return uatURL == "http://*************/apr/api" ||
		strings.Contains(uatURL, "localhost") ||
		strings.Contains(uatURL, "127.0.0.1") ||
		strings.Contains(uatURL, "httptest")
}

// FetchNewToken 直接从 ERP API 获取新的访问令牌，不检查缓存
// userType: "transaction" 或 "atg"，用于确定使用哪个用户凭证
func (tm *TokenManager) FetchNewToken(ctx context.Context, userType string) (string, error) {
	// 根据用户类型获取对应的凭证
	var userConfig config.ERPUserConfig
	switch userType {
	case "transaction":
		userConfig = tm.erpConfig.TransactionUser
	case "atg":
		userConfig = tm.erpConfig.ATGUser
	default:
		return "", fmt.Errorf("不支持的用户类型: %s", userType)
	}

	// 构造请求体
	request := GetTokenRequest{
		ID:          0,
		Key:         userConfig.Key,
		User:        userConfig.User,
		Password:    userConfig.Password,
		IsActivated: true,
		IsDisabled:  false,
	}

	// 将请求体序列化为JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 构造请求URL
	var baseURL string
	if tm.isUATEnvironment() {
		baseURL = tm.erpConfig.BaseURLUAT
	} else {
		baseURL = tm.erpConfig.BaseURLProd
	}
	url := baseURL + "/ATG/GetToken"

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := tm.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
	}

	// 解析响应体
	var response GetTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("解析响应体失败: %w", err)
	}

	// 检查业务响应码
	if response.Code != 200 {
		return "", fmt.Errorf("获取Token失败，响应码: %d", response.Code)
	}

	// 返回访问令牌
	return response.Data.Token.AccessToken, nil
}

// ClearTokenCache 清除指定用户类型的Token缓存
// userType: "transaction" 或 "atg"，用于确定要清除哪个缓存
// ClearTokenCache 清除Token缓存，同时清理Redis和内存缓存
func (tm *TokenManager) ClearTokenCache(ctx context.Context, userType string) error {
	var errors []error

	// 清除Redis中的Token
	if tm.redisClient != nil && tm.redisHealthy {
		redisKey := fmt.Sprintf("erp_token:%s", userType)
		if err := tm.redisClient.Del(ctx, redisKey).Err(); err != nil {
			errors = append(errors, fmt.Errorf("清除Redis Token缓存失败: %w", err))
			// Redis访问失败，标记为不健康
			tm.redisHealthy = false
		}
	}

	// 清除内存中的Token
	tm.cacheMutex.Lock()
	delete(tm.memoryCache, userType)
	tm.cacheMutex.Unlock()

	if len(errors) > 0 {
		log.Printf("警告: 清除Token缓存时发生错误: %v", errors)
		// 即使Redis清除失败，内存缓存已清除，所以不返回错误
	}

	log.Printf("已清除用户类型 %s 的Token缓存", userType)
	return nil
}

// GetCacheStatus 获取缓存状态信息，用于监控和调试
func (tm *TokenManager) GetCacheStatus() map[string]interface{} {
	tm.cacheMutex.RLock()
	defer tm.cacheMutex.RUnlock()

	status := map[string]interface{}{
		"redis_healthy":      tm.redisHealthy,
		"redis_available":    tm.redisClient != nil,
		"memory_cache_count": len(tm.memoryCache),
	}

	// 添加内存缓存详情
	memoryEntries := make(map[string]interface{})
	for userType, entry := range tm.memoryCache {
		memoryEntries[userType] = map[string]interface{}{
			"expires_at": entry.ExpiresAt,
			"expired":    entry.IsExpired(),
		}
	}
	status["memory_entries"] = memoryEntries

	return status
}
