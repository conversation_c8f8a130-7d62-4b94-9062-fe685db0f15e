package erp

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
)

// ERPClient ERP API 客户端，封装所有与 ERP API 的交互
type ERPClient struct {
	httpClient   *http.Client
	tokenManager *TokenManager
	erpConfig    *config.ERPAPIConfig
}

// NewERPClient 创建新的 ERP 客户端实例
func NewERPClient(httpClient *http.Client, tokenManager *TokenManager, erpConfig *config.ERPAPIConfig) *ERPClient {
	return &ERPClient{
		httpClient:   httpClient,
		tokenManager: tokenManager,
		erpConfig:    erpConfig,
	}
}

// Post 发送 POST 请求到 ERP API，自动处理认证
// path: API 路径，例如 "/ATG/DispenserTransaction"
// userType: 用户类型，"transaction" 或 "atg"，用于获取相应的 Token
// body: 请求体，将被序列化为 JSON
func (c *ERPClient) Post(ctx context.Context, path string, userType string, body interface{}) (*http.Response, error) {
	// 获取访问令牌
	token, err := c.tokenManager.GetToken(ctx, userType)
	if err != nil {
		return nil, fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 序列化请求体
	var requestBody []byte
	if body != nil {
		requestBody, err = json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("序列化请求体失败: %w", err)
		}
	}

	// 构造完整的 URL
	// 根据环境选择base URL
	var baseURL string
	if c.tokenManager.isUATEnvironment() {
		baseURL = c.tokenManager.erpConfig.BaseURLUAT
	} else {
		baseURL = c.tokenManager.erpConfig.BaseURLProd
	}
	url := baseURL + path

	// 创建 HTTP 请求
	log.Println("请求参数", string(requestBody))
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}

	return resp, nil
}
