package erp

// GetTokenRequest 获取访问令牌的请求结构体
type GetTokenRequest struct {
	ID          int    `json:"id"`
	Key         string `json:"key"`
	User        string `json:"user"`
	Password    string `json:"password"`
	IsActivated bool   `json:"isActivated"`
	IsDisabled  bool   `json:"isDisabled"`
}

// GetTokenResponse 获取访问令牌的响应结构体
type GetTokenResponse struct {
	Code int          `json:"code"` // 响应码 (如 200 表示成功)
	Data GetTokenData `json:"data"`
}

type GetTokenData struct {
	User  User  `json:"user"`
	Token Token `json:"token"`
}

type User struct {
	ID         int    `json:"id"`
	Email      string `json:"email"`
	Activation bool   `json:"activation"`
}

type Token struct {
	AccessToken           string `json:"access_token"`
	AccessTokenExpiresIn  string `json:"access_token_expires_in"`
	RefreshToken          string `json:"refresh_token"`
	RefreshTokenExpiresIn string `json:"refresh_token_expires_in"`
}

// DispenserTransactionRequest 交易数据上报请求结构体
type DispenserTransactionRequest struct {
	Key              string                `json:"key"`
	User             string                `json:"user"`
	Password         string                `json:"password"`
	TransactionCount int                   `json:"transactionCount"`
	MessageID        string                `json:"messageID"`
	Transactions     []TransactionDataItem `json:"transactionData"`
}

type TransactionDataItem struct {
	TransactionID         string  `json:"transactionId"`           // 交易ID, order_table
	SlipNumber            string  `json:"slipNumber"`              // 订单编号, order_table
	DispenserNumber       int     `json:"dispenserNumber"`         // 加油机编号, order_table.transaction,Pump + Nozzle 做一个 Map 对应
	NozzleNumber          int     `json:"nozzleNumber"`            // 油枪编号, order_table.transaction
	VehicleType           int     `json:"vehicleType"`             // 车辆类型, order_table.order.metadata
	VehicleID             string  `json:"vehicleID"`               // 车牌号, order_table.order.metadata
	ProductID             string  `json:"productID"`               // 油品代码, order_table.transaction
	OperatorID            string  `json:"operatorID"`              // 员工编号, order_table.transaction
	Amount                float64 `json:"amount"`                  // 订单金额, order_table.order.net_amount
	Price                 float64 `json:"price"`                   // 交易单价, order_table.transaction.price
	Volume                float64 `json:"volume"`                  // 交易体积, order_table.transaction.volume
	TransactionDate       string  `json:"transactionDate"`         // 交易时间, order_table.transaction.created_at
	Reprint               int     `json:"reprint"`                 // 是否重印, order_table.metadata | 0
	TotalizerStart        float64 `json:"totalizerStart"`          // 起始计数器, order_table.transaction.start_totalizer
	TotalizerEnd          float64 `json:"totalizerEnd"`            // 结束计数器, order_table.transaction.end_totalizer
	TransactionLength     int     `json:"transactionLength"`       // 交易服务时长, order_table.order.payment_time - order_table.transaction.created_at
	RFIDVehicleID         string  `json:"rfidVehicleID"`           // B2B, Null
	SiteID                string  `json:"siteID"`                  // 站点ID, order_table.order.site_id
	DeviceID              string  `json:"deviceID"`                // 设备ID, order_table.order.metadata
	IDVehicleTypeGroup    string  `json:"iD_VehicleTypeGroup"`     // 车辆类型组, order_table.order.metadata.[Car or Motor]
	IDProductGroup        string  `json:"iD_ProductGroup"`         // 油品类型组, order_table.transaction.fuel_type - oil_table.oil_product.Category
	FieldTambahan1        string  `json:"field_Tambahan_1"`        // 支付方式名称,order_table.order.id - payment_table.payments.order_id - payment_method - payment_table.payment_methods.display_name
	FieldTambahan2        string  `json:"field_Tambahan_2"`        // 员工名称,order_table.transaction.staff_card_id - order_table.staff_card.user_id - core_table.users.full_name
	FieldTambahan3        string  `json:"field_Tambahan_3"`        // 优惠券代码, null
	PromotionType         string  `json:"promotion_Type"`          // 优惠名称, order_table.order_promotion.promotion_name
	PercentDiscount       float64 `json:"percent_Discount"`        // 类型为百分比折扣的优惠，折扣百分比, order_table.order_promotion.promotion_type = "percent".metadata
	AmountPercentDiscount float64 `json:"amount_Percent_Discount"` // 类型为百分比折扣的优惠，折扣的金额, order_table.order_promotion.promotion_type = "percent".discount_amount
	AmountDiscount        float64 `json:"amount_Discount"`         // 类型为金额折扣的优惠，折扣的金额, order_table.order_promotion.promotion_type = "fixed_amount".discount_amount
	FlagItemPromotion     int     `json:"flag_Item_Promotion"`     // 是否为优惠，0 否，1 是
	FinalDiscount         float64 `json:"final_Discount"`          // 订单折扣金额, order_table.order.discount_amount
	Voucher               float64 `json:"voucher"`                 // 优惠券, null
	Point                 int     `json:"point"`                   // 积分, null
	MessageHeader         string  `json:"message_Header"`          // null
	MessageID             string  `json:"message_ID"`              // "msg11"
	CustomerName          string  `json:"customer_Name"`           // 会员名称, order_table.order.metadata
	CustomerPhoneNo       string  `json:"customer_Phone_No"`       // 会员手机号, order_table.order.metadata
	Category              string  `json:"category"`                // 交易类型,"fuel"
	AreaSite              string  `json:"area_Site"`               // 站点地址, order_table.transaction.site_id - core_table.sites.address
	DOB                   *string `json:"dob,omitempty"`           // 会员生日, order_table.order.metadata | null
	Gender                string  `json:"gender"`                  // 会员性别,"male" or "female"
	Email                 string  `json:"email"`                   // 会员邮箱, order_table.order.metadata | null
	DEXROWID              string  `json:"dex"`                     // 会员ID, order_table.order.metadata | null
	DEX_ROW_TS            string  `json:"dex_row_ts"`              // 会员注册时间, order_table.order.metadata | null
}

// DispenserTransactionResponse 交易数据上报响应结构体
type DispenserTransactionResponse struct {
	Code    int                   `json:"code"`    // 响应码
	Message string                `json:"message"` // 响应消息
	Data    TransactionResultData `json:"data"`    // 每笔交易的处理结果
}

// TransactionResultData 单笔交易处理结果
type TransactionResultData struct {
	TransactionCount int `json:"transactionCount"` // 交易次数
	DataCreated      int `json:"dataCreated"`      // 处理状态
	ArrayLength      int `json:"arrayLength"`
	Exists           int `json:"exists"`
}

// DeliveryRequest 发货数据上报请求结构体
type DeliveryRequest struct {
	Key              string         `json:"key"`
	User             string         `json:"user"`
	Password         string         `json:"password"`
	TransactionCount int            `json:"transactionCount"`
	MessageID        string         `json:"messageId"`
	DeliveriesData   []DeliveryData `json:"deliveriesData"`
}

// DeliveryData 发货数据
type DeliveryData struct {
	FunctionCode     string  `json:"functionCode"` // 功能码
	TankNumber       string  `json:"tankNumber"`   // 油罐编号
	CurrentDateTime  string  `json:"currentDateTime"`
	ProductCode      string  `json:"productCode"` // 油品代码
	StartingDateTime string  `json:"startingDateTime"`
	EndingDateTime   string  `json:"endingDateTime"`
	StartingVolume   float64 `json:"startingVolume"`
	EndingVolume     float64 `json:"endingVolume"`
	StartingWater    float64 `json:"startingWater"`
	EndingWater      float64 `json:"endingWater"`
	StartingHeight   float64 `json:"startingHeight"`
	EndingHeight     float64 `json:"endingHeight"`
	SiteID           string  `json:"siteId"`
	DeviceID         string  `json:"deviceId"`
}

// DeliveryResponse 发货数据上报响应结构体
type DeliveryResponse struct {
	Code int                  `json:"code"` // 响应码
	Data []DeliveryResultData `json:"data"` // 每条发货记录的处理结果
}

// DeliveryResultData 发货记录处理结果
type DeliveryResultData struct {
	TransactionCount int    `json:"transactionCount"` // 交易次数
	DataCreated      string `json:"dataCreated"`      // 处理状态
	ArrayLength      int    `json:"arrayLength"`
	Exists           string `json:"exists"`
}

// InventoryRequest 库存数据上报请求结构体
type InventoryRequest struct {
	Key              string          `json:"key"`
	User             string          `json:"user"`
	Password         string          `json:"password"`
	TransactionCount int             `json:"transactionCount"`
	MessageID        string          `json:"messageId"`
	Inventories      []InventoryData `json:"inventoryData"`
}

// InventoryData 库存数据
type InventoryData struct {
	FunctionCode    string  `json:"functionCode"` // 功能码
	TankNumber      string  `json:"tankNumber"`   // 油罐编号
	CurrentDateTime string  `json:"currentDateTime"`
	ProductCode     string  `json:"productCode"` // 油品代码
	TankStatus      int     `json:"tankStatus"`  // 油罐状态
	Volume          float64 `json:"volume"`
	TcVolume        float64 `json:"tcVolume"`
	Ullage          float64 `json:"ullage"`
	Height          float64 `json:"height"`
	Water           float64 `json:"water"`
	Temperature     float64 `json:"temperature"`
	WaterVolume     float64 `json:"waterVolume"`
	SiteID          string  `json:"siteId"`
	DeviceID        string  `json:"deviceId"`
}

// InventoryResponse 库存数据上报响应结构体
type InventoryResponse struct {
	Code int                   `json:"code"` // 响应码
	Data []InventoryResultData `json:"data"` // 每条库存记录的处理结果
}

// InventoryResultData 库存记录处理结果
type InventoryResultData struct {
	TransactionCount int    `json:"transactionCount"` // 交易次数
	DataCreated      string `json:"dataCreated"`      // 处理状态
	ArrayLength      int    `json:"arrayLength"`
	Exists           string `json:"exists"`
}

// ErrorResponse 通用错误响应结构体
type ErrorResponse struct {
	Code    int    `json:"code"`    // 错误码
	Message string `json:"desc"`    // 错误消息
	Details string `json:"details"` // 详细错误信息（可选）
}
