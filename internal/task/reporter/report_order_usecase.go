package reporter

import (
	"context"
	"fmt"
	"log"
	"time"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service"
)

// ReportTransactions 执行交易数据上报的核心流程
func (uc *ReporterUsecase) ReportTransactions(ctx context.Context, cfg *config.ReporterConfig) error {
	log.Println("开始执行交易数据上报任务")

	// 检查依赖是否已初始化
	if uc.reportRecordRepo == nil {
		log.Println("交易上报任务跳过：数据库依赖尚未初始化")
		return nil
	}

	if uc.erpClient == nil {
		log.Println("交易上报任务跳过：ERP客户端依赖尚未初始化")
		return nil
	}

	// a. 调用 repo.FindPending 获取一批待上报数据
	batchSize := 20
	if cfg != nil {
		batchSize = cfg.BatchSize
	}
	pendingRecords, err := uc.reportRecordRepo.FindPending(ctx, batchSize)
	if err != nil {
		return fmt.Errorf("查询待上报数据失败: %w", err)
	}

	// b. 如果列表为空，记录一条 "No pending transactions to report." 日志并返回
	if len(pendingRecords) == 0 {
		log.Println("No pending transactions to report.")
		return nil
	}

	log.Printf("找到 %d 条待上报的交易记录", len(pendingRecords))

	orderIds := make([]string, 0, len(pendingRecords))
	recordIDs := make([]int64, 0, len(pendingRecords))
	for _, record := range pendingRecords {
		orderIds = append(orderIds, record.BusinessID)
		recordIDs = append(recordIDs, record.ID)
	}
	orders, err := uc.orderRepo.GetByIDs(ctx, orderIds)
	if err != nil {
		return fmt.Errorf("查询订单失败: %w", err)
	}

	// c. 将 ent.ReportRecord 列表转换为 types.DispenserTransactionRequest
	transactionRequest := uc.ConvertToTransactionRequest(orders)

	// d. 调用 adapter.PostTransactions
	log.Printf("准备上报 %d 条交易数据到 ERP", len(transactionRequest.Transactions))

	response, err := uc.erpClient.PostTransactions(ctx, transactionRequest)
	if err != nil {
		log.Printf("上报交易数据失败1: %v", err)
		// e. 处理失败情况：更新状态为 failed，增加重试次数
		return uc.updateFailedRecords(ctx, recordIDs, err)
	}

	// 检查 ERP 响应
	if response == nil {
		err := fmt.Errorf("ERP 返回空响应")
		log.Printf("上报交易数据失败2: %v", err)
		return uc.updateFailedRecords(ctx, recordIDs, err)
	}

	log.Printf("ERP 响应: 代码=%d, 消息=%s", response.Code, response.Message)

	// 根据响应码处理结果
	switch response.Code {
	case 200, 210: // 成功或重复数据（都视为成功）
		// e. 处理成功情况：调用 repo.UpdateReportingStatus 将这批数据的状态更新为 success
		err = uc.reportRecordRepo.UpdateReportingStatus(ctx, recordIDs, "success", 0)
		if err != nil {
			return fmt.Errorf("更新上报状态失败: %w", err)
		}
		log.Printf("成功上报 %d 条交易数据", len(recordIDs))
		return nil
	default:
		// 其他响应码视为业务错误
		err := fmt.Errorf("ERP 业务错误 (代码: %d): %s", response.Code, response.Message)
		log.Printf("上报交易数据失败3: %v", err)
		return uc.updateFailedRecords(ctx, recordIDs, err)
	}
}

// ConvertToTransactionRequest 将订单列表转换为 ERP API 请求格式（公共方法，供测试使用）
func (uc *ReporterUsecase) ConvertToTransactionRequest(orders []*order_service.Order) *erp.DispenserTransactionRequest {
	cfg := config.GetConfig()

	// 使用已初始化的集成数据转换器，提高性能和一致性
	transactions := make([]erp.TransactionDataItem, 0, len(orders))
	for _, order := range orders {
		item, err := uc.converter.ConvertOrderToTransactionItem(order)
		if err != nil {
			log.Printf("[ERROR] Failed to convert order %s: %v", order.ID, err)
			continue // 跳过无法转换的订单
		}
		transactions = append(transactions, item)
	}

	return &erp.DispenserTransactionRequest{
		Key:              cfg.ERPAPI.TransactionUser.Key,
		User:             cfg.ERPAPI.TransactionUser.User,
		Password:         cfg.ERPAPI.TransactionUser.Password,
		TransactionCount: len(transactions),
		MessageID:        fmt.Sprintf("batch_%d_%d", len(transactions), time.Now().Unix()),
		Transactions:     transactions,
	}
}

// ConvertToTransactionRequestWithValidation 带完整验证的转换方法
func (uc *ReporterUsecase) ConvertToTransactionRequestWithValidation(orders []*order_service.Order) (*erp.DispenserTransactionRequest, []string) {
	cfg := config.GetConfig()
	var allIssues []string

	transactions := make([]erp.TransactionDataItem, 0, len(orders))
	for _, order := range orders {
		item, err := uc.converter.ConvertOrderToTransactionItem(order)
		if err != nil {
			log.Printf("[ERROR] Failed to convert order %s: %v", order.ID, err)
			continue // 跳过无法转换的订单
		}

		// 执行完整的数据质量验证
		if issues := uc.converter.ValidateTransactionItem(&item); len(issues) > 0 {
			orderIssues := fmt.Sprintf("订单 %s: %v", order.ID, issues)
			allIssues = append(allIssues, orderIssues)
			log.Printf("数据质量警告 - %s", orderIssues)
		}

		transactions = append(transactions, item)
	}

	request := &erp.DispenserTransactionRequest{
		Key:              cfg.ERPAPI.TransactionUser.Key,
		User:             cfg.ERPAPI.TransactionUser.User,
		Password:         cfg.ERPAPI.TransactionUser.Password,
		TransactionCount: len(transactions),
		MessageID:        fmt.Sprintf("batch_%d_%d", len(transactions), time.Now().Unix()),
		Transactions:     transactions,
	}

	return request, allIssues
}

// updateFailedRecords 处理上报失败的记录
func (uc *ReporterUsecase) updateFailedRecords(ctx context.Context, recordIDs []int64, err error) error {
	log.Printf("上报失败，更新 %d 条记录状态为 failed: %v", len(recordIDs), err)

	// 增加重试次数 (暂时设为1，后续可以根据当前重试次数递增)
	updateErr := uc.reportRecordRepo.UpdateReportingStatus(ctx, recordIDs, "failed", 1)
	if updateErr != nil {
		return fmt.Errorf("更新失败记录状态时出错: %w", updateErr)
	}

	return fmt.Errorf("交易数据上报错误: %w", err)
}
