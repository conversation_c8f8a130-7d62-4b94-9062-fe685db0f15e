package reporter

import (
	"context"
	"fmt"
	"log"
	"time"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/mapping"
	sqlmodels "gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/sql"
)

// NewReporterService 基于新架构的上报服务
type NewReporterService struct {
	odDbClient  *order_service.Client
	fieldMapper *mapping.FieldMapper
	erpClient   *erp.ERPClient
	erpConfig   *config.ERPAPIConfig
	logger      *log.Logger
}

// NewReporterServiceConfig 新上报服务配置
type NewReporterServiceConfig struct {
	OdDbClient    *order_service.Client
	MappingConfig *mapping.Config
	ERPClient     *erp.ERPClient
	ERPConfig     *config.ERPAPIConfig
	Logger        *log.Logger
}

// NewNewReporterService 创建新架构的上报服务实例
func NewNewReporterService(cfg *NewReporterServiceConfig) *NewReporterService {
	if cfg.Logger == nil {
		cfg.Logger = log.Default()
	}

	return &NewReporterService{
		odDbClient:  cfg.OdDbClient,
		fieldMapper: mapping.NewFieldMapper(cfg.MappingConfig),
		erpClient:   cfg.ERPClient,
		erpConfig:   cfg.ERPConfig,
		logger:      cfg.Logger,
	}
}

// ReportOrdersResult 上报结果
type ReportOrdersResult struct {
	TotalOrders     int           `json:"total_orders"`
	SuccessCount    int           `json:"success_count"`
	FailureCount    int           `json:"failure_count"`
	SuccessRate     float64       `json:"success_rate"`
	TotalDuration   time.Duration `json:"total_duration"`
	QueryDuration   time.Duration `json:"query_duration"`
	ConvertDuration time.Duration `json:"convert_duration"`
	APIDuration     time.Duration `json:"api_duration"`
	ErrorMessages   []string      `json:"error_messages"`
}

// ReportOrdersByIDs 根据订单ID列表上报交易数据
func (nrs *NewReporterService) ReportOrdersByIDs(ctx context.Context, orderIDs []string) (*ReportOrdersResult, error) {
	if len(orderIDs) == 0 {
		return &ReportOrdersResult{}, nil
	}

	startTime := time.Now()
	result := &ReportOrdersResult{
		TotalOrders:   len(orderIDs),
		ErrorMessages: make([]string, 0),
	}

	nrs.logger.Printf("🚀 开始上报 %d 个订单", len(orderIDs))

	// 1. 批量查询订单数据
	queryStart := time.Now()
	orders, err := nrs.batchQueryOrders(ctx, orderIDs)
	if err != nil {
		return nil, fmt.Errorf("批量查询订单失败: %w", err)
	}
	result.QueryDuration = time.Since(queryStart)

	nrs.logger.Printf("✅ 查询完成，耗时: %v，获得 %d 个订单", result.QueryDuration, len(orders))

	// 2. 转换为TransactionRecord
	transactions := nrs.convertOrdersToTransactionRecords(orders)

	// 3. 批量转换为ERP格式
	convertStart := time.Now()
	request := nrs.fieldMapper.ConvertToERPRequest(
		transactions,
		nrs.erpConfig.TransactionUser.Key,
		nrs.erpConfig.TransactionUser.User,
		nrs.erpConfig.TransactionUser.Password,
	)
	result.ConvertDuration = time.Since(convertStart)

	nrs.logger.Printf("✅ 数据转换完成，耗时: %v，转换 %d 条交易", result.ConvertDuration, len(request.Transactions))

	// 4. 发送到ERP API
	apiStart := time.Now()
	resp, err := nrs.erpClient.PostTransactions(ctx, request)
	result.APIDuration = time.Since(apiStart)

	if err != nil {
		result.FailureCount = len(orderIDs)
		result.ErrorMessages = append(result.ErrorMessages, fmt.Sprintf("ERP API调用失败: %v", err))
		nrs.logger.Printf("❌ ERP API调用失败: %v", err)
	} else {
		// 5. 处理响应
		switch resp.Code {
		case 200:
			result.SuccessCount = len(orderIDs)
			nrs.logger.Printf("✅ 上报成功，响应码: %d", resp.Code)
		case 210:
			result.SuccessCount = len(orderIDs)
			nrs.logger.Printf("⚠️  重复数据（视为成功），响应码: %d", resp.Code)
		case 1001, 401:
			result.FailureCount = len(orderIDs)
			result.ErrorMessages = append(result.ErrorMessages, fmt.Sprintf("认证失败，响应码: %d", resp.Code))
			nrs.logger.Printf("❌ 认证失败，响应码: %d", resp.Code)
		default:
			result.FailureCount = len(orderIDs)
			result.ErrorMessages = append(result.ErrorMessages, fmt.Sprintf("其他错误，响应码: %d, 消息: %s", resp.Code, resp.Message))
			nrs.logger.Printf("❓ 其他响应码: %d，消息: %s", resp.Code, resp.Message)
		}
	}

	// 6. 计算统计信息
	result.TotalDuration = time.Since(startTime)
	if result.TotalOrders > 0 {
		result.SuccessRate = float64(result.SuccessCount) / float64(result.TotalOrders) * 100
	}

	nrs.logger.Printf("📊 上报完成 - 成功: %d, 失败: %d, 成功率: %.1f%%, 总耗时: %v",
		result.SuccessCount, result.FailureCount, result.SuccessRate, result.TotalDuration)

	return result, nil
}

// ReportUnreportedOrders 上报未上报的订单
func (nrs *NewReporterService) ReportUnreportedOrders(ctx context.Context, limit int) (*ReportOrdersResult, error) {
	// 1. 查询未上报的订单ID
	orderIDs, err := nrs.getUnreportedOrderIDs(ctx, limit)
	if err != nil {
		return nil, fmt.Errorf("获取未上报订单ID失败: %w", err)
	}

	if len(orderIDs) == 0 {
		nrs.logger.Println("📭 没有找到未上报的订单")
		return &ReportOrdersResult{}, nil
	}

	// 2. 上报这些订单
	return nrs.ReportOrdersByIDs(ctx, orderIDs)
}

// batchQueryOrders 批量查询订单数据
func (nrs *NewReporterService) batchQueryOrders(ctx context.Context, orderIDs []string) ([]*order_service.Order, error) {
	// 使用ent的批量查询，包含关联的燃油交易数据
	orders := make([]*order_service.Order, 0, len(orderIDs))

	// 分批查询，避免单次查询过多数据
	batchSize := 50
	for i := 0; i < len(orderIDs); i += batchSize {
		end := i + batchSize
		if end > len(orderIDs) {
			end = len(orderIDs)
		}

		batchIDs := orderIDs[i:end]

		// 使用ent的标准predicate而不是自定义函数
		batchOrders, err := nrs.odDbClient.Order.Query().
			Where(order.IDIn(batchIDs...)).
			WithFuelTransactionLink(func(q *order_service.FuelTransactionOrderLinkQuery) {
				q.WithFuelTransaction()
			}).
			All(ctx)

		if err != nil {
			return nil, fmt.Errorf("查询订单批次失败 (批次 %d-%d): %w", i, end-1, err)
		}

		orders = append(orders, batchOrders...)
	}

	return orders, nil
}

// convertOrdersToTransactionRecords 将订单转换为TransactionRecord格式
func (nrs *NewReporterService) convertOrdersToTransactionRecords(orders []*order_service.Order) []sqlmodels.TransactionRecord {
	records := make([]sqlmodels.TransactionRecord, 0, len(orders))

	for _, order := range orders {
		record := sqlmodels.TransactionRecord{
			OrderID:     order.ID,
			OrderNumber: fmt.Sprintf("ORD%s", order.ID),
			StationID:   order.StationID,
			TotalAmount: order.TotalAmount,
			FinalAmount: order.TotalAmount,
			CreatedAt:   order.CreatedAt,
		}

		// 可选字段
		if order.EmployeeNo != nil {
			record.EmployeeNo = order.EmployeeNo
		}
		if order.CustomerName != nil {
			record.CustomerName = order.CustomerName
		}
		if order.StaffCardID != nil {
			record.StaffCardID = order.StaffCardID
		}

		// 燃油交易信息
		if order.Edges.FuelTransactionLink != nil &&
			order.Edges.FuelTransactionLink.Edges.FuelTransaction != nil {
			ft := order.Edges.FuelTransactionLink.Edges.FuelTransaction
			record.TransactionNumber = &ft.PumpID
			record.PumpID = &ft.PumpID
			record.NozzleID = &ft.NozzleID
			record.FuelType = &ft.FuelType
			record.UnitPrice = &ft.UnitPrice
			record.Volume = &ft.Volume
			record.TransactionAmount = &ft.Amount

			if ft.StartTotalizer != nil {
				record.StartTotalizer = ft.StartTotalizer
			}
			if ft.EndTotalizer != nil {
				record.EndTotalizer = ft.EndTotalizer
			}
			if ft.MemberCardID != nil {
				record.MemberCardID = ft.MemberCardID
			}
			if ft.NozzleEndTime != nil {
				record.NozzleEndTime = ft.NozzleEndTime
			}
			if ft.NozzleStartTime != nil {
				record.NozzleStartTime = ft.NozzleStartTime
			}

		}

		records = append(records, record)
	}

	return records
}

// getUnreportedOrderIDs 获取未上报的订单ID (简化实现)
func (nrs *NewReporterService) getUnreportedOrderIDs(ctx context.Context, limit int) ([]string, error) {
	// 简化实现：直接查询completed状态的订单
	// 在实际应用中，这里应该排除已上报的订单
	orders, err := nrs.odDbClient.Order.Query().
		Where(order.Status("completed")).
		Limit(limit).
		All(ctx)

	if err != nil {
		return nil, err
	}

	orderIDs := make([]string, len(orders))
	for i, order := range orders {
		orderIDs[i] = order.ID
	}

	return orderIDs, nil
}
