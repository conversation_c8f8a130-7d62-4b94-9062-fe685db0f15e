package reporter

// ERP字段映射常量
const (
	// 交易状态映射
	TransactionStatusCompleted = "completed"
	TransactionStatusPending   = "pending"

	// 车辆类型映射 - 根据业务规则修正
	VehicleTypeDinas = 1
	VehicleTypeTruck = 2
	VehicleTypeCar   = 3
	VehicleTypeMotor = 4

	// 产品类型映射
	ProductIDPertamax      = "PERTAMAX"
	ProductIDPertalite     = "PERTALITE"
	ProductIDPertamaxTurbo = "PERTAMAX_TURBO"
	ProductIDPertamina     = "PERTAMINA_DEX"
	ProductIDDiesel        = "SOLAR"

	// 默认值常量
	DefaultSiteID        = "SITE001"
	DefaultDispenserNum  = 1
	DefaultNozzleNum     = 1
	DefaultOperatorID    = "OP001"
	DefaultMessageHeader = ""     // 固定值null (空字符串)
	DefaultCategory      = "Fuel" // 固定值"fuel"
	DefaultAreaSite      = "Jakarta"
	DefaultReprint       = 0
	DefaultPromotionType = "None"
	DefaultTxnLength     = 5
)
