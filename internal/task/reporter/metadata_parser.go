package reporter

import (
	"encoding/json"
	"log"
	"strconv"
	"strings"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
)

// MetadataParser 负责解析order.metadata JSON字段
// 设计原则：纯函数，无副作用，可预测的输入输出
type MetadataParser struct{}

// OrderMetadata 订单元数据结构
// 使用指针类型支持null值，便于区分未设置和空值
type OrderMetadata struct {
	VehicleType   *int    `json:"vehicle_type"`
	VehicleID     *string `json:"vehicle_id"`
	VehicleGroup  *string `json:"vehicle_group"`
	DeviceID      *string `json:"device_id"`
	Reprint       *int    `json:"reprint"`
	CustomerPhone *string `json:"customer_phone"`
	Email         *string `json:"email"`
	Gender        *string `json:"gender"`
	DOB           *string `json:"dob"`
	MemberID      *string `json:"member_id"`
	MemberRegTime *string `json:"member_reg_time"`

	// 新增：ERP信息结构（优先级最高）
	ERPInfo *ERPInfo `json:"erp_info"`
}

// ERPInfo ERP相关信息结构体
// 这个结构体映射metadata中的erp_info字段，包含完整的ERP数据
type ERPInfo struct {
	// 设备信息
	DispenserNumber *int    `json:"dispenserNumber"`
	NozzleNumber    *int    `json:"nozzleNumber"`
	DeviceID        *string `json:"deviceID"`
	SiteID          *string `json:"siteID"`

	// 车辆信息
	VehicleType        *int    `json:"vehicleType"`
	VehicleID          *string `json:"vehicleID"`
	IDVehicleTypeGroup *string `json:"iD_VehicleTypeGroup"`

	// 产品信息
	ProductID      *string `json:"productID"`
	IDProductGroup *string `json:"iD_ProductGroup"`
	Category       *string `json:"category"`

	// 操作员和客户信息
	OperatorID      *string `json:"operatorID"`
	CustomerName    *string `json:"customer_Name"`
	CustomerPhoneNo *string `json:"customer_Phone_No"`
	Email           *string `json:"email"`
	Gender          *string `json:"gender"`
	DOB             *string `json:"dob"`

	// 交易信息
	TransactionID     *string `json:"transactionID"`
	SlipNumber        *string `json:"slipNumber"`
	TransactionDate   *string `json:"transactionDate"`
	TransactionLength *int    `json:"transactionLength"`

	// 金额和折扣信息
	Amount                *float64 `json:"amount"`
	Price                 *float64 `json:"price"`
	Volume                *float64 `json:"volume"`
	PercentDiscount       *float64 `json:"percent_Discount"`
	AmountDiscount        *float64 `json:"amount_Discount"`
	AmountPercentDiscount *float64 `json:"amount_Percent_Discount"`
	FinalDiscount         *float64 `json:"final_Discount"`
	FlagItemPromotion     *int     `json:"flag_Item_Promotion"`
	PromotionType         *string  `json:"promotion_Type"`

	// 计数器和RFID信息
	TotalizerStart *float64 `json:"totalizerStart"`
	TotalizerEnd   *float64 `json:"totalizerEnd"`
	RFIDVehicleID  *string  `json:"rfidVehicleID"`

	// 附加字段
	FieldTambahan1 *string `json:"field_Tambahan_1"`
	FieldTambahan2 *string `json:"field_Tambahan_2"`
	FieldTambahan3 *string `json:"field_Tambahan_3"`
	AreaSite       *string `json:"area_Site"`

	// 其他控制字段
	Reprint       *int     `json:"reprint"`
	MessageHeader *string  `json:"message_Header"`
	Point         *int     `json:"point"`
	Voucher       *float64 `json:"voucher"`
}

// NewMetadataParser 创建MetadataParser实例
// 纯函数：无状态，可以多次调用
func NewMetadataParser() *MetadataParser {
	return &MetadataParser{}
}

// ParseMetadata 解析metadata map到结构化数据
// 纯函数：给定相同输入，总是返回相同输出，无副作用
func (mp *MetadataParser) ParseMetadata(metadataMap map[string]interface{}) *OrderMetadata {
	// 处理nil输入
	if metadataMap == nil {
		return &OrderMetadata{}
	}

	// 预处理metadata，修复类型不兼容的字段
	processedMap := mp.preprocessMetadata(metadataMap)

	// 通过JSON序列化/反序列化实现类型转换
	// 这样可以利用Go的JSON包处理类型转换（如string "1" -> int 1）
	jsonBytes, err := json.Marshal(processedMap)
	if err != nil {
		// 记录错误但继续尝试处理
		// 在生产环境中可以考虑使用proper logging
		// log.Printf("[WARN] Metadata JSON marshal failed: %v", err)
		return &OrderMetadata{}
	}

	var metadata OrderMetadata
	if err := json.Unmarshal(jsonBytes, &metadata); err != nil {
		// 记录错误但继续尝试处理
		log.Printf("[DEBUG] Metadata JSON unmarshal failed: %v, raw data: %s", err, string(jsonBytes))
		return &OrderMetadata{}
	}

	// 调试：检查 ERP info 是否成功解析
	if metadata.ERPInfo != nil {
		log.Printf("[DEBUG] ERP Info successfully parsed - DispenserNumber: %v, NozzleNumber: %v",
			metadata.ERPInfo.DispenserNumber, metadata.ERPInfo.NozzleNumber)
	} else {
		log.Printf("[DEBUG] ERP Info is nil after parsing")
	}

	return &metadata
}

// preprocessMetadata 预处理metadata，修复类型不兼容的问题
func (mp *MetadataParser) preprocessMetadata(metadataMap map[string]interface{}) map[string]interface{} {
	// 创建一个副本避免修改原始数据
	processed := make(map[string]interface{})
	for k, v := range metadataMap {
		processed[k] = v
	}

	// 处理vehicle_type字段：如果是字符串，移除它（让erp_info中的vehicleType生效）
	if vehicleType, exists := processed["vehicle_type"]; exists {
		if _, isString := vehicleType.(string); isString {
			// 删除字符串类型的vehicle_type，避免JSON反序列化错误
			delete(processed, "vehicle_type")
		}
	}

	// 处理 erp_info 中的字符串数字字段
	if erpInfo, exists := processed["erp_info"]; exists {
		if erpMap, ok := erpInfo.(map[string]interface{}); ok {
			// 处理 dispenserNumber：字符串转整数
			if dispenserStr, exists := erpMap["dispenserNumber"]; exists {
				if str, ok := dispenserStr.(string); ok {
					if num, err := strconv.Atoi(str); err == nil {
						erpMap["dispenserNumber"] = num
						log.Printf("[DEBUG] Converted dispenserNumber from string '%s' to int %d", str, num)
					}
				}
			}

			// 处理 nozzleNumber：字符串转整数
			if nozzleStr, exists := erpMap["nozzleNumber"]; exists {
				if str, ok := nozzleStr.(string); ok {
					if num, err := strconv.Atoi(str); err == nil {
						erpMap["nozzleNumber"] = num
						log.Printf("[DEBUG] Converted nozzleNumber from string '%s' to int %d", str, num)
					}
				}
			}
		}
	}

	return processed
}

// FillVehicleFields 填充车辆相关字段
// 纯函数原则：只修改传入的item，不访问外部状态
func (mp *MetadataParser) FillVehicleFields(item *erp.TransactionDataItem, metadata *OrderMetadata) {
	// 只有当metadata中有值时才更新，保持原有默认值
	if metadata.VehicleType != nil {
		item.VehicleType = *metadata.VehicleType
	}

	if metadata.VehicleGroup != nil {
		item.IDVehicleTypeGroup = *metadata.VehicleGroup
	}

	if metadata.DeviceID != nil {
		item.DeviceID = *metadata.DeviceID
	}

	if metadata.Reprint != nil {
		item.Reprint = *metadata.Reprint
	}
}

// FillCustomerFields 填充客户相关字段
// 纯函数原则：只修改传入的item，不访问外部状态
func (mp *MetadataParser) FillCustomerFields(item *erp.TransactionDataItem, metadata *OrderMetadata) {
	// 只有当metadata中有值时才更新
	if metadata.CustomerPhone != nil {
		item.CustomerPhoneNo = *metadata.CustomerPhone
	}

	if metadata.Email != nil {
		item.Email = *metadata.Email
	}

	if metadata.Gender != nil {
		item.Gender = *metadata.Gender
	}

	if metadata.DOB != nil {
		item.DOB = metadata.DOB // DOB字段本身就是*string类型
	}

	if metadata.MemberID != nil {
		item.DEXROWID = *metadata.MemberID
	}

	if metadata.MemberRegTime != nil {
		item.DEX_ROW_TS = *metadata.MemberRegTime
	}
}

// FillERPInfoFields 填充ERP信息字段（优先级最高）
// 这个方法会使用erp_info中的数据覆盖其他数据源的值
// 注意：核心交易数据（金额、价格、体积）不会被覆盖，以保护数据完整性
func (mp *MetadataParser) FillERPInfoFields(item *erp.TransactionDataItem, metadata *OrderMetadata) {
	if metadata.ERPInfo == nil {
		return
	}

	erpInfo := metadata.ERPInfo

	// 设备信息 - 优先级最高
	if erpInfo.DispenserNumber != nil {
		item.DispenserNumber = *erpInfo.DispenserNumber
	}
	if erpInfo.NozzleNumber != nil {
		item.NozzleNumber = *erpInfo.NozzleNumber
	}
	if erpInfo.DeviceID != nil {
		item.DeviceID = *erpInfo.DeviceID
	}
	if erpInfo.SiteID != nil {
		item.SiteID = *erpInfo.SiteID
	}

	// 车辆信息 - 覆盖基础metadata
	if erpInfo.VehicleType != nil {
		item.VehicleType = *erpInfo.VehicleType
	}
	// VehicleID 强制使用 orders.license_plate，不被 ERP info 覆盖
	if erpInfo.VehicleID != nil && *erpInfo.VehicleID != "" {
		// 只有当前VehicleID为空或是生成的默认值时，才使用ERP info中的值
		if item.VehicleID == "" || strings.HasPrefix(item.VehicleID, "VEH_") {
			item.VehicleID = *erpInfo.VehicleID
		}
	}
	if erpInfo.IDVehicleTypeGroup != nil {
		item.IDVehicleTypeGroup = *erpInfo.IDVehicleTypeGroup
	}

	// 产品信息 - ProductID 直接使用 fuel_transaction.fuel_grade，不被 ERP info 覆盖
	// if erpInfo.ProductID != nil {
	// 	// 检查当前ProductID是否是从fuel_grade设置的有效值
	// 	// 如果当前ProductID不是空且不是通过映射得到的默认值，则保持不变
	// 	mappedERPProductID := mp.mapProductID(*erpInfo.ProductID)
	// 	if item.ProductID == "" || item.ProductID == mappedERPProductID {
	// 		// 当前ProductID为空或与ERP映射值相同，使用ERP映射值
	// 		item.ProductID = mappedERPProductID
	// 		log.Printf("[DEBUG] Using ERP ProductID fallback: %s -> %s", *erpInfo.ProductID, mappedERPProductID)
	// 	} else {
	// 		// 当前ProductID是从fuel_grade设置的，保持不变
	// 		log.Printf("[DEBUG] Preserving existing ProductID: %s (ERP value would be: %s)", item.ProductID, *erpInfo.ProductID)
	// 	}
	// }
	// IDProductGroup 保持固定值 "fuel" - 单一来源策略
	// if erpInfo.IDProductGroup != nil {
	// 	item.IDProductGroup = *erpInfo.IDProductGroup
	// }

	// 操作员信息 - 单一来源：ERP信息
	if erpInfo.OperatorID != nil {
		item.OperatorID = *erpInfo.OperatorID
		log.Printf("[DEBUG] Set OperatorID from ERP info: %s", item.OperatorID)
	}

	// 客户信息 - 直接使用 orders 字段，不被 ERP info 覆盖
	// if erpInfo.CustomerName != nil {
	// 	// 只有当前CustomerName为空或是默认值时，才使用ERP info作为保底
	// 	if item.CustomerName == "" || item.CustomerName == "Guest Customer" {
	// 		item.CustomerName = *erpInfo.CustomerName
	// 		log.Printf("[DEBUG] Using ERP fallback for CustomerName: %s", *erpInfo.CustomerName)
	// 	} else {
	// 		log.Printf("[DEBUG] Preserving existing CustomerName: %s (ERP value: %s)",
	// 			item.CustomerName, *erpInfo.CustomerName)
	// 	}
	// }
	// if erpInfo.CustomerPhoneNo != nil {
	// 	// 只有当前CustomerPhoneNo为空时，才使用ERP info作为保底
	// 	if item.CustomerPhoneNo == "" {
	// 		item.CustomerPhoneNo = *erpInfo.CustomerPhoneNo
	// 		log.Printf("[DEBUG] Using ERP fallback for CustomerPhoneNo: %s", *erpInfo.CustomerPhoneNo)
	// 	} else {
	// 		log.Printf("[DEBUG] Preserving existing CustomerPhoneNo: %s (ERP value: %s)",
	// 			item.CustomerPhoneNo, *erpInfo.CustomerPhoneNo)
	// 	}
	// }
	if erpInfo.Email != nil {
		item.Email = *erpInfo.Email
	}
	if erpInfo.Gender != nil {
		item.Gender = *erpInfo.Gender
	}
	if erpInfo.DOB != nil {
		item.DOB = erpInfo.DOB
	}

	if erpInfo.TransactionDate != nil {
		// // 解析ERP时间并转换为本地时区
		// if parsedTime, err := time.Parse(time.RFC3339, *erpInfo.TransactionDate); err == nil {
		// 	localTime := parsedTime.In(getLocalTimezone())
		// 	item.TransactionDate = localTime.Format("2006-01-02 15:04:05")
		// 	log.Printf("[DEBUG] ERP TransactionDate converted to local time: %s (original: %s)",
		// 		item.TransactionDate, *erpInfo.TransactionDate)
		// } else {
		// 	// 如果解析失败，直接使用原值
		// 	item.TransactionDate = *erpInfo.TransactionDate
		// 	log.Printf("[DEBUG] ERP TransactionDate parse failed, using original: %s", *erpInfo.TransactionDate)
		// }
	}
	// if erpInfo.TransactionLength != nil {
	// 	item.TransactionLength = *erpInfo.TransactionLength
	// }

	// ===== 核心金额信息保护区域 =====
	// 注意：为了保护数据完整性，以下核心交易字段只有在ERP数据有效时才覆盖
	// 无效的ERP数据（0值）不应该覆盖正确的燃油交易数据

	// 只有当ERP数据有效（大于0）时，才覆盖现有数据
	if erpInfo.Amount != nil && *erpInfo.Amount > 0 {
		item.Amount = *erpInfo.Amount
	}
	if erpInfo.Price != nil && *erpInfo.Price > 0 {
		item.Price = *erpInfo.Price
	}
	if erpInfo.Volume != nil && *erpInfo.Volume > 0 {
		item.Volume = *erpInfo.Volume
	}

	// 计数器信息 - 只在ERP数据有效时覆盖
	if erpInfo.TotalizerStart != nil && *erpInfo.TotalizerStart > 0 {
		item.TotalizerStart = *erpInfo.TotalizerStart
	}
	if erpInfo.TotalizerEnd != nil && *erpInfo.TotalizerEnd > 0 {
		item.TotalizerEnd = *erpInfo.TotalizerEnd
	}
	// ===== 核心金额信息保护区域结束 =====

	// 折扣信息 - 可以被覆盖
	if erpInfo.PercentDiscount != nil {
		item.PercentDiscount = *erpInfo.PercentDiscount
	}
	if erpInfo.AmountDiscount != nil {
		item.AmountDiscount = *erpInfo.AmountDiscount
	}
	if erpInfo.AmountPercentDiscount != nil {
		item.AmountPercentDiscount = *erpInfo.AmountPercentDiscount
	}
	if erpInfo.FinalDiscount != nil {
		item.FinalDiscount = *erpInfo.FinalDiscount
	}
	if erpInfo.FlagItemPromotion != nil {
		item.FlagItemPromotion = *erpInfo.FlagItemPromotion
	}
	if erpInfo.PromotionType != nil {
		item.PromotionType = *erpInfo.PromotionType
	}

	// RFID信息
	if erpInfo.RFIDVehicleID != nil {
		item.RFIDVehicleID = *erpInfo.RFIDVehicleID
	}

	// 附加字段 - 这些是我们最需要的！
	if erpInfo.FieldTambahan1 != nil {
		item.FieldTambahan1 = *erpInfo.FieldTambahan1
	}
	if erpInfo.FieldTambahan2 != nil {
		item.FieldTambahan2 = *erpInfo.FieldTambahan2
	}
	if erpInfo.FieldTambahan3 != nil {
		item.FieldTambahan3 = *erpInfo.FieldTambahan3
	}
	// if erpInfo.AreaSite != nil {
	// 	item.AreaSite = *erpInfo.AreaSite
	// }
	item.AreaSite = "Area 1"
	// 控制字段
	if erpInfo.Reprint != nil {
		item.Reprint = *erpInfo.Reprint
	}
	if erpInfo.MessageHeader != nil {
		item.MessageHeader = *erpInfo.MessageHeader
	}
	if erpInfo.Point != nil {
		item.Point = *erpInfo.Point
	}
	if erpInfo.Voucher != nil {
		item.Voucher = *erpInfo.Voucher
	}
}

// FillAllFields 填充所有metadata字段的便捷方法
// 组合函数：将多个纯函数组合成一个操作
// 注意：ERP信息具有最高优先级，会覆盖其他数据源
func (mp *MetadataParser) FillAllFields(item *erp.TransactionDataItem, metadataMap map[string]interface{}) {
	metadata := mp.ParseMetadata(metadataMap)

	// 先填充基础字段
	mp.FillVehicleFields(item, metadata)
	mp.FillCustomerFields(item, metadata)

	// 最后填充ERP信息（优先级最高，会覆盖基础字段）
	mp.FillERPInfoFields(item, metadata)
}

// mapProductID 映射产品ID（处理数字代码到产品名称的转换）
// 注意：这是一个简化版本，主要的数据库查询逻辑在 IntegratedDataConverter.mapFuelTypeToProductID 中
func (mp *MetadataParser) mapProductID(productID string) string {
	// 简单的数字代码映射，保持向后兼容
	switch productID {
	case "101":
		return "PERTALITE"
	case "102":
		return "PERTAMAX"
	case "103":
		return "PERTAMAX_TURBO"
	case "104":
		return "PERTAMINA_DEX"
	case "105":
		return "SOLAR"
	default:
		// 如果已经是产品名称，转为大写并返回
		return strings.ToUpper(productID)
	}
}
