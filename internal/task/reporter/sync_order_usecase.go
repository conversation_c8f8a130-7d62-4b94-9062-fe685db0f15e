package reporter

import (
	"context"
	"fmt"
	"log"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/hos_reporter"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/hos_reporter/reportrecord"
)

func (uc *ReporterUsecase) syncOrder(ctx context.Context) error {
	log.Println("开始执行数据同步任务：同步订单数据到report_record表")

	// 检查依赖是否已初始化
	if uc.orderRepo == nil || uc.reportRecordRepo == nil {
		log.Println("数据同步任务跳过：数据库依赖尚未初始化")
		return nil
	}

	// 1. 获取已上报的订单ID
	reportRecords, err := uc.reportRecordRepo.GetByType(ctx, "transaction")
	if err != nil {
		return fmt.Errorf("查询已上报订单失败: %w", err)
	}

	reportRecordIds := make([]string, 0)
	for _, reportRecord := range reportRecords {
		reportRecordIds = append(reportRecordIds, reportRecord.BusinessID)
	}

	// 2. 从订单表中查询已支付的订单数据
	orders, err := uc.orderRepo.FindPaidNotReport(ctx, reportRecordIds)
	if err != nil {
		return fmt.Errorf("查询已支付订单失败: %w", err)
	}

	if len(orders) == 0 {
		log.Println("没有找到需要同步的已支付订单")
		return nil
	}

	log.Printf("找到 %d 个已支付订单，开始检查同步状态", len(orders))

	// 统计数据
	syncedCount := 0

	// 3. 遍历每个订单
	for _, order := range orders {
		// 4. 创建新的report_record记录
		reportRecord := &hos_reporter.ReportRecord{
			RecordType:          "transaction",                       // 记录类型为transaction
			BusinessID:          order.ID,                            // 业务数据ID为订单ID
			ReportingStatus:     reportrecord.ReportingStatusPending, // 初始状态为pending
			ReportingRetryCount: 0,                                   // 重试次数为0
			Priority:            1,                                   // 默认优先级
			SiteID:              fmt.Sprintf("%d", order.StationID),  // 站点ID来自订单的加油站ID
		}

		// 5. 将新记录插入到report_record表中
		err = uc.reportRecordRepo.Add(ctx, reportRecord)
		if err != nil {
			log.Printf("同步订单 %s 到report_record表失败: %v", order.ID, err)
			continue
		}

		syncedCount++
		log.Printf("成功同步订单 %s 到report_record表", order.ID)
	}

	log.Printf("数据同步任务完成：新同步 %d 条记录", syncedCount)
	return nil
}
