package reporter

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
)

// PaymentMethodInfo 支付方式信息
type PaymentMethodInfo struct {
	Code        string `json:"code"`
	DisplayName string `json:"display_name"`
	Type        string `json:"type"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID       string `json:"id"`
	FullName string `json:"full_name"`
	Username string `json:"username"`
}

// StationInfo 站点信息
type StationInfo struct {
	ID      int64  `json:"id"`
	Address string `json:"address"`
	Name    string `json:"name"`
}

// PromotionInfo 优惠信息
type PromotionInfo struct {
	PromotionType         string  `json:"promotion_type"`
	PromotionName         string  `json:"promotion_name"`
	PercentDiscount       float64 `json:"percent_discount"`
	AmountPercentDiscount float64 `json:"amount_percent_discount"`
	AmountDiscount        float64 `json:"amount_discount"`
	FlagItemPromotion     int     `json:"flag_item_promotion"`
}

// OilProductInfo 油品信息
type OilProductInfo struct {
	Code     string `json:"code"`
	Category string `json:"category"`
	Name     string `json:"name"`
}

// MemberInfo 会员信息
type MemberInfo struct {
	MemberID string `json:"member_id"`
	Phone    string `json:"phone"`
	Name     string `json:"name"`
}

// StaffCardInfo 员工卡信息
type StaffCardInfo struct {
	ID       string `json:"id"`
	CardNo   string `json:"card_no"`
	Name     string `json:"name"`
	Position string `json:"position"`
}

// DatabaseJoinResolverInterface 数据库联查解析器接口
type DatabaseJoinResolverInterface interface {
	ResolvePaymentMethod(orderID string) (*PaymentMethodInfo, error)
	ResolveUser(employeeID string) (*UserInfo, error)
	ResolveStation(stationID int64) (*StationInfo, error)
	ResolvePromotions(orderID string) ([]PromotionInfo, error)
	ResolveOilProduct(fuelType string) (*OilProductInfo, error)
	ResolveMemberPhone(memberID string) (*MemberInfo, error)
	ResolveStaffCard(staffCardID string) (*StaffCardInfo, error)
}

// DatabaseJoinResolverImpl 数据库联查解析器的实际实现
type DatabaseJoinResolverImpl struct {
	db *sql.DB
}

// NewDatabaseJoinResolverImpl 创建新的数据库联查解析器实现
func NewDatabaseJoinResolverImpl(db *sql.DB) DatabaseJoinResolverInterface {
	return &DatabaseJoinResolverImpl{db: db}
}

// ResolvePaymentMethod 解析支付方式信息
// 联查路径: orders.id → order_payments.order_id → payment_methods.payment_method → payment_methods.display_name
func (r *DatabaseJoinResolverImpl) ResolvePaymentMethod(orderID string) (*PaymentMethodInfo, error) {

	if orderID == "" {
		return nil, nil
	}
	ctx := context.Background()
	query := `
		SELECT
			pm.type as code,
			pm.display_name,
			pm.type
		FROM order_schema.orders o
		JOIN order_schema.order_payments op ON o.id = op.order_id
		JOIN payment_schema.payment_methods pm ON op.payment_method = pm.type
		WHERE o.id = $1 AND op.status = 'completed'
		ORDER BY op.created_at DESC
		LIMIT 1
	`

	var info PaymentMethodInfo
	err := r.db.QueryRowContext(ctx, query, orderID).Scan(
		&info.Code,
		&info.DisplayName,
		&info.Type,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to resolve payment method for order %s: %w", orderID, err)
	}

	return &info, nil
}

// ResolveUser 解析用户信息
// 联查路径: fuel_transactions.employee_id → core_schema.users.id → users.full_name
func (r *DatabaseJoinResolverImpl) ResolveUser(employeeID string) (*UserInfo, error) {
	if employeeID == "" {
		return nil, nil
	}

	ctx := context.Background()
	query := `
		SELECT 
			u.id::text,
			u.full_name,
			u.username
		FROM core_schema.users u
		WHERE u.id = $1::uuid
	`

	var info UserInfo
	err := r.db.QueryRowContext(ctx, query, employeeID).Scan(
		&info.ID,
		&info.FullName,
		&info.Username,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to resolve user for employee ID %s: %w", employeeID, err)
	}

	return &info, nil
}

// ResolveStation 解析站点信息
// 联查路径: orders.station_id → stations.id → stations.address
func (r *DatabaseJoinResolverImpl) ResolveStation(stationID int64) (*StationInfo, error) {
	if stationID <= 0 {
		return nil, nil
	}

	ctx := context.Background()
	// 注意：这里假设stations表存在于core_schema中，可能需要根据实际表结构调整
	query := `
		SELECT 
			s.id,
			COALESCE(s.address, '') as address,
			COALESCE(s.name, '') as name
		FROM core_schema.stations s
		WHERE s.id = $1
	`

	var info StationInfo
	err := r.db.QueryRowContext(ctx, query, stationID).Scan(
		&info.ID,
		&info.Address,
		&info.Name,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to resolve station for station ID %d: %w", stationID, err)
	}

	return &info, nil
}

// ResolvePromotions 解析优惠信息
// 联查路径: orders.id → order_promotions.order_id → promotion信息
func (r *DatabaseJoinResolverImpl) ResolvePromotions(orderID string) ([]PromotionInfo, error) {

	if orderID == "" {
		return []PromotionInfo{}, nil
	}

	ctx := context.Background()
	query := `
		SELECT
			COALESCE(op.promotion_name, op.promotion_type, '') as promotion_name,
			COALESCE(op.promotion_type, '') as promotion_type,
			COALESCE(op.discount_amount, 0) as discount_amount,
			CASE
				WHEN op.promotion_type = 'percentage' THEN
					COALESCE((op.metadata->>'percent_discount')::float, 0)
				ELSE 0
			END as percent_discount,
			CASE
				WHEN op.promotion_type = 'percentage' THEN
					COALESCE(op.discount_amount, 0)
				ELSE 0
			END as amount_percent_discount,
			-- 修复：AmountDiscount 应该等于 discount_amount，不管 promotion_type 是什么
			COALESCE(op.discount_amount, 0) as amount_discount
		FROM order_schema.order_promotions op
		WHERE op.order_id = $1
		ORDER BY op.created_at ASC
	`

	rows, err := r.db.QueryContext(ctx, query, orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to query promotions for order %s: %w", orderID, err)
	}
	defer rows.Close()

	var promotions []PromotionInfo
	for rows.Next() {
		var promo PromotionInfo
		var discountAmount float64

		err := rows.Scan(
			&promo.PromotionName,
			&promo.PromotionType,
			&discountAmount,
			&promo.PercentDiscount,
			&promo.AmountPercentDiscount,
			&promo.AmountDiscount,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan promotion row for order %s: %w", orderID, err)
		}

		// 设置是否有优惠标志
		promo.FlagItemPromotion = 1
		if discountAmount == 0 {
			promo.FlagItemPromotion = 0
		}

		promotions = append(promotions, promo)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating promotion rows for order %s: %w", orderID, err)
	}

	return promotions, nil
}

// ResolveOilProduct 解析油品信息
// 联查路径: fuel_transactions.fuel_type → oil_products.code → oil_products.category
func (r *DatabaseJoinResolverImpl) ResolveOilProduct(fuelType string) (*OilProductInfo, error) {
	if fuelType == "" {
		return nil, nil
	}

	ctx := context.Background()
	query := `
		SELECT 
			op.code,
			COALESCE(op.category, '') as category,
			COALESCE(op.name, '') as name
		FROM oil_schema.oil_products op
		WHERE UPPER(op.code) = UPPER($1)
		   OR UPPER(op.name) = UPPER($1)
		LIMIT 1
	`

	var info OilProductInfo
	err := r.db.QueryRowContext(ctx, query, fuelType).Scan(
		&info.Code,
		&info.Category,
		&info.Name,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to resolve oil product for fuel type %s: %w", fuelType, err)
	}

	return &info, nil
}

// ResolveMemberPhone 解析会员电话信息
// 注意: core_schema.members表不存在于当前数据库，直接返回空值，让调用方使用ERP fallback
func (r *DatabaseJoinResolverImpl) ResolveMemberPhone(memberID string) (*MemberInfo, error) {
	if memberID == "" {
		return nil, nil
	}

	// 由于core_schema.members表不存在，直接返回错误让调用方使用ERP fallback
	return nil, fmt.Errorf("core_schema.members table not available in current database schema")
}

// FillTransactionDataItemWithJoinData 使用联查数据填充TransactionDataItem
func FillTransactionDataItemWithJoinData(
	resolver DatabaseJoinResolverInterface,
	item *erp.TransactionDataItem,
	orderID string,
	employeeID string,
	stationID int64,
	fuelType string,
) error {
	// 填充支付方式信息
	if paymentInfo, err := resolver.ResolvePaymentMethod(orderID); err != nil {
		return fmt.Errorf("failed to resolve payment method: %w", err)
	} else if paymentInfo != nil {
		item.FieldTambahan1 = paymentInfo.DisplayName
	}

	// 填充员工姓名
	if userInfo, err := resolver.ResolveUser(employeeID); err != nil {
		return fmt.Errorf("failed to resolve user: %w", err)
	} else if userInfo != nil {
		item.OperatorID = userInfo.FullName

	}

	// 填充站点地址
	if stationInfo, err := resolver.ResolveStation(stationID); err != nil {
		return fmt.Errorf("failed to resolve station: %w", err)
	} else if stationInfo != nil {
		item.AreaSite = stationInfo.Address
	}

	// 填充优惠信息
	if promotions, err := resolver.ResolvePromotions(orderID); err != nil {
		return fmt.Errorf("failed to resolve promotions: %w", err)
	} else if len(promotions) > 0 {
		// 使用第一个优惠信息
		promo := promotions[0]
		item.PromotionType = promo.PromotionType
		item.PercentDiscount = promo.PercentDiscount
		item.AmountPercentDiscount = promo.AmountPercentDiscount
		item.AmountDiscount = promo.AmountDiscount
		item.FlagItemPromotion = promo.FlagItemPromotion
	} else {
		// 无优惠时设置默认值
		item.FlagItemPromotion = 0
	}

	// 填充油品分类
	if oilInfo, err := resolver.ResolveOilProduct(fuelType); err != nil {
		return fmt.Errorf("failed to resolve oil product: %w", err)
	} else if oilInfo != nil {
		item.IDProductGroup = oilInfo.Category
	}

	return nil
}

// ValidateJoinDataCompleteness 验证联查数据的完整性
func ValidateJoinDataCompleteness(item *erp.TransactionDataItem) []string {
	var missingFields []string

	if item.FieldTambahan1 == "" {
		missingFields = append(missingFields, "FieldTambahan1 (payment method)")
	}
	if item.FieldTambahan2 == "" {
		missingFields = append(missingFields, "FieldTambahan2 (employee name)")
	}
	if item.AreaSite == "" {
		missingFields = append(missingFields, "AreaSite (station address)")
	}
	if item.IDProductGroup == "" {
		missingFields = append(missingFields, "IDProductGroup (oil product category)")
	}

	return missingFields
}

// BatchResolvePaymentMethods 批量解析支付方式信息
func (r *DatabaseJoinResolverImpl) BatchResolvePaymentMethods(orderIDs []int64) (map[int64]*PaymentMethodInfo, error) {
	if len(orderIDs) == 0 {
		return make(map[int64]*PaymentMethodInfo), nil
	}

	ctx := context.Background()

	// 构建IN子句
	placeholders := make([]string, len(orderIDs))
	args := make([]interface{}, len(orderIDs))
	for i, id := range orderIDs {
		placeholders[i] = fmt.Sprintf("$%d", i+1)
		args[i] = id
	}

	query := fmt.Sprintf(`
		SELECT DISTINCT ON (o.id)
			o.id,
			pm.type as code,
			pm.display_name,
			pm.type
		FROM order_schema.orders o
		JOIN order_schema.order_payments op ON o.id = op.order_id
		JOIN payment_schema.payment_methods pm ON op.payment_method = pm.type
		WHERE o.id IN (%s) AND op.status = 'completed'
		ORDER BY o.id, op.created_at DESC
	`, strings.Join(placeholders, ","))

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to batch resolve payment methods: %w", err)
	}
	defer rows.Close()

	result := make(map[int64]*PaymentMethodInfo)
	for rows.Next() {
		var orderID int64
		var info PaymentMethodInfo

		err := rows.Scan(&orderID, &info.Code, &info.DisplayName, &info.Type)
		if err != nil {
			return nil, fmt.Errorf("failed to scan payment method row: %w", err)
		}

		result[orderID] = &info
	}

	return result, rows.Err()
}

// ResolveStaffCard 解析员工卡信息
// 联查路径: fuel_transaction.staff_card_id → order_schema.staff_cards → core_schema.users
func (r *DatabaseJoinResolverImpl) ResolveStaffCard(staffCardID string) (*StaffCardInfo, error) {
	if staffCardID == "" {
		return nil, nil
	}

	ctx := context.Background()
	query := `
		SELECT
			sc.id,
			COALESCE(sc.card_number, '') as card_no,
			COALESCE(u.full_name, '') as name,
			COALESCE(u.department, '') as position
		FROM order_schema.staff_cards sc
		LEFT JOIN core_schema.users u ON sc.user_id = u.id
		WHERE sc.id = $1::uuid
	`

	var info StaffCardInfo
	err := r.db.QueryRowContext(ctx, query, staffCardID).Scan(
		&info.ID,
		&info.CardNo,
		&info.Name,
		&info.Position,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to resolve staff card for ID %s: %w", staffCardID, err)
	}

	return &info, nil
}
