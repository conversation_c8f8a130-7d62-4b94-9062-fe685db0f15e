package reporter

import (
	"fmt"
	"strconv"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service"
)

// getLocalTimezone 获取本地时区（东7区）
func getLocalTimezone() *time.Location {
	// 尝试加载亚洲/雅加达时区（UTC+7）
	loc, err := time.LoadLocation("Asia/Jakarta")
	if err != nil {
		// 如果加载失败，使用固定偏移量UTC+7
		log.Printf("[WARN] 无法加载Asia/Jakarta时区，使用固定偏移量UTC+7: %v", err)
		loc = time.FixedZone("UTC+7", 7*60*60)
	}
	return loc
}

// DataConverter 基于常量的数据转换器
type DataConverter struct{}

// NewDataConverter 创建数据转换器实例
func NewDataConverter() *DataConverter {
	return &DataConverter{}
}

// ConvertOrderToTransactionItem 将订单转换为ERP交易项
func (dc *DataConverter) ConvertOrderToTransactionItem(order *order_service.Order) erp.TransactionDataItem {
	// 使用项目现有的常量进行字段映射，确保所有ERP字段完整
	item := erp.TransactionDataItem{
		// 基础交易信息 - 来源：order_table
		TransactionID: dc.generateTransactionID(order.ID),
		SlipNumber:    dc.generateSlipNumber(order.ID),

		// 设备信息 - 来源：order_table.transaction (Pump + Nozzle 做映射)
		DispenserNumber: DefaultDispenserNum,
		NozzleNumber:    DefaultNozzleNum,

		// 车辆信息 - 来源：order_table.order.metadata
		VehicleType: VehicleTypeCar,
		VehicleID:   dc.generateVehicleID(order.ID),

		// 操作员信息 - 来源：order_table.transaction
		OperatorID: dc.getOperatorID(order),

		// 站点信息 - 来源：order_table.order.site_id
		SiteID:   dc.getSiteID(order),
		DeviceID: fmt.Sprintf("DEV%03d", order.StationID),

		// 时间信息 - 来源：order_table.transaction.created_at（转换为本地时区）
		TransactionDate: order.CreatedAt.In(getLocalTimezone()).Format("2006-01-02 15:04:05"),

		// 控制字段
		Reprint:           DefaultReprint,   // 来源：order_table.metadata (固定值0)
		TransactionLength: DefaultTxnLength, // 来源：payment_time - created_at
		MessageHeader:     "",               // 固定值null (空字符串)
		MessageID:         "msg11",          // 固定值"msg11"
		Category:          DefaultCategory,  // 固定值"fuel"
		AreaSite:          DefaultAreaSite,  // 来源：sites.address

		// 促销折扣 - 来源：order_table.order_promotion
		PromotionType:         DefaultPromotionType, // 来源：promotion_name
		PercentDiscount:       0.00,                 // 来源：percent.metadata
		AmountPercentDiscount: 0.00,                 // 来源：percent.discount_amount
		AmountDiscount:        0.00,                 // 来源：fixed_amount.discount_amount
		FlagItemPromotion:     0,                    // 是否为优惠，0否1是
		FinalDiscount:         0.00,                 // 来源：order.discount_amount
		Voucher:               0.00,                 // 固定值null
		Point:                 0,                    // 固定值null

		// 车辆和产品组
		IDVehicleTypeGroup: "Car",  // 来源：order.metadata (Car or Motor)
		IDProductGroup:     "Fuel", // 来源：fuel_type - oil_product.Category

		// 附加字段 - 需要关联查询
		FieldTambahan1: "", // 支付方式名称 - 来源：payment_methods.display_name
		FieldTambahan2: "", // 员工名称 - 来源：users.full_name
		FieldTambahan3: "", // 优惠券代码 - 固定值null

		// 客户信息 - 来源：order_table.order.metadata (初始化为空，后续填充)
		CustomerName:    "",
		CustomerPhoneNo: "",
		Email:           "",
		Gender:          "",
		DEXROWID:        "", // 会员ID
		DEX_ROW_TS:      "", // 会员注册时间
		// DOB字段不在此处设置，避免指针问题
	}

	// 如果有关联的fuel_transaction，使用真实数据填充
	if order.Edges.FuelTransactionLink != nil &&
		order.Edges.FuelTransactionLink.Edges.FuelTransaction != nil {
		dc.fillFromFuelTransaction(&item, order.Edges.FuelTransactionLink.Edges.FuelTransaction)
	} else {
		// 使用订单基础信息填充
		dc.fillFromOrderData(&item, order)
	}

	// 客户信息 - 使用order表的customer相关字段
	dc.fillCustomerInfo(&item, order)

	return item
}

// fillFromFuelTransaction 从fuel_transaction表填充数据
func (dc *DataConverter) fillFromFuelTransaction(item *erp.TransactionDataItem, ft *order_service.FuelTransaction) {
	// 使用fueltransaction常量映射字段
	item.ProductID = dc.mapFuelTypeToProductID(ft.FuelType)
	item.Amount = ft.Amount
	item.Price = ft.UnitPrice
	item.Volume = ft.Volume

	// 优先使用生产环境的真实计数器数据
	if ft.StartTotalizer != nil && ft.EndTotalizer != nil {
		item.TotalizerStart = *ft.StartTotalizer
		item.TotalizerEnd = *ft.EndTotalizer
	} else {
		// 后备计算方案
		item.TotalizerStart = ft.TotalVolume - ft.Volume
		item.TotalizerEnd = ft.TotalVolume
	}

	// 设备信息映射 - 生产环境pump_id/nozzle_id是varchar(50)
	if pumpID, err := strconv.Atoi(ft.PumpID); err == nil {
		item.DispenserNumber = pumpID
	}
	if nozzleID, err := strconv.Atoi(ft.NozzleID); err == nil {
		item.NozzleNumber = nozzleID
	}

	// RFID信息
	if ft.MemberCardID != nil {
		item.RFIDVehicleID = *ft.MemberCardID
	}

	// 使用交易时间覆盖订单时间 - 优先使用油枪结束时间（转换为本地时区）
	if ft.NozzleEndTime != nil {
		localTime := ft.NozzleEndTime.In(getLocalTimezone())
		item.TransactionDate = localTime.Format("2006-01-02 15:04:05")
	} else {
		localTime := ft.CreatedAt.In(getLocalTimezone())
		item.TransactionDate = localTime.Format("2006-01-02 15:04:05")
	}
}

// fillFromOrderData 从订单数据填充（无fuel_transaction时的后备方案）
func (dc *DataConverter) fillFromOrderData(item *erp.TransactionDataItem, order *order_service.Order) {
	item.ProductID = ProductIDPertalite // 默认产品
	item.Amount = order.TotalAmount
	item.Price = 15000.00                      // 默认单价
	item.Volume = order.TotalAmount / 15000.00 // 计算油量
	item.TotalizerStart = 1000000.00
	item.TotalizerEnd = item.TotalizerStart + item.Volume
}

// fillCustomerInfo 填充客户信息 - 来源：order_table.order.metadata
func (dc *DataConverter) fillCustomerInfo(item *erp.TransactionDataItem, order *order_service.Order) {
	// 使用order表的customer_name字段
	if order.CustomerName != nil {
		item.CustomerName = *order.CustomerName
	} else {
		item.CustomerName = "Guest Customer"
	}

	// 客户详细信息 - 来源：order_table.order.metadata
	// TODO: 需要解析order.metadata JSON来获取以下字段：
	// - CustomerPhoneNo: 会员手机号
	// - Email: 会员邮箱 (可为null)
	// - Gender: 会员性别 ("male" or "female")
	// - DOB: 会员生日 (可为null)
	// - DEXROWID: 会员ID (可为null)
	// - DEX_ROW_TS: 会员注册时间 (可为null)

	item.CustomerPhoneNo = "" // 空字符串会被ERP处理为null
	item.Email = ""
	item.Gender = ""
	item.DEXROWID = ""   // 会员ID
	item.DEX_ROW_TS = "" // 会员注册时间
	// DOB字段不在此处设置，避免指针问题
}

// 辅助方法 - 使用常量生成各种ID

func (dc *DataConverter) generateTransactionID(orderID int64) string {
	return fmt.Sprintf("TXN%d_%d", orderID, orderID*1000)
}

func (dc *DataConverter) generateSlipNumber(orderID int64) string {
	return fmt.Sprintf("SLIP_%d", orderID)
}

func (dc *DataConverter) generateVehicleID(orderID int64) string {
	return fmt.Sprintf("VEH_%d", orderID)
}

func (dc *DataConverter) getOperatorID(order *order_service.Order) string {
	// 优先使用员工卡ID
	if order.StaffCardID != nil {
		return fmt.Sprintf("CARD_%d", *order.StaffCardID)
	}
	// 次选员工编号
	if order.EmployeeNo != nil {
		return *order.EmployeeNo
	}
	return DefaultOperatorID
}

func (dc *DataConverter) getSiteID(order *order_service.Order) string {
	return fmt.Sprintf("SITE%03d", order.StationID)
}

// mapFuelTypeToProductID 燃油类型映射到产品ID
func (dc *DataConverter) mapFuelTypeToProductID(fuelType string) string {
	switch fuelType {
	case "PERTAMAX":
		return ProductIDPertamax
	case "PERTALITE":
		return ProductIDPertalite
	case "PERTAMAX_TURBO":
		return ProductIDPertamaxTurbo
	case "PERTAMINA_DEX":
		return ProductIDPertamina
	case "SOLAR", "DIESEL":
		return ProductIDDiesel
	default:
		return ProductIDPertalite // 默认
	}
}

// ConvertOrdersToRequest 批量转换订单列表为ERP请求
func (dc *DataConverter) ConvertOrdersToRequest(orders []*order_service.Order, apiKey, user, password string) *erp.DispenserTransactionRequest {
	transactions := make([]erp.TransactionDataItem, 0, len(orders))

	for _, order := range orders {
		item := dc.ConvertOrderToTransactionItem(order)
		transactions = append(transactions, item)
	}

	return &erp.DispenserTransactionRequest{
		Key:              apiKey,
		User:             user,
		Password:         password,
		TransactionCount: len(transactions),
		MessageID:        fmt.Sprintf("batch_%d", len(transactions)),
		Transactions:     transactions,
	}
}
