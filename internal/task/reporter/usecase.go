package reporter

import (
	"context"
	"database/sql"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/repository"
)

// ERPClientInterface 定义 ERP 客户端的接口，便于测试时使用 mock
type ERPClientInterface interface {
	PostTransactions(ctx context.Context, req *erp.DispenserTransactionRequest) (*erp.DispenserTransactionResponse, error)
}

// ReporterUsecase 上报业务逻辑
type ReporterUsecase struct {
	orderRepo        repository.OrderRepo
	reportRecordRepo repository.ReportRecordRepo
	erpClient        ERPClientInterface
	db               *sql.DB                  // 添加数据库连接，支持IntegratedDataConverter的完整功能
	converter        *IntegratedDataConverter // 集成数据转换器
}

// NewReporterUsecase 创建新的上报用例实例
func NewReporterUsecase(orderRepo repository.OrderRepo, reportRecordRepo repository.ReportRecordRepo, erpClient ERPClientInterface, db *sql.DB) *ReporterUsecase {
	var converter *IntegratedDataConverter
	if db != nil {
		// 使用完整的IntegratedDataConverter（包含数据库联查）
		converter = NewIntegratedDataConverter(db)
	} else {
		// 使用简化版本（不包含数据库联查）
		converter = NewIntegratedDataConverterNoJoin()
	}

	return &ReporterUsecase{
		orderRepo:        orderRepo,
		reportRecordRepo: reportRecordRepo,
		erpClient:        erpClient,
		db:               db,
		converter:        converter,
	}
}

// NewReporterUsecaseWithoutDB 创建不带数据库连接的实例（向后兼容）
func NewReporterUsecaseWithoutDB(orderRepo repository.OrderRepo, reportRecordRepo repository.ReportRecordRepo, erpClient ERPClientInterface) *ReporterUsecase {
	return NewReporterUsecase(orderRepo, reportRecordRepo, erpClient, nil)
}

// ReportTransactions 执行交易数据上报 - 实现已移至 report_order_usecase.go

// SyncDataToReportRecord 执行数据同步任务 - 将订单表数据同步到report_record表
func (uc *ReporterUsecase) SyncDataToReportRecord(ctx context.Context) error {
	// 1. 同步订单数据到report_record表
	err := uc.syncOrder(ctx)
	if err != nil {
		return err
	}

	// 2. 同步库存数据到report_record表 TODO
	// return uc.syncInventory(ctx)
	return nil
}
