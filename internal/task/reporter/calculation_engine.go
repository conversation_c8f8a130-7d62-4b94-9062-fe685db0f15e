package reporter

import (
	"time"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service"
)

// CalculationEngine 负责计算派生字段
// 设计原则：纯函数，无副作用，可预测的数学计算
type CalculationEngine struct{}

// NewCalculationEngine 创建CalculationEngine实例
// 纯函数：无状态，可以多次调用
func NewCalculationEngine() *CalculationEngine {
	return &CalculationEngine{}
}

// CalculateTransactionLength 计算交易时长（秒）
// 纯函数：给定相同的时间输入，总是返回相同的秒数
func (ce *CalculationEngine) CalculateTransactionLength(paymentTime, transactionStartTime *time.Time) int {
	// 处理空值情况
	if paymentTime == nil || transactionStartTime == nil {
		return DefaultTxnLength
	}

	// 计算时间差并转换为秒
	duration := paymentTime.Sub(*transactionStartTime)
	return int(duration.Seconds())
}

// CalculateDiscount 计算折扣金额
// 纯函数：计算原价与最终价格的差额
func (ce *CalculationEngine) CalculateDiscount(originalAmount, finalAmount float64) float64 {
	return originalAmount - finalAmount
}

// CalculateDiscountPercentage 计算折扣百分比
// 纯函数：计算折扣的百分比表示
func (ce *CalculationEngine) CalculateDiscountPercentage(originalAmount, finalAmount float64) float64 {
	// 避免除零错误
	if originalAmount == 0 {
		return 0.0
	}

	discountAmount := originalAmount - finalAmount
	return (discountAmount / originalAmount) * 100.0
}

// FillCalculatedFields 填充计算派生的字段
// 纯函数原则：只修改传入的item，不访问外部状态
func (ce *CalculationEngine) FillCalculatedFields(item *erp.TransactionDataItem, order *order_service.Order) {
	// 填充finalDiscount（使用order的discount_amount字段）
	item.FinalDiscount = order.DiscountAmount
	// 如果有燃油交易数据，可以使用更精确的时间计算
	// if order.Edges.FuelTransactionLink != nil &&
	//    order.Edges.FuelTransactionLink.Edges.FuelTransaction != nil {
	//     ft := order.Edges.FuelTransactionLink.Edges.FuelTransaction
	//
	//     var startTime *time.Time
	//     if ft.NozzleStartTime != nil {
	//         startTime = ft.NozzleStartTime
	//     } else {
	//         startTime = &ft.CreatedAt
	//     }
	//
	//     var endTime *time.Time
	//     if ft.NozzleEndTime != nil {
	//         endTime = ft.NozzleEndTime
	//     } else if order.PaymentCompletedAt != nil {
	//         endTime = order.PaymentCompletedAt
	//     }
	//
	//     if startTime != nil && endTime != nil {
	//         item.TransactionLength = ce.CalculateTransactionLength(endTime, startTime)
	//     }
	// }
}

// FillCalculatedFieldsWithTimes 带有明确时间参数的填充方法
// 纯函数：提供更精确的时间计算控制
func (ce *CalculationEngine) FillCalculatedFieldsWithTimes(
	item *erp.TransactionDataItem,
	order *order_service.Order,
	paymentTime *time.Time,
	transactionStartTime *time.Time,
) {
	// 填充折扣金额
	item.FinalDiscount = order.DiscountAmount

	// 计算交易时长
	// item.TransactionLength = ce.CalculateTransactionLength(paymentTime, transactionStartTime)
}

// ValidateTransactionLength 验证交易时长是否合理
// 纯函数：用于数据质量检查
func (ce *CalculationEngine) ValidateTransactionLength(length int) bool {
	// 交易时长应该在合理范围内（0秒到1小时）
	return length >= 0 && length <= 3600
}

// ValidateDiscountAmount 验证折扣金额是否合理
// 纯函数：用于数据质量检查
func (ce *CalculationEngine) ValidateDiscountAmount(discountAmount, originalAmount float64) bool {
	// 折扣金额不应该超过原价，也不应该为负数（除非是价格上涨）
	return discountAmount >= 0 && discountAmount <= originalAmount
}
