package reporter

// MappingTableResolver 负责处理映射表查询
// 设计原则：纯函数，无副作用，可预测的映射转换
type MappingTableResolver struct {
	pumpMapping   map[string]int // pump_id -> dispenser_number
	nozzleMapping map[string]int // nozzle_id -> nozzle_number
}

// NewMappingTableResolver 创建MappingTableResolver实例
// 纯函数：无状态，可以多次调用
func NewMappingTableResolver() *MappingTableResolver {
	return &MappingTableResolver{
		// 初始化空映射表，支持后续自定义加载
		pumpMapping:   make(map[string]int),
		nozzleMapping: make(map[string]int),
	}
}

// LoadCustomMappings 加载自定义映射表
// 纯函数：只修改内部映射表状态，不产生其他副作用
func (mtr *MappingTableResolver) LoadCustomMappings(pumpMap, nozzleMap map[string]int) {
	// 清空现有映射
	mtr.pumpMapping = make(map[string]int)
	mtr.nozzleMapping = make(map[string]int)

	// 加载自定义映射
	for k, v := range pumpMap {
		mtr.pumpMapping[k] = v
	}
	for k, v := range nozzleMap {
		mtr.nozzleMapping[k] = v
	}
}

// MapVehicleTypeToGroup 映射车辆类型到车辆组名称
// 纯函数：车辆类型的标准化映射
// 业务规则：1=Dinas, 2=Truck, 3=Car, 4=Motor
func (mtr *MappingTableResolver) MapVehicleTypeToGroup(vehicleType int) string {
	switch vehicleType {
	case VehicleTypeDinas:
		return "Dinas"
	case VehicleTypeTruck:
		return "Truck"
	case VehicleTypeCar:
		return "Car"
	case VehicleTypeMotor:
		return "Motor"
	default:
		return "Car" // 默认为汽车
	}
}

// NormalizeVehicleType 标准化车辆类型
// 纯函数：确保车辆类型在有效范围内
func (mtr *MappingTableResolver) NormalizeVehicleType(vehicleType int) int {
	switch vehicleType {
	case VehicleTypeDinas, VehicleTypeTruck, VehicleTypeCar, VehicleTypeMotor:
		return vehicleType
	default:
		return VehicleTypeCar // 默认为汽车
	}
}

// GetMappingStats 获取映射表统计信息
// 纯函数：只读操作，返回映射表状态信息
func (mtr *MappingTableResolver) GetMappingStats() (int, int) {
	return len(mtr.pumpMapping), len(mtr.nozzleMapping)
}

// HasCustomMappings 检查是否有自定义映射
// 纯函数：检查内部状态，无副作用
func (mtr *MappingTableResolver) HasCustomMappings() bool {
	return len(mtr.pumpMapping) > 0 || len(mtr.nozzleMapping) > 0
}
