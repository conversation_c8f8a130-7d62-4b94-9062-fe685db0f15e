package reporter

import (
	"context"
	"log"
	"sync"
	"time"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/config"
)

// Scheduler 调度器结构体
type Scheduler struct {
	usecase   *ReporterUsecase
	config    *config.ReporterConfig
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
	isRunning bool
	mu        sync.RWMutex
}

// NewScheduler 创建新的调度器实例
func NewScheduler(usecase *ReporterUsecase, cfg *config.ReporterConfig) *Scheduler {
	ctx, cancel := context.WithCancel(context.Background())
	return &Scheduler{
		usecase: usecase,
		config:  cfg,
		ctx:     ctx,
		cancel:  cancel,
	}
}

// Start 启动调度器，根据配置定时触发任务
func (s *Scheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		log.Println("调度器已经在运行中")
		return nil
	}

	s.isRunning = true

	// 启动交易数据上报定时任务
	if s.config.TransactionInterval > 0 {
		s.wg.Add(1)
		go s.runTransactionReporter()
		log.Printf("交易数据上报定时任务已启动，间隔: %d秒", s.config.TransactionInterval)
	}

	// 启动库存数据上报定时任务（如果配置了的话）
	if s.config.InventoryInterval > 0 {
		s.wg.Add(1)
		go s.runInventoryReporter()
		log.Printf("库存数据上报定时任务已启动，间隔: %d秒", s.config.InventoryInterval)
	}

	// 启动数据同步定时任务（如果配置了的话）
	if s.config.DataSyncInterval > 0 {
		s.wg.Add(1)
		go s.runDataSyncReporter()
		log.Printf("数据同步定时任务已启动，间隔: %d秒", s.config.DataSyncInterval)
	}

	log.Println("调度器已启动")
	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return
	}

	s.isRunning = false
	s.cancel()  // 取消所有协程
	s.wg.Wait() // 等待所有协程完成
	log.Println("调度器已停止")
}

// runTransactionReporter 运行交易数据上报任务
func (s *Scheduler) runTransactionReporter() {
	defer s.wg.Done()

	ticker := time.NewTicker(time.Duration(s.config.TransactionInterval) * time.Second)
	defer ticker.Stop()

	// 立即执行一次
	s.executeTransactionReport()

	for {
		select {
		case <-s.ctx.Done():
			log.Println("交易数据上报任务协程退出")
			return
		case <-ticker.C:
			s.executeTransactionReport()
		}
	}
}

// runInventoryReporter 运行库存数据上报任务
func (s *Scheduler) runInventoryReporter() {
	defer s.wg.Done()

	ticker := time.NewTicker(time.Duration(s.config.InventoryInterval) * time.Second)
	defer ticker.Stop()

	// 立即执行一次
	s.executeInventoryReport()

	for {
		select {
		case <-s.ctx.Done():
			log.Println("库存数据上报任务协程退出")
			return
		case <-ticker.C:
			s.executeInventoryReport()
		}
	}
}

// runDataSyncReporter 运行数据同步任务
func (s *Scheduler) runDataSyncReporter() {
	defer s.wg.Done()

	ticker := time.NewTicker(time.Duration(s.config.DataSyncInterval) * time.Second)
	defer ticker.Stop()

	// 立即执行一次
	s.executeDataSyncReport()

	for {
		select {
		case <-s.ctx.Done():
			log.Println("数据同步任务协程退出")
			return
		case <-ticker.C:
			s.executeDataSyncReport()
		}
	}
}

// executeTransactionReport 执行交易数据上报
func (s *Scheduler) executeTransactionReport() {
	log.Println("开始执行交易数据上报任务...")
	ctx := context.Background()
	if err := s.usecase.ReportTransactions(ctx, s.config); err != nil {
		log.Printf("交易数据上报失败: %v", err)
	} else {
		log.Println("交易数据上报任务完成")
	}
}

// executeInventoryReport 执行库存数据上报
func (s *Scheduler) executeInventoryReport() {
	log.Println("开始执行库存数据上报任务...")
	// TODO: 实现库存数据上报逻辑
	// ctx := context.Background()
	// if err := s.usecase.ReportInventory(ctx); err != nil {
	//     log.Printf("库存数据上报失败: %v", err)
	// } else {
	//     log.Println("库存数据上报任务完成")
	// }
}

// executeDataSyncReport 执行数据同步任务
func (s *Scheduler) executeDataSyncReport() {
	log.Println("开始执行数据同步任务...")
	ctx := context.Background()
	if err := s.usecase.SyncDataToReportRecord(ctx); err != nil {
		log.Printf("数据同步失败: %v", err)
	} else {
		log.Println("数据同步任务完成")
	}
}

// IsRunning 检查调度器是否正在运行
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}
