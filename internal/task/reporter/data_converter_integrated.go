package reporter

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/adapter/erp"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service"
)

// getLocalTimezone 获取本地时区（东7区）
func getLocalTimezone() *time.Location {
	// 尝试加载亚洲/雅加达时区（UTC+7）
	loc, err := time.LoadLocation("Asia/Jakarta")
	if err != nil {
		// 如果加载失败，使用固定偏移量UTC+7
		log.Printf("[WARN] 无法加载Asia/Jakarta时区，使用固定偏移量UTC+7: %v", err)
		loc = time.FixedZone("UTC+7", 7*60*60)
	}
	return loc
}

// IntegratedDataConverter 集成了所有模块的完整数据转换器
type IntegratedDataConverter struct {
	metadataParser    *MetadataParser
	calculationEngine *CalculationEngine
	mappingResolver   *MappingTableResolver
	joinResolver      DatabaseJoinResolverInterface
}

// NewIntegratedDataConverter 创建集成数据转换器
func NewIntegratedDataConverter(db *sql.DB) *IntegratedDataConverter {
	return &IntegratedDataConverter{
		metadataParser:    NewMetadataParser(),
		calculationEngine: NewCalculationEngine(),
		mappingResolver:   NewMappingTableResolver(),
		joinResolver:      NewDatabaseJoinResolverImpl(db),
	}
}

// NewIntegratedDataConverterNoJoin 创建不带数据库联查的版本（用于测试）
func NewIntegratedDataConverterNoJoin() *IntegratedDataConverter {
	return &IntegratedDataConverter{
		metadataParser:    NewMetadataParser(),
		calculationEngine: NewCalculationEngine(),
		mappingResolver:   NewMappingTableResolver(),
		joinResolver:      nil, // 不使用联查
	}
}

// ConvertOrderToTransactionItem 集成模块化转换订单为ERP交易项 - 单一来源策略
func (idc *IntegratedDataConverter) ConvertOrderToTransactionItem(order *order_service.Order) (erp.TransactionDataItem, error) {
	log.Printf("[DEBUG] Starting conversion for order ID: %s", order.ID)

	// 1. 初始化基础交易项
	log.Printf("[DEBUG] Step 1: Initializing base item for order %s", order.ID)
	item := idc.initializeBaseItem(order)
	log.Printf("[DEBUG] Base item initialized - TransactionID: %s, SlipNumber: %s, SiteID: %s",
		item.TransactionID, item.SlipNumber, item.SiteID)

	// TransactionDate - 唯一来源：订单创建时间（转换为本地时区）
	localTime := order.CreatedAt.In(getLocalTimezone())
	item.TransactionDate = localTime.Format("2006-01-02 15:04:05")

	// 2. 填充燃油交易数据
	log.Printf("[DEBUG] Step 2: Filling fuel transaction data for order %s", order.ID)
	// 2. 燃油交易数据填充 - 必须存在，不允许兜底策略
	var fuelTransaction *order_service.FuelTransaction
	if order.Edges.FuelTransactionLink != nil && order.Edges.FuelTransactionLink.Edges.FuelTransaction != nil {
		fuelTransaction = order.Edges.FuelTransactionLink.Edges.FuelTransaction
		log.Printf("[DEBUG] Found fuel transaction - ID: %s, FuelType: %s, Amount: %.2f, Volume: %.2f",
			fuelTransaction.ID, fuelTransaction.FuelType, fuelTransaction.Amount, fuelTransaction.Volume)
		idc.fillFromFuelTransaction(&item, fuelTransaction)
		log.Printf("[DEBUG] After fuel transaction fill - ProductID: %s, Amount: %.2f, Volume: %.2f, Price: %.2f",
			item.ProductID, item.Amount, item.Volume, item.Price)
	} else {
		// 单一来源策略：没有燃油交易数据则返回错误，不使用兜底策略
		return erp.TransactionDataItem{}, fmt.Errorf("no fuel transaction found for order %s - required for single source strategy", order.ID)
	}

	// 3. 模块化数据填充

	// 3.1 元数据解析模块 - 填充车辆和客户信息
	log.Printf("[DEBUG] Step 3.1: Processing metadata fields for order %s", order.ID)
	idc.fillMetadataFields(&item, order)

	// 3.2 映射解析模块 - 填充ID映射字段
	log.Printf("[DEBUG] Step 3.2: Processing mapped fields for order %s", order.ID)
	idc.fillMappedFields(&item, order, fuelTransaction)

	// 3.3 计算引擎模块 - 填充计算字段
	log.Printf("[DEBUG] Step 3.3: Processing calculated fields for order %s", order.ID)
	idc.fillCalculatedFields(&item, order)

	// 3.4 数据库联查模块 - 填充关联数据（最后执行，可能需要网络请求）
	log.Printf("[DEBUG] Step 3.4: Processing joined fields for order %s", order.ID)
	idc.fillJoinedFields(&item, order, fuelTransaction)

	// CustomerPhoneNo 已经在初始化时直接从订单字段获取，无需额外查询

	log.Printf("[DEBUG] Conversion completed for order %s - Final item summary:", order.ID)
	log.Printf("[DEBUG] - TransactionID: %s, Amount: %.2f, Volume: %.2f",
		item.TransactionID, item.Amount, item.Volume)
	log.Printf("[DEBUG] - ProductID: %s, VehicleType: %d, DispenserNumber: %d, NozzleNumber: %d",
		item.ProductID, item.VehicleType, item.DispenserNumber, item.NozzleNumber)
	log.Printf("[DEBUG] - CustomerName: %s, OperatorID: %s, AreaSite: %s",
		item.CustomerName, item.OperatorID, item.AreaSite)

	return item, nil
}

// initializeBaseItem 初始化基础交易项（继承现有逻辑）
func (idc *IntegratedDataConverter) initializeBaseItem(order *order_service.Order) erp.TransactionDataItem {
	// 优先使用订单号，如果为空则生成默认值
	slipNumber := order.OrderNumber
	if slipNumber == "" {
		slipNumber = fmt.Sprintf("SLIP_%s", order.ID)
	}

	return erp.TransactionDataItem{
		// 基础交易信息 - 单一来源
		TransactionID: order.ID,
		SlipNumber:    slipNumber,

		// 设备信息 - 只从元数据ERP获取，不设置默认值
		// DispenserNumber: 将在元数据模块中设置
		// NozzleNumber: 将在元数据模块中设置

		// 车辆信息 - 明确单一来源
		// VehicleType: 将在元数据模块中设置
		VehicleID: idc.getVehicleID(order), // 唯一来源：订单车牌号
		// IDVehicleTypeGroup: 将在映射模块中设置

		// 操作员信息 - 将在元数据模块中从ERP信息设置
		// OperatorID: 将在元数据模块中设置

		// 站点信息 - 只从元数据ERP获取，不设置默认值
		// SiteID: 将在元数据模块中设置
		// DeviceID: 将在元数据模块中设置

		// 产品信息 - 将在燃油交易模块中设置
		// ProductID: 将在fillFromFuelTransaction中设置
		// IDProductGroup: 将在映射模块中基于ProductID计算

		// 时间信息 - 将在燃油交易模块中设置
		// TransactionDate: 将在fillFromFuelTransaction中设置

		// 控制字段
		Reprint: 0, // 固定值
		// TransactionLength: 将在计算模块中设置
		MessageHeader: "",
		MessageID:     "msg11",
		Category:      "Fuel",
		AreaSite:      "Area 1", // 固定值

		// 促销折扣 - 明确单一来源
		// PromotionType: 将在联查模块中设置
		// PercentDiscount: 将在计算模块中设置
		AmountPercentDiscount: 0.0, // 固定值
		AmountDiscount:        0.0, // 将在联查模块中设置
		FlagItemPromotion:     0,   // 将在联查模块中设置
		// FinalDiscount: 将在计算模块中设置
		Voucher: 0.0, // 固定值
		Point:   0,   // 固定值

		// 附加字段 - 明确单一来源
		// FieldTambahan1: 将在元数据模块中设置
		// FieldTambahan2: 将在联查模块中设置
		FieldTambahan3: "TRN", // 固定值

		// 客户信息 - 明确单一来源
		CustomerName:    idc.getCustomerName(order),  // 唯一来源：订单字段
		CustomerPhoneNo: idc.getCustomerPhone(order), // 唯一来源：订单字段
		// Email: 将在元数据模块中设置
		// Gender: 将在元数据模块中设置
		// DEXROWID: 将在元数据模块中设置
		// DEX_ROW_TS: 将在元数据模块中设置
	}
}

// fillFromFuelTransaction 从燃油交易填充数据 - 单一来源策略
func (idc *IntegratedDataConverter) fillFromFuelTransaction(item *erp.TransactionDataItem, ft *order_service.FuelTransaction) {
	log.Printf("[DEBUG] Filling from fuel transaction - FuelType: %s, FuelGrade: %s",
		ft.FuelType, ft.FuelGrade)

	// ProductID - 唯一来源：优先使用 fuel_grade，否则使用 fuel_type
	item.ProductID = idc.mapFuelTypeToProductID(ft.FuelType)

	// 核心交易数据 - 唯一来源：燃油交易
	item.Amount = ft.Amount
	item.Price = ft.UnitPrice
	item.Volume = ft.Volume * 1000
	log.Printf("[DEBUG] Set Amount: %.2f, Price: %.2f, Volume: %.2f",
		item.Amount, item.Price, item.Volume)

	// 计数器数据 - 唯一来源：燃油交易
	if ft.StartTotalizer != nil && ft.EndTotalizer != nil {
		item.TotalizerStart = *ft.StartTotalizer
		item.TotalizerEnd = *ft.EndTotalizer
		log.Printf("[DEBUG] Totalizer from fuel transaction - Start: %.2f, End: %.2f",
			item.TotalizerStart, item.TotalizerEnd)
	} else {
		// 计算值作为唯一来源
		item.TotalizerStart = ft.TotalVolume - ft.Volume
		item.TotalizerEnd = ft.TotalVolume
		log.Printf("[DEBUG] Calculated totalizer values - Start: %.2f, End: %.2f (TotalVolume: %.2f)",
			item.TotalizerStart, item.TotalizerEnd, ft.TotalVolume)
	}

	// RFID信息 - 唯一来源：燃油交易
	if ft.MemberCardID != nil {
		item.RFIDVehicleID = *ft.MemberCardID
		log.Printf("[DEBUG] RFIDVehicleID from fuel transaction: %s", item.RFIDVehicleID)
	}
	// 注意：如果没有MemberCardID，字段保持空值，不设置默认值

}

// 删除 fillFromOrderData 方法 - 不允许兜底策略，必须有明确的数据来源

// fillMetadataFields 使用元数据解析模块填充字段
func (idc *IntegratedDataConverter) fillMetadataFields(item *erp.TransactionDataItem, order *order_service.Order) {
	if order.Metadata == nil {
		log.Printf("[DEBUG] No metadata found for order %s", order.ID)
		return
	}

	// 显示原始metadata JSON内容
	metadataBytes, _ := json.Marshal(order.Metadata)
	log.Printf("[DEBUG] Processing metadata for order %s - Raw JSON: %s", order.ID, string(metadataBytes))

	// 解析metadata JSON
	metadata := idc.metadataParser.ParseMetadata(order.Metadata)

	// 显示解析后的结构化数据
	log.Printf("[DEBUG] Parsed metadata structure:")
	if metadata.VehicleType != nil {
		log.Printf("[DEBUG] - VehicleType: %d", *metadata.VehicleType)
	}
	if metadata.VehicleID != nil {
		log.Printf("[DEBUG] - VehicleID: %s", *metadata.VehicleID)
	}
	if metadata.VehicleGroup != nil {
		log.Printf("[DEBUG] - VehicleGroup: %s", *metadata.VehicleGroup)
	}
	if metadata.DeviceID != nil {
		log.Printf("[DEBUG] - DeviceID: %s", *metadata.DeviceID)
	}
	if metadata.Reprint != nil {
		log.Printf("[DEBUG] - Reprint: %d", *metadata.Reprint)
	}
	if metadata.CustomerPhone != nil {
		log.Printf("[DEBUG] - CustomerPhone: %s", *metadata.CustomerPhone)
	}
	if metadata.Email != nil {
		log.Printf("[DEBUG] - Email: %s", *metadata.Email)
	}
	if metadata.Gender != nil {
		log.Printf("[DEBUG] - Gender: %s", *metadata.Gender)
	}
	if metadata.DOB != nil {
		log.Printf("[DEBUG] - DOB: %s", *metadata.DOB)
	}
	if metadata.MemberID != nil {
		log.Printf("[DEBUG] - MemberID: %s", *metadata.MemberID)
	}
	if metadata.MemberRegTime != nil {
		log.Printf("[DEBUG] - MemberRegTime: %s", *metadata.MemberRegTime)
	}

	// 显示ERP信息
	if metadata.ERPInfo != nil {
		log.Printf("[DEBUG] === ERP Info Found ===")
		if metadata.ERPInfo.VehicleType != nil {
			log.Printf("[DEBUG] - ERP VehicleType: %d", *metadata.ERPInfo.VehicleType)
		}
		if metadata.ERPInfo.DispenserNumber != nil {
			log.Printf("[DEBUG] - ERP DispenserNumber: %d", *metadata.ERPInfo.DispenserNumber)
		}
		if metadata.ERPInfo.NozzleNumber != nil {
			log.Printf("[DEBUG] - ERP NozzleNumber: %d", *metadata.ERPInfo.NozzleNumber)
		}
		if metadata.ERPInfo.FieldTambahan1 != nil {
			log.Printf("[DEBUG] - ERP FieldTambahan1: %s", *metadata.ERPInfo.FieldTambahan1)
		}
		if metadata.ERPInfo.FieldTambahan2 != nil {
			log.Printf("[DEBUG] - ERP FieldTambahan2: %s", *metadata.ERPInfo.FieldTambahan2)
		}
		if metadata.ERPInfo.AreaSite != nil {
			log.Printf("[DEBUG] - ERP AreaSite: %s", *metadata.ERPInfo.AreaSite)
		}
	} else {
		log.Printf("[DEBUG] No ERP Info found in metadata")
	}

	// 单一来源策略：只使用ERP信息填充字段
	idc.metadataParser.FillERPInfoFields(item, metadata)

	// 填充其他元数据字段（非ERP信息）
	if metadata.DOB != nil {
		item.DOB = metadata.DOB
		log.Printf("[DEBUG] Set DOB from metadata: %v", item.DOB)
	}
	if metadata.MemberID != nil {
		item.DEXROWID = *metadata.MemberID
		log.Printf("[DEBUG] Set DEXROWID from metadata: %s", item.DEXROWID)
	}
	if metadata.MemberRegTime != nil {
		item.DEX_ROW_TS = *metadata.MemberRegTime
		log.Printf("[DEBUG] Set DEX_ROW_TS from metadata: %s", item.DEX_ROW_TS)
	}
	if metadata.Email != nil {
		item.Email = *metadata.Email
		log.Printf("[DEBUG] Set Email from metadata: %s", item.Email)
	}
	if metadata.Gender != nil {
		item.Gender = *metadata.Gender
		log.Printf("[DEBUG] Set Gender from metadata: %s", item.Gender)
	}
}

// fillMappedFields 使用映射解析模块填充字段 - 单一来源策略
func (idc *IntegratedDataConverter) fillMappedFields(item *erp.TransactionDataItem, order *order_service.Order, ft *order_service.FuelTransaction) {
	log.Printf("[DEBUG] Processing mapped fields for order %s", order.ID)

	// 单一来源：只负责车辆类型到分组的映射计算
	item.IDVehicleTypeGroup = idc.mappingResolver.MapVehicleTypeToGroup(item.VehicleType)
	log.Printf("[DEBUG] Mapped VehicleType %d to VehicleTypeGroup: %s", item.VehicleType, item.IDVehicleTypeGroup)

	// 单一来源：基于 ProductID 映射 IDProductGroup
	item.IDProductGroup = idc.mapProductIDToGroup(item.ProductID)
	log.Printf("[DEBUG] Mapped ProductID %s to IDProductGroup: %s", item.ProductID, item.IDProductGroup)
}

// fillCalculatedFields 使用计算引擎模块填充字段 - 单一来源策略
func (idc *IntegratedDataConverter) fillCalculatedFields(item *erp.TransactionDataItem, order *order_service.Order) {
	log.Printf("[DEBUG] Processing calculated fields for order %s", order.ID)

	// TransactionLength - 唯一来源：燃油交易时间计算
	var paymentTime, startTime *time.Time
	if order.Edges.FuelTransactionLink != nil && order.Edges.FuelTransactionLink.Edges.FuelTransaction != nil {
		ft := order.Edges.FuelTransactionLink.Edges.FuelTransaction
		if ft.NozzleStartTime != nil {
			startTime = ft.NozzleStartTime
		} else {
			startTime = &ft.CreatedAt
		}
		if order.CompletedAt != nil {
			paymentTime = order.CompletedAt
		} else if ft.NozzleEndTime != nil {
			paymentTime = ft.NozzleEndTime
		}
	}

	item.TransactionLength = idc.calculationEngine.CalculateTransactionLength(paymentTime, startTime)
	log.Printf("[DEBUG] Calculated TransactionLength: %d", item.TransactionLength)

	// FinalDiscount - 唯一来源：订单折扣金额
	item.FinalDiscount = order.DiscountAmount
	log.Printf("[DEBUG] Set FinalDiscount from order: %.2f", item.FinalDiscount)

	// PercentDiscount - 唯一来源：基于订单金额计算
	if order.DiscountAmount > 0 {
		item.PercentDiscount = idc.calculationEngine.CalculateDiscountPercentage(order.TotalAmount, order.TotalAmount-order.DiscountAmount)
		log.Printf("[DEBUG] Calculated PercentDiscount: %.2f", item.PercentDiscount)
	}
}

// fillJoinedFields 使用数据库联查模块填充字段 - 单一来源策略
func (idc *IntegratedDataConverter) fillJoinedFields(item *erp.TransactionDataItem, order *order_service.Order, ft *order_service.FuelTransaction) {
	log.Printf("[DEBUG] Processing joined fields for order %s", order.ID)

	// 单一来源策略：只有联查解析器可用时才执行，不使用兜底策略
	if idc.joinResolver == nil {
		log.Printf("[DEBUG] No join resolver available - skipping joined fields")
		return
	}

	// 获取员工ID - 唯一来源：燃油交易
	var employeeID string
	if ft != nil && ft.EmployeeID != nil {
		employeeID = *ft.EmployeeID
		log.Printf("[DEBUG] Using employeeID from fuel transaction: %s", employeeID)
	}

	// 获取燃油类型 - 唯一来源：燃油交易
	var fuelType string
	if ft != nil {
		fuelType = ft.FuelType
		log.Printf("[DEBUG] Using fuelType from fuel transaction: %s", fuelType)
	}

	log.Printf("[DEBUG] Starting database join queries with parameters:")
	log.Printf("[DEBUG] - OrderID: %s", order.ID)
	log.Printf("[DEBUG] - EmployeeID: %s", employeeID)
	log.Printf("[DEBUG] - StationID: %d", order.StationID)
	log.Printf("[DEBUG] - FuelType: %s", fuelType)

	// 执行必要的联查操作 - 简化版本，只保留有效的查询
	// idc.executePaymentMethodQuery(item, order.ID)  // ❌ 注释：使用 metadata
	idc.executeStaffCardQuery(item, order, ft) // ✅ 保留：获取员工姓名
	// idc.executeStationQuery(item, order.StationID) // ❌ 注释：使用硬编码
	idc.executePromotionQuery(item, order.ID) // ✅ 保留：获取促销名称
	// idc.executeOilProductQuery(item, fuelType)      // ❌ 注释：使用 metadata
	// idc.executeMemberPhoneQuery(item, order) // 移至ConvertOrderToTransactionItem中

	log.Printf("[DEBUG] All join queries completed")

	// 显示最终的优先级选择结果
	log.Printf("[DEBUG] Final priority-based field selection results:")
	log.Printf("[DEBUG] - FieldTambahan1 (Payment): %s", item.FieldTambahan1)
	log.Printf("[DEBUG] - FieldTambahan2 (Employee): %s", item.FieldTambahan2)
	log.Printf("[DEBUG] - AreaSite (Station): %s", item.AreaSite)
	log.Printf("[DEBUG] - IDProductGroup (Product): %s", item.IDProductGroup)
}

// getMemberIDFromMetadata 方法已移除 - 不再需要会员ID查询

// executePaymentMethodQuery 执行支付方式查询
func (idc *IntegratedDataConverter) executePaymentMethodQuery(item *erp.TransactionDataItem, orderID string) {
	log.Printf("[DEBUG] === Payment Method Query ===")
	log.Printf("[DEBUG] SQL: SELECT DISTINCT pm.type as code, pm.display_name, pm.type FROM order_schema.orders o JOIN order_schema.order_payments op ON o.id = op.order_id JOIN payment_schema.payment_methods pm ON op.payment_method = pm.type WHERE o.id = $1 AND op.status = 'completed' ORDER BY op.created_at DESC LIMIT 1")
	log.Printf("[DEBUG] Parameters: [%s]", orderID)

	// 记录当前值（可能来自ERP Info）
	currentERPValue := item.FieldTambahan1
	log.Printf("[DEBUG] Current FieldTambahan1 value (from ERP): %s", currentERPValue)

	// 优先级1: 数据库联查
	if paymentInfo, err := idc.joinResolver.ResolvePaymentMethod(orderID); err != nil {
		log.Printf("[DEBUG] Payment method query failed: %v, trying ERP fallback", err)
		// 优先级2: ERP Info保底 - 使用已有的ERP值
		if currentERPValue != "" {
			log.Printf("[DEBUG] Using ERP fallback value: %s", currentERPValue)
			// ERP值已经在之前设置，保持不变
		} else {
			// 优先级3: 默认值兜底
			item.FieldTambahan1 = "Unknown Payment"
			log.Printf("[DEBUG] Using default fallback: Unknown Payment")
		}
	} else if paymentInfo != nil {
		// 数据库查询成功，覆盖ERP值
		log.Printf("[DEBUG] Payment method query result: Code=%s, DisplayName=%s, Type=%s",
			paymentInfo.Code, paymentInfo.DisplayName, paymentInfo.Type)
		oldValue := item.FieldTambahan1
		item.FieldTambahan1 = paymentInfo.DisplayName
		log.Printf("[DEBUG] DB query success - Updated FieldTambahan1: %s -> %s", oldValue, item.FieldTambahan1)
	} else {
		log.Printf("[DEBUG] Payment method query returned no results, trying ERP fallback")
		// 优先级2: ERP Info保底
		if currentERPValue != "" {
			log.Printf("[DEBUG] Using ERP fallback value: %s", currentERPValue)
			// ERP值已经在之前设置，保持不变
		} else {
			// 优先级3: 默认值兜底
			item.FieldTambahan1 = "Unknown Payment"
			log.Printf("[DEBUG] Using default fallback: Unknown Payment")
		}
	}
}

// executeStaffCardQuery 执行员工卡查询 - 使用 fuel_transaction.staff_card_id 联查 staff_card 表
func (idc *IntegratedDataConverter) executeStaffCardQuery(item *erp.TransactionDataItem, order *order_service.Order, ft *order_service.FuelTransaction) {
	log.Printf("[DEBUG] === Staff Card Query ===")

	// 从燃油交易中获取 staff_card_id (直接使用字符串类型)
	var staffCardID string
	if ft != nil && ft.StaffCardID != nil && *ft.StaffCardID != "" {
		staffCardID = *ft.StaffCardID
		log.Printf("[DEBUG] Using StaffCardID from fuel transaction: %s", staffCardID)
	} else {
		log.Printf("[DEBUG] No StaffCardID found in fuel transaction")
	}

	// 如果没有有效的 staff_card_id，尝试使用订单中的 employee_no 作为回退
	if staffCardID == "" {
		log.Printf("[DEBUG] No valid StaffCardID found, trying order.EmployeeNo as fallback")
		if order.EmployeeNo != nil && *order.EmployeeNo != "" && *order.EmployeeNo != "unknown" {
			log.Printf("[DEBUG] Using EmployeeNo from order as FieldTambahan2: %s", *order.EmployeeNo)
			oldValue := item.FieldTambahan2
			item.FieldTambahan2 = *order.EmployeeNo
			log.Printf("[DEBUG] Updated FieldTambahan2: %s -> %s", oldValue, item.FieldTambahan2)

			// OperatorID 单一来源策略：只来自元数据ERP信息，不在此处修改
			// oldOperatorID := item.OperatorID
			// item.OperatorID = *order.EmployeeNo
			// log.Printf("[DEBUG] Updated OperatorID with employee number: %s -> %s", oldOperatorID, item.OperatorID)
			return
		} else {
			log.Printf("[DEBUG] No valid EmployeeNo found in order either")
			// 使用ERP保底或默认值
			currentERPValue := item.FieldTambahan2
			if currentERPValue != "" && currentERPValue != "unknown" {
				log.Printf("[DEBUG] Using ERP fallback value: %s", currentERPValue)
			} else {
				item.FieldTambahan2 = "Unknown Employee"
				log.Printf("[DEBUG] Using default fallback: Unknown Employee")
			}
			return
		}
	}

	// 如果没有联查解析器，使用默认值
	if idc.joinResolver == nil {
		log.Printf("[DEBUG] No join resolver available, using default fallback")
		item.FieldTambahan2 = "Unknown Employee"
		return
	}

	// 查询员工卡信息
	log.Printf("[DEBUG] SQL: SELECT sc.id, sc.card_number, u.full_name, u.department FROM order_schema.staff_cards sc LEFT JOIN core_schema.users u ON sc.user_id = u.id WHERE sc.id = $1::uuid")
	log.Printf("[DEBUG] Parameters: [%s]", staffCardID)

	if staffCardInfo, err := idc.joinResolver.ResolveStaffCard(staffCardID); err != nil {
		log.Printf("[DEBUG] Staff card query failed: %v, using fallback", err)
		// 使用ERP保底或默认值
		currentERPValue := item.FieldTambahan2
		if currentERPValue != "" && currentERPValue != "unknown" {
			log.Printf("[DEBUG] Using ERP fallback value: %s", currentERPValue)
		} else {
			item.FieldTambahan2 = "Unknown Employee"
			log.Printf("[DEBUG] Using default fallback: Unknown Employee")
		}
	} else if staffCardInfo != nil && staffCardInfo.Name != "" {
		// 数据库查询成功，使用员工姓名
		log.Printf("[DEBUG] Staff card query result: ID=%s, CardNo=%s, Name=%s, Position=%s",
			staffCardInfo.ID, staffCardInfo.CardNo, staffCardInfo.Name, staffCardInfo.Position)
		oldValue := item.FieldTambahan2
		item.FieldTambahan2 = staffCardInfo.Name
		log.Printf("[DEBUG] DB query success - Updated FieldTambahan2: %s -> %s", oldValue, item.FieldTambahan2)

		// 同时更新OperatorID为员工姓名
		// if item.OperatorID == "" || item.OperatorID == "OP001" || item.OperatorID == *order.EmployeeNo {
		// 	oldOperatorID := item.OperatorID
		// 	item.OperatorID = staffCardInfo.Name
		// 	log.Printf("[DEBUG] Updated OperatorID with staff name: %s -> %s", oldOperatorID, item.OperatorID)
		// }
	} else {
		log.Printf("[DEBUG] Staff card query returned no results or empty name, using fallback")
		// 使用ERP保底或默认值
		currentERPValue := item.FieldTambahan2
		if currentERPValue != "" && currentERPValue != "unknown" {
			log.Printf("[DEBUG] Using ERP fallback value: %s", currentERPValue)
		} else {
			item.FieldTambahan2 = "Unknown Employee"
			log.Printf("[DEBUG] Using default fallback: Unknown Employee")
		}
	}
}

// executeStationQuery 执行站点查询
func (idc *IntegratedDataConverter) executeStationQuery(item *erp.TransactionDataItem, stationID int64) {
	log.Printf("[DEBUG] === Station Query ===")
	log.Printf("[DEBUG] SQL: SELECT s.id, COALESCE(s.address, '') as address, COALESCE(s.name, '') as name FROM core_schema.stations s WHERE s.id = $1")
	log.Printf("[DEBUG] Parameters: [%d]", stationID)

	// 记录当前值（可能来自ERP Info）
	currentERPValue := item.AreaSite
	log.Printf("[DEBUG] Current AreaSite value (from ERP): %s", currentERPValue)

	// 优先级1: 数据库联查
	if stationInfo, err := idc.joinResolver.ResolveStation(stationID); err != nil {
		log.Printf("[DEBUG] Station query failed: %v, trying ERP fallback", err)
		// 优先级2: ERP Info保底 - 使用已有的ERP值
		if currentERPValue != "" && currentERPValue != "Jakarta" {
			log.Printf("[DEBUG] Using ERP fallback value: %s", currentERPValue)
			// ERP值已经在之前设置，保持不变
		} else {
			// 优先级3: 默认值兜底
			item.AreaSite = "Unknown Location"
			log.Printf("[DEBUG] Using default fallback: Unknown Location")
		}
	} else if stationInfo != nil {
		// 数据库查询成功，覆盖ERP值
		log.Printf("[DEBUG] Station query result: ID=%d, Address=%s, Name=%s",
			stationInfo.ID, stationInfo.Address, stationInfo.Name)
		oldValue := item.AreaSite
		item.AreaSite = stationInfo.Address
		log.Printf("[DEBUG] DB query success - Updated AreaSite: %s -> %s", oldValue, item.AreaSite)
	} else {
		log.Printf("[DEBUG] Station query returned no results, trying ERP fallback")
		// 优先级2: ERP Info保底
		if currentERPValue != "" && currentERPValue != "Jakarta" {
			log.Printf("[DEBUG] Using ERP fallback value: %s", currentERPValue)
			// ERP值已经在之前设置，保持不变
		} else {
			// 优先级3: 默认值兜底
			item.AreaSite = "Unknown Location"
			log.Printf("[DEBUG] Using default fallback: Unknown Location")
		}
	}
}

// executePromotionQuery 执行优惠查询
func (idc *IntegratedDataConverter) executePromotionQuery(item *erp.TransactionDataItem, orderID string) {
	log.Printf("[DEBUG] === Promotion Query ===")
	log.Printf("[DEBUG] SQL: SELECT op.promotion_type, COALESCE(op.discount_amount, 0) as discount_amount, ... FROM order_schema.order_promotions op WHERE op.order_id = $1 ORDER BY op.created_at ASC")
	log.Printf("[DEBUG] Parameters: [%s]", orderID)

	if promotions, err := idc.joinResolver.ResolvePromotions(orderID); err != nil {
		log.Printf("[DEBUG] Promotion query failed: %v", err)
		item.FlagItemPromotion = 0
		item.PromotionType = ""
	} else if len(promotions) > 0 {
		promo := promotions[0]
		log.Printf("[DEBUG] Promotion query result: Name=%s, Type=%s, PercentDiscount=%.2f, AmountDiscount=%.2f, Flag=%d",
			promo.PromotionName, promo.PromotionType, promo.PercentDiscount, promo.AmountDiscount, promo.FlagItemPromotion)
		// 使用 promotion_name 而不是 promotion_type
		item.PromotionType = promo.PromotionName
		item.PercentDiscount = promo.PercentDiscount
		item.AmountPercentDiscount = promo.AmountPercentDiscount

		// 修复：根据促销类型决定 AmountDiscount 的值
		item.AmountDiscount = item.FinalDiscount
		// "PERCENTAGE" && "FIXED_AMOUNT" && && FREE_ITEM
		// 百分比 && 固定金额 && 免费升数
		// if promo.PromotionType == "FIXED_AMOUNT" && item.FinalDiscount > 0 {
		// 	item.AmountDiscount = item.FinalDiscount
		// 	log.Printf("[DEBUG] 促销类型为FIXED_AMOUNT，使用订单折扣金额: %.2f", item.FinalDiscount)
		// } else {
		// 	item.AmountDiscount = 0
		// 	log.Printf("[DEBUG] 促销类型为 '%s'，AmountDiscount 设置为 0", promo.PromotionType)
		// }

		item.FlagItemPromotion = promo.FlagItemPromotion
	} else {
		log.Printf("[DEBUG] Promotion query returned no results")
		item.FlagItemPromotion = 0
		item.PromotionType = ""

		// 没有促销记录时，检查订单是否有折扣
		if item.FinalDiscount > 0 {
			// 假设没有促销记录但有折扣的情况下，默认为 FIXED_AMOUNT 类型
			item.AmountDiscount = item.FinalDiscount
			log.Printf("[DEBUG] 无促销记录但有订单折扣，AmountDiscount 设置为: %.2f", item.FinalDiscount)
		} else {
			item.AmountDiscount = 0
		}
	}
}

// executeOilProductQuery 执行油品查询
func (idc *IntegratedDataConverter) executeOilProductQuery(item *erp.TransactionDataItem, fuelType string) {
	log.Printf("[DEBUG] === Oil Product Query ===")
	log.Printf("[DEBUG] SQL: SELECT op.code, COALESCE(op.category, '') as category, COALESCE(op.name, '') as name FROM oil_schema.oil_products op WHERE UPPER(op.code) = UPPER($1) OR UPPER(op.name) = UPPER($1) LIMIT 1")
	log.Printf("[DEBUG] Parameters: [%s]", fuelType)

	// 记录当前值（可能来自ERP Info）
	currentERPValue := item.IDProductGroup
	log.Printf("[DEBUG] Current IDProductGroup value (from ERP): %s", currentERPValue)

	// 优先级1: 数据库联查
	if oilInfo, err := idc.joinResolver.ResolveOilProduct(fuelType); err != nil {
		log.Printf("[DEBUG] Oil product query failed: %v, trying ERP fallback", err)
		// 优先级2: ERP Info保底 - 使用已有的ERP值
		if currentERPValue != "" && currentERPValue != "fuel" {
			log.Printf("[DEBUG] Using ERP fallback value: %s", currentERPValue)
			// ERP值已经在之前设置，保持不变
		} else {
			// 优先级3: 默认值兜底
			item.IDProductGroup = "fuel"
			log.Printf("[DEBUG] Using default fallback: fuel")
		}
	} else if oilInfo != nil {
		// 数据库查询成功，覆盖ERP值
		log.Printf("[DEBUG] Oil product query result: Code=%s, Category=%s, Name=%s",
			oilInfo.Code, oilInfo.Category, oilInfo.Name)
		oldValue := item.IDProductGroup
		item.IDProductGroup = oilInfo.Category
		log.Printf("[DEBUG] DB query success - Updated IDProductGroup: %s -> %s", oldValue, item.IDProductGroup)
	} else {
		log.Printf("[DEBUG] Oil product query returned no results, trying ERP fallback")
		// 优先级2: ERP Info保底
		if currentERPValue != "" && currentERPValue != "fuel" {
			log.Printf("[DEBUG] Using ERP fallback value: %s", currentERPValue)
			// ERP值已经在之前设置，保持不变
		} else {
			// 优先级3: 默认值兜底
			item.IDProductGroup = "fuel"
			log.Printf("[DEBUG] Using default fallback: fuel")
		}
	}
}

// executeMemberPhoneQuery 方法已移除 - CustomerPhoneNo 现在直接从订单字段获取

// ConvertOrdersToRequest 批量转换订单列表为ERP请求
func (idc *IntegratedDataConverter) ConvertOrdersToRequest(orders []*order_service.Order, apiKey, user, password string) *erp.DispenserTransactionRequest {
	log.Printf("[DEBUG] === Batch Conversion Started ===")
	log.Printf("[DEBUG] Converting %d orders to ERP transaction request", len(orders))
	log.Printf("[DEBUG] ERP API credentials - User: %s, Key: %s", user, apiKey)

	transactions := make([]erp.TransactionDataItem, 0, len(orders))

	for i, order := range orders {
		log.Printf("[DEBUG] Processing order %d/%d (OrderID: %s)", i+1, len(orders), order.ID)
		item, err := idc.ConvertOrderToTransactionItem(order)
		if err != nil {
			log.Printf("[ERROR] Failed to convert order %s: %v", order.ID, err)
			continue // 跳过无法转换的订单
		}

		// 验证转换结果
		validationIssues := idc.ValidateTransactionItem(&item)
		if len(validationIssues) > 0 {
			log.Printf("[DEBUG] Validation issues for order %s: %v", order.ID, validationIssues)
		} else {
			log.Printf("[DEBUG] Order %s validation passed", order.ID)
		}

		transactions = append(transactions, item)
	}

	request := &erp.DispenserTransactionRequest{
		Key:              apiKey,
		User:             user,
		Password:         password,
		TransactionCount: len(transactions),
		MessageID:        fmt.Sprintf("batch_%d", len(transactions)),
		Transactions:     transactions,
	}

	log.Printf("[DEBUG] === Batch Conversion Completed ===")
	log.Printf("[DEBUG] Generated ERP request with %d transactions, MessageID: %s",
		request.TransactionCount, request.MessageID)

	return request
}

// 辅助方法

// getOperatorID 方法已删除 - OperatorID 现在只来自元数据ERP信息

func (idc *IntegratedDataConverter) getCustomerPhone(order *order_service.Order) string {
	// 直接使用订单中的客户手机号
	if order.CustomerPhone != nil && *order.CustomerPhone != "" {
		log.Printf("[DEBUG] Using CustomerPhone from order: %s", *order.CustomerPhone)
		return *order.CustomerPhone
	}
	log.Printf("[DEBUG] No CustomerPhone found in order")
	return ""
}

func (idc *IntegratedDataConverter) getVehicleID(order *order_service.Order) string {
	// 直接使用订单中的车牌号作为VehicleID
	if order.LicensePlate != nil && *order.LicensePlate != "" {
		log.Printf("[DEBUG] Using LicensePlate as VehicleID: %s", *order.LicensePlate)
		return *order.LicensePlate
	}
	return ""
}

func (idc *IntegratedDataConverter) getCustomerName(order *order_service.Order) string {
	if order.CustomerName != nil {
		log.Printf("[DEBUG] Using order CustomerName: %s", *order.CustomerName)
		return *order.CustomerName
	}
	log.Printf("[DEBUG] No CustomerName found, using default: Guest Customer")
	return "Guest"
}

func (idc *IntegratedDataConverter) mapFuelTypeToProductID(fuelType string) string {
	log.Printf("[DEBUG] === Fuel Type to ProductID Mapping ===")
	log.Printf("[DEBUG] Input FuelType: %s", fuelType)

	// 优先级1: 查询油品表获取实际产品名称
	if oilInfo, err := idc.joinResolver.ResolveOilProduct(fuelType); err == nil && oilInfo != nil {
		// 将油品名称转为全大写作为ProductID
		productID := strings.ToUpper(oilInfo.Name)
		log.Printf("[DEBUG] DB query success - Oil product found: Code=%s, Name=%s, ProductID=%s",
			oilInfo.Code, oilInfo.Name, productID)
		return productID
	}

	log.Printf("[DEBUG] Oil product query failed or returned no results, using fallback mapping")

	// 优先级2: 回退到硬编码映射（保持向后兼容）
	// TODO 配置文件
	var productID string
	switch fuelType {
	// BP 92, BPULTIMATE，BPULTIMATEDIESEL
	case "101":
		productID = "BP 92"
	case "102":
		productID = "BPULTIMATE"
	case "103":
		productID = "BPULTIMATEDIESEL"
	default:
		productID = "" // 默认值
		log.Printf("[DEBUG] Unknown fuel type '%s', using default ProductID: %s", fuelType, productID)
		return productID
	}

	log.Printf("[DEBUG] Fallback mapping - FuelType '%s' -> ProductID: %s", fuelType, productID)
	return productID
}

// ValidateTransactionItem 验证转换后的交易项完整性
func (idc *IntegratedDataConverter) ValidateTransactionItem(item *erp.TransactionDataItem) []string {
	log.Printf("[DEBUG] === Transaction Item Validation ===")
	log.Printf("[DEBUG] Validating TransactionID: %s", item.TransactionID)

	var issues []string

	// 基本字段验证
	if item.TransactionID == "" {
		issues = append(issues, "TransactionID is empty")
		log.Printf("[DEBUG] ISSUE: TransactionID is empty")
	}
	if item.SlipNumber == "" {
		issues = append(issues, "SlipNumber is empty")
		log.Printf("[DEBUG] ISSUE: SlipNumber is empty")
	}
	if item.Amount <= 0 {
		issues = append(issues, "Amount must be positive")
		log.Printf("[DEBUG] ISSUE: Amount must be positive, current: %.2f", item.Amount)
	} else {
		log.Printf("[DEBUG] OK: Amount = %.2f", item.Amount)
	}
	if item.Volume <= 0 {
		issues = append(issues, "Volume must be positive")
		log.Printf("[DEBUG] ISSUE: Volume must be positive, current: %.2f", item.Volume)
	} else {
		log.Printf("[DEBUG] OK: Volume = %.2f", item.Volume)
	}
	if item.Price <= 0 {
		issues = append(issues, "Price must be positive")
		log.Printf("[DEBUG] ISSUE: Price must be positive, current: %.2f", item.Price)
	} else {
		log.Printf("[DEBUG] OK: Price = %.2f", item.Price)
	}

	// 关键字段检查
	log.Printf("[DEBUG] Checking key fields:")
	log.Printf("[DEBUG] - ProductID: %s", item.ProductID)
	log.Printf("[DEBUG] - CustomerName: %s", item.CustomerName)
	log.Printf("[DEBUG] - OperatorID: %s", item.OperatorID)
	log.Printf("[DEBUG] - SiteID: %s", item.SiteID)
	log.Printf("[DEBUG] - DispenserNumber: %d", item.DispenserNumber)
	log.Printf("[DEBUG] - NozzleNumber: %d", item.NozzleNumber)
	log.Printf("[DEBUG] - VehicleType: %d", item.VehicleType)
	log.Printf("[DEBUG] - TransactionDate: %s", item.TransactionDate)

	// 联查数据完整性验证
	log.Printf("[DEBUG] Validating join data completeness:")
	joinDataIssues := ValidateJoinDataCompleteness(item)
	for _, issue := range joinDataIssues {
		log.Printf("[DEBUG] JOIN ISSUE: %s", issue)
	}
	issues = append(issues, joinDataIssues...)

	if len(issues) == 0 {
		log.Printf("[DEBUG] Validation completed: NO ISSUES FOUND")
	} else {
		log.Printf("[DEBUG] Validation completed: %d ISSUES FOUND", len(issues))
	}

	return issues
}

// mapProductName 映射产品名称，去除空格等格式化
func (idc *IntegratedDataConverter) mapProductName(productName string) string {
	// 产品名称映射表
	productMapping := map[string]string{
		"BP Diesel":          "BPDIESEL",
		"BP Ultimate Diesel": "BPULTIMATEDIESEL",
		"BP 92":              "BP 92", // 保持不变
	}

	// 查找映射
	if mappedName, exists := productMapping[productName]; exists {
		return mappedName
	}

	// 如果没有找到映射，返回原始名称
	return productName
}

// mapProductIDToGroup 根据 ProductID 映射到 IDProductGroup
// 业务规则：BP 92, BPULTIMATE → Gasoline; BPULTIMATEDIESEL → Diesel
func (idc *IntegratedDataConverter) mapProductIDToGroup(productID string) string {
	switch productID {
	case "BP 92", "BPULTIMATE":
		return "Gasoline"
	case "BPULTIMATEDIESEL":
		return "Diesel"
	default:
		// 默认根据产品名称推断
		productUpper := strings.ToUpper(productID)
		if strings.Contains(productUpper, "DIESEL") {
			return "Diesel"
		}
		return "Gasoline" // 默认为汽油
	}
}
