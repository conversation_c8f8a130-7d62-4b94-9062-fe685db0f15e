// Code generated by ent, DO NOT EDIT.

package order

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the order type in the database.
	Label = "order"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldOrderNumber holds the string denoting the order_number field in the database.
	FieldOrderNumber = "order_number"
	// FieldCustomerID holds the string denoting the customer_id field in the database.
	FieldCustomerID = "customer_id"
	// FieldCustomerPhone holds the string denoting the customer_phone field in the database.
	FieldCustomerPhone = "customer_phone"
	// FieldLicensePlate holds the string denoting the license_plate field in the database.
	FieldLicensePlate = "license_plate"
	// FieldCustomerName holds the string denoting the customer_name field in the database.
	FieldCustomerName = "customer_name"
	// FieldStationID holds the string denoting the station_id field in the database.
	FieldStationID = "station_id"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldTotalAmount holds the string denoting the total_amount field in the database.
	FieldTotalAmount = "total_amount"
	// FieldDiscountAmount holds the string denoting the discount_amount field in the database.
	FieldDiscountAmount = "discount_amount"
	// FieldFinalAmount holds the string denoting the final_amount field in the database.
	FieldFinalAmount = "final_amount"
	// FieldTaxAmount holds the string denoting the tax_amount field in the database.
	FieldTaxAmount = "tax_amount"
	// FieldPaidAmount holds the string denoting the paid_amount field in the database.
	FieldPaidAmount = "paid_amount"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldCompletedAt holds the string denoting the completed_at field in the database.
	FieldCompletedAt = "completed_at"
	// FieldCancelledAt holds the string denoting the cancelled_at field in the database.
	FieldCancelledAt = "cancelled_at"
	// FieldEmployeeNo holds the string denoting the employee_no field in the database.
	FieldEmployeeNo = "employee_no"
	// FieldStaffCardID holds the string denoting the staff_card_id field in the database.
	FieldStaffCardID = "staff_card_id"
	// EdgeFuelTransactionLink holds the string denoting the fuel_transaction_link edge name in mutations.
	EdgeFuelTransactionLink = "fuel_transaction_link"
	// Table holds the table name of the order in the database.
	Table = "orders"
	// FuelTransactionLinkTable is the table that holds the fuel_transaction_link relation/edge.
	FuelTransactionLinkTable = "fuel_transaction_order_links"
	// FuelTransactionLinkInverseTable is the table name for the FuelTransactionOrderLink entity.
	// It exists in this package in order to avoid circular dependency with the "fueltransactionorderlink" package.
	FuelTransactionLinkInverseTable = "fuel_transaction_order_links"
	// FuelTransactionLinkColumn is the table column denoting the fuel_transaction_link relation/edge.
	FuelTransactionLinkColumn = "order_id"
)

// Columns holds all SQL columns for order fields.
var Columns = []string{
	FieldID,
	FieldOrderNumber,
	FieldCustomerID,
	FieldCustomerPhone,
	FieldLicensePlate,
	FieldCustomerName,
	FieldStationID,
	FieldStatus,
	FieldTotalAmount,
	FieldDiscountAmount,
	FieldFinalAmount,
	FieldTaxAmount,
	FieldPaidAmount,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldCompletedAt,
	FieldCancelledAt,
	FieldEmployeeNo,
	FieldStaffCardID,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// OrderNumberValidator is a validator for the "order_number" field. It is called by the builders before save.
	OrderNumberValidator func(string) error
	// CustomerPhoneValidator is a validator for the "customer_phone" field. It is called by the builders before save.
	CustomerPhoneValidator func(string) error
	// LicensePlateValidator is a validator for the "license_plate" field. It is called by the builders before save.
	LicensePlateValidator func(string) error
	// CustomerNameValidator is a validator for the "customer_name" field. It is called by the builders before save.
	CustomerNameValidator func(string) error
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus string
	// StatusValidator is a validator for the "status" field. It is called by the builders before save.
	StatusValidator func(string) error
	// DefaultTotalAmount holds the default value on creation for the "total_amount" field.
	DefaultTotalAmount float64
	// DefaultDiscountAmount holds the default value on creation for the "discount_amount" field.
	DefaultDiscountAmount float64
	// DefaultFinalAmount holds the default value on creation for the "final_amount" field.
	DefaultFinalAmount float64
	// DefaultTaxAmount holds the default value on creation for the "tax_amount" field.
	DefaultTaxAmount float64
	// DefaultPaidAmount holds the default value on creation for the "paid_amount" field.
	DefaultPaidAmount float64
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// EmployeeNoValidator is a validator for the "employee_no" field. It is called by the builders before save.
	EmployeeNoValidator func(string) error
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the Order queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByOrderNumber orders the results by the order_number field.
func ByOrderNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrderNumber, opts...).ToFunc()
}

// ByCustomerID orders the results by the customer_id field.
func ByCustomerID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCustomerID, opts...).ToFunc()
}

// ByCustomerPhone orders the results by the customer_phone field.
func ByCustomerPhone(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCustomerPhone, opts...).ToFunc()
}

// ByLicensePlate orders the results by the license_plate field.
func ByLicensePlate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLicensePlate, opts...).ToFunc()
}

// ByCustomerName orders the results by the customer_name field.
func ByCustomerName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCustomerName, opts...).ToFunc()
}

// ByStationID orders the results by the station_id field.
func ByStationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStationID, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByTotalAmount orders the results by the total_amount field.
func ByTotalAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTotalAmount, opts...).ToFunc()
}

// ByDiscountAmount orders the results by the discount_amount field.
func ByDiscountAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDiscountAmount, opts...).ToFunc()
}

// ByFinalAmount orders the results by the final_amount field.
func ByFinalAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFinalAmount, opts...).ToFunc()
}

// ByTaxAmount orders the results by the tax_amount field.
func ByTaxAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTaxAmount, opts...).ToFunc()
}

// ByPaidAmount orders the results by the paid_amount field.
func ByPaidAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaidAmount, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByCompletedAt orders the results by the completed_at field.
func ByCompletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCompletedAt, opts...).ToFunc()
}

// ByCancelledAt orders the results by the cancelled_at field.
func ByCancelledAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCancelledAt, opts...).ToFunc()
}

// ByEmployeeNo orders the results by the employee_no field.
func ByEmployeeNo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmployeeNo, opts...).ToFunc()
}

// ByStaffCardID orders the results by the staff_card_id field.
func ByStaffCardID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStaffCardID, opts...).ToFunc()
}

// ByFuelTransactionLinkField orders the results by fuel_transaction_link field.
func ByFuelTransactionLinkField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newFuelTransactionLinkStep(), sql.OrderByField(field, opts...))
	}
}
func newFuelTransactionLinkStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(FuelTransactionLinkInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2O, false, FuelTransactionLinkTable, FuelTransactionLinkColumn),
	)
}
