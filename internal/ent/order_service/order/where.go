// Code generated by ent, DO NOT EDIT.

package order

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldID, id))
}

// OrderNumber applies equality check predicate on the "order_number" field. It's identical to OrderNumberEQ.
func OrderNumber(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldOrderNumber, v))
}

// CustomerID applies equality check predicate on the "customer_id" field. It's identical to CustomerIDEQ.
func CustomerID(v int64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCustomerID, v))
}

// CustomerPhone applies equality check predicate on the "customer_phone" field. It's identical to CustomerPhoneEQ.
func CustomerPhone(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCustomerPhone, v))
}

// LicensePlate applies equality check predicate on the "license_plate" field. It's identical to LicensePlateEQ.
func LicensePlate(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldLicensePlate, v))
}

// CustomerName applies equality check predicate on the "customer_name" field. It's identical to CustomerNameEQ.
func CustomerName(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCustomerName, v))
}

// StationID applies equality check predicate on the "station_id" field. It's identical to StationIDEQ.
func StationID(v int64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldStationID, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldStatus, v))
}

// TotalAmount applies equality check predicate on the "total_amount" field. It's identical to TotalAmountEQ.
func TotalAmount(v float64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldTotalAmount, v))
}

// DiscountAmount applies equality check predicate on the "discount_amount" field. It's identical to DiscountAmountEQ.
func DiscountAmount(v float64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldDiscountAmount, v))
}

// FinalAmount applies equality check predicate on the "final_amount" field. It's identical to FinalAmountEQ.
func FinalAmount(v float64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldFinalAmount, v))
}

// TaxAmount applies equality check predicate on the "tax_amount" field. It's identical to TaxAmountEQ.
func TaxAmount(v float64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldTaxAmount, v))
}

// PaidAmount applies equality check predicate on the "paid_amount" field. It's identical to PaidAmountEQ.
func PaidAmount(v float64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldPaidAmount, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldUpdatedAt, v))
}

// CompletedAt applies equality check predicate on the "completed_at" field. It's identical to CompletedAtEQ.
func CompletedAt(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCompletedAt, v))
}

// CancelledAt applies equality check predicate on the "cancelled_at" field. It's identical to CancelledAtEQ.
func CancelledAt(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCancelledAt, v))
}

// EmployeeNo applies equality check predicate on the "employee_no" field. It's identical to EmployeeNoEQ.
func EmployeeNo(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldEmployeeNo, v))
}

// StaffCardID applies equality check predicate on the "staff_card_id" field. It's identical to StaffCardIDEQ.
func StaffCardID(v int64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldStaffCardID, v))
}

// OrderNumberEQ applies the EQ predicate on the "order_number" field.
func OrderNumberEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldOrderNumber, v))
}

// OrderNumberNEQ applies the NEQ predicate on the "order_number" field.
func OrderNumberNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldOrderNumber, v))
}

// OrderNumberIn applies the In predicate on the "order_number" field.
func OrderNumberIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldOrderNumber, vs...))
}

// OrderNumberNotIn applies the NotIn predicate on the "order_number" field.
func OrderNumberNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldOrderNumber, vs...))
}

// OrderNumberGT applies the GT predicate on the "order_number" field.
func OrderNumberGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldOrderNumber, v))
}

// OrderNumberGTE applies the GTE predicate on the "order_number" field.
func OrderNumberGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldOrderNumber, v))
}

// OrderNumberLT applies the LT predicate on the "order_number" field.
func OrderNumberLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldOrderNumber, v))
}

// OrderNumberLTE applies the LTE predicate on the "order_number" field.
func OrderNumberLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldOrderNumber, v))
}

// OrderNumberContains applies the Contains predicate on the "order_number" field.
func OrderNumberContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldOrderNumber, v))
}

// OrderNumberHasPrefix applies the HasPrefix predicate on the "order_number" field.
func OrderNumberHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldOrderNumber, v))
}

// OrderNumberHasSuffix applies the HasSuffix predicate on the "order_number" field.
func OrderNumberHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldOrderNumber, v))
}

// OrderNumberEqualFold applies the EqualFold predicate on the "order_number" field.
func OrderNumberEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldOrderNumber, v))
}

// OrderNumberContainsFold applies the ContainsFold predicate on the "order_number" field.
func OrderNumberContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldOrderNumber, v))
}

// CustomerIDEQ applies the EQ predicate on the "customer_id" field.
func CustomerIDEQ(v int64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCustomerID, v))
}

// CustomerIDNEQ applies the NEQ predicate on the "customer_id" field.
func CustomerIDNEQ(v int64) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldCustomerID, v))
}

// CustomerIDIn applies the In predicate on the "customer_id" field.
func CustomerIDIn(vs ...int64) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldCustomerID, vs...))
}

// CustomerIDNotIn applies the NotIn predicate on the "customer_id" field.
func CustomerIDNotIn(vs ...int64) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldCustomerID, vs...))
}

// CustomerIDGT applies the GT predicate on the "customer_id" field.
func CustomerIDGT(v int64) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldCustomerID, v))
}

// CustomerIDGTE applies the GTE predicate on the "customer_id" field.
func CustomerIDGTE(v int64) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldCustomerID, v))
}

// CustomerIDLT applies the LT predicate on the "customer_id" field.
func CustomerIDLT(v int64) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldCustomerID, v))
}

// CustomerIDLTE applies the LTE predicate on the "customer_id" field.
func CustomerIDLTE(v int64) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldCustomerID, v))
}

// CustomerIDIsNil applies the IsNil predicate on the "customer_id" field.
func CustomerIDIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldCustomerID))
}

// CustomerIDNotNil applies the NotNil predicate on the "customer_id" field.
func CustomerIDNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldCustomerID))
}

// CustomerPhoneEQ applies the EQ predicate on the "customer_phone" field.
func CustomerPhoneEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCustomerPhone, v))
}

// CustomerPhoneNEQ applies the NEQ predicate on the "customer_phone" field.
func CustomerPhoneNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldCustomerPhone, v))
}

// CustomerPhoneIn applies the In predicate on the "customer_phone" field.
func CustomerPhoneIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldCustomerPhone, vs...))
}

// CustomerPhoneNotIn applies the NotIn predicate on the "customer_phone" field.
func CustomerPhoneNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldCustomerPhone, vs...))
}

// CustomerPhoneGT applies the GT predicate on the "customer_phone" field.
func CustomerPhoneGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldCustomerPhone, v))
}

// CustomerPhoneGTE applies the GTE predicate on the "customer_phone" field.
func CustomerPhoneGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldCustomerPhone, v))
}

// CustomerPhoneLT applies the LT predicate on the "customer_phone" field.
func CustomerPhoneLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldCustomerPhone, v))
}

// CustomerPhoneLTE applies the LTE predicate on the "customer_phone" field.
func CustomerPhoneLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldCustomerPhone, v))
}

// CustomerPhoneContains applies the Contains predicate on the "customer_phone" field.
func CustomerPhoneContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldCustomerPhone, v))
}

// CustomerPhoneHasPrefix applies the HasPrefix predicate on the "customer_phone" field.
func CustomerPhoneHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldCustomerPhone, v))
}

// CustomerPhoneHasSuffix applies the HasSuffix predicate on the "customer_phone" field.
func CustomerPhoneHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldCustomerPhone, v))
}

// CustomerPhoneIsNil applies the IsNil predicate on the "customer_phone" field.
func CustomerPhoneIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldCustomerPhone))
}

// CustomerPhoneNotNil applies the NotNil predicate on the "customer_phone" field.
func CustomerPhoneNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldCustomerPhone))
}

// CustomerPhoneEqualFold applies the EqualFold predicate on the "customer_phone" field.
func CustomerPhoneEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldCustomerPhone, v))
}

// CustomerPhoneContainsFold applies the ContainsFold predicate on the "customer_phone" field.
func CustomerPhoneContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldCustomerPhone, v))
}

// LicensePlateEQ applies the EQ predicate on the "license_plate" field.
func LicensePlateEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldLicensePlate, v))
}

// LicensePlateNEQ applies the NEQ predicate on the "license_plate" field.
func LicensePlateNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldLicensePlate, v))
}

// LicensePlateIn applies the In predicate on the "license_plate" field.
func LicensePlateIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldLicensePlate, vs...))
}

// LicensePlateNotIn applies the NotIn predicate on the "license_plate" field.
func LicensePlateNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldLicensePlate, vs...))
}

// LicensePlateGT applies the GT predicate on the "license_plate" field.
func LicensePlateGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldLicensePlate, v))
}

// LicensePlateGTE applies the GTE predicate on the "license_plate" field.
func LicensePlateGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldLicensePlate, v))
}

// LicensePlateLT applies the LT predicate on the "license_plate" field.
func LicensePlateLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldLicensePlate, v))
}

// LicensePlateLTE applies the LTE predicate on the "license_plate" field.
func LicensePlateLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldLicensePlate, v))
}

// LicensePlateContains applies the Contains predicate on the "license_plate" field.
func LicensePlateContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldLicensePlate, v))
}

// LicensePlateHasPrefix applies the HasPrefix predicate on the "license_plate" field.
func LicensePlateHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldLicensePlate, v))
}

// LicensePlateHasSuffix applies the HasSuffix predicate on the "license_plate" field.
func LicensePlateHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldLicensePlate, v))
}

// LicensePlateIsNil applies the IsNil predicate on the "license_plate" field.
func LicensePlateIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldLicensePlate))
}

// LicensePlateNotNil applies the NotNil predicate on the "license_plate" field.
func LicensePlateNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldLicensePlate))
}

// LicensePlateEqualFold applies the EqualFold predicate on the "license_plate" field.
func LicensePlateEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldLicensePlate, v))
}

// LicensePlateContainsFold applies the ContainsFold predicate on the "license_plate" field.
func LicensePlateContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldLicensePlate, v))
}

// CustomerNameEQ applies the EQ predicate on the "customer_name" field.
func CustomerNameEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCustomerName, v))
}

// CustomerNameNEQ applies the NEQ predicate on the "customer_name" field.
func CustomerNameNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldCustomerName, v))
}

// CustomerNameIn applies the In predicate on the "customer_name" field.
func CustomerNameIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldCustomerName, vs...))
}

// CustomerNameNotIn applies the NotIn predicate on the "customer_name" field.
func CustomerNameNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldCustomerName, vs...))
}

// CustomerNameGT applies the GT predicate on the "customer_name" field.
func CustomerNameGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldCustomerName, v))
}

// CustomerNameGTE applies the GTE predicate on the "customer_name" field.
func CustomerNameGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldCustomerName, v))
}

// CustomerNameLT applies the LT predicate on the "customer_name" field.
func CustomerNameLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldCustomerName, v))
}

// CustomerNameLTE applies the LTE predicate on the "customer_name" field.
func CustomerNameLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldCustomerName, v))
}

// CustomerNameContains applies the Contains predicate on the "customer_name" field.
func CustomerNameContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldCustomerName, v))
}

// CustomerNameHasPrefix applies the HasPrefix predicate on the "customer_name" field.
func CustomerNameHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldCustomerName, v))
}

// CustomerNameHasSuffix applies the HasSuffix predicate on the "customer_name" field.
func CustomerNameHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldCustomerName, v))
}

// CustomerNameIsNil applies the IsNil predicate on the "customer_name" field.
func CustomerNameIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldCustomerName))
}

// CustomerNameNotNil applies the NotNil predicate on the "customer_name" field.
func CustomerNameNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldCustomerName))
}

// CustomerNameEqualFold applies the EqualFold predicate on the "customer_name" field.
func CustomerNameEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldCustomerName, v))
}

// CustomerNameContainsFold applies the ContainsFold predicate on the "customer_name" field.
func CustomerNameContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldCustomerName, v))
}

// StationIDEQ applies the EQ predicate on the "station_id" field.
func StationIDEQ(v int64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldStationID, v))
}

// StationIDNEQ applies the NEQ predicate on the "station_id" field.
func StationIDNEQ(v int64) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldStationID, v))
}

// StationIDIn applies the In predicate on the "station_id" field.
func StationIDIn(vs ...int64) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldStationID, vs...))
}

// StationIDNotIn applies the NotIn predicate on the "station_id" field.
func StationIDNotIn(vs ...int64) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldStationID, vs...))
}

// StationIDGT applies the GT predicate on the "station_id" field.
func StationIDGT(v int64) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldStationID, v))
}

// StationIDGTE applies the GTE predicate on the "station_id" field.
func StationIDGTE(v int64) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldStationID, v))
}

// StationIDLT applies the LT predicate on the "station_id" field.
func StationIDLT(v int64) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldStationID, v))
}

// StationIDLTE applies the LTE predicate on the "station_id" field.
func StationIDLTE(v int64) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldStationID, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldStatus, v))
}

// TotalAmountEQ applies the EQ predicate on the "total_amount" field.
func TotalAmountEQ(v float64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldTotalAmount, v))
}

// TotalAmountNEQ applies the NEQ predicate on the "total_amount" field.
func TotalAmountNEQ(v float64) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldTotalAmount, v))
}

// TotalAmountIn applies the In predicate on the "total_amount" field.
func TotalAmountIn(vs ...float64) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldTotalAmount, vs...))
}

// TotalAmountNotIn applies the NotIn predicate on the "total_amount" field.
func TotalAmountNotIn(vs ...float64) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldTotalAmount, vs...))
}

// TotalAmountGT applies the GT predicate on the "total_amount" field.
func TotalAmountGT(v float64) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldTotalAmount, v))
}

// TotalAmountGTE applies the GTE predicate on the "total_amount" field.
func TotalAmountGTE(v float64) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldTotalAmount, v))
}

// TotalAmountLT applies the LT predicate on the "total_amount" field.
func TotalAmountLT(v float64) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldTotalAmount, v))
}

// TotalAmountLTE applies the LTE predicate on the "total_amount" field.
func TotalAmountLTE(v float64) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldTotalAmount, v))
}

// DiscountAmountEQ applies the EQ predicate on the "discount_amount" field.
func DiscountAmountEQ(v float64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldDiscountAmount, v))
}

// DiscountAmountNEQ applies the NEQ predicate on the "discount_amount" field.
func DiscountAmountNEQ(v float64) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldDiscountAmount, v))
}

// DiscountAmountIn applies the In predicate on the "discount_amount" field.
func DiscountAmountIn(vs ...float64) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldDiscountAmount, vs...))
}

// DiscountAmountNotIn applies the NotIn predicate on the "discount_amount" field.
func DiscountAmountNotIn(vs ...float64) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldDiscountAmount, vs...))
}

// DiscountAmountGT applies the GT predicate on the "discount_amount" field.
func DiscountAmountGT(v float64) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldDiscountAmount, v))
}

// DiscountAmountGTE applies the GTE predicate on the "discount_amount" field.
func DiscountAmountGTE(v float64) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldDiscountAmount, v))
}

// DiscountAmountLT applies the LT predicate on the "discount_amount" field.
func DiscountAmountLT(v float64) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldDiscountAmount, v))
}

// DiscountAmountLTE applies the LTE predicate on the "discount_amount" field.
func DiscountAmountLTE(v float64) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldDiscountAmount, v))
}

// FinalAmountEQ applies the EQ predicate on the "final_amount" field.
func FinalAmountEQ(v float64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldFinalAmount, v))
}

// FinalAmountNEQ applies the NEQ predicate on the "final_amount" field.
func FinalAmountNEQ(v float64) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldFinalAmount, v))
}

// FinalAmountIn applies the In predicate on the "final_amount" field.
func FinalAmountIn(vs ...float64) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldFinalAmount, vs...))
}

// FinalAmountNotIn applies the NotIn predicate on the "final_amount" field.
func FinalAmountNotIn(vs ...float64) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldFinalAmount, vs...))
}

// FinalAmountGT applies the GT predicate on the "final_amount" field.
func FinalAmountGT(v float64) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldFinalAmount, v))
}

// FinalAmountGTE applies the GTE predicate on the "final_amount" field.
func FinalAmountGTE(v float64) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldFinalAmount, v))
}

// FinalAmountLT applies the LT predicate on the "final_amount" field.
func FinalAmountLT(v float64) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldFinalAmount, v))
}

// FinalAmountLTE applies the LTE predicate on the "final_amount" field.
func FinalAmountLTE(v float64) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldFinalAmount, v))
}

// TaxAmountEQ applies the EQ predicate on the "tax_amount" field.
func TaxAmountEQ(v float64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldTaxAmount, v))
}

// TaxAmountNEQ applies the NEQ predicate on the "tax_amount" field.
func TaxAmountNEQ(v float64) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldTaxAmount, v))
}

// TaxAmountIn applies the In predicate on the "tax_amount" field.
func TaxAmountIn(vs ...float64) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldTaxAmount, vs...))
}

// TaxAmountNotIn applies the NotIn predicate on the "tax_amount" field.
func TaxAmountNotIn(vs ...float64) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldTaxAmount, vs...))
}

// TaxAmountGT applies the GT predicate on the "tax_amount" field.
func TaxAmountGT(v float64) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldTaxAmount, v))
}

// TaxAmountGTE applies the GTE predicate on the "tax_amount" field.
func TaxAmountGTE(v float64) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldTaxAmount, v))
}

// TaxAmountLT applies the LT predicate on the "tax_amount" field.
func TaxAmountLT(v float64) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldTaxAmount, v))
}

// TaxAmountLTE applies the LTE predicate on the "tax_amount" field.
func TaxAmountLTE(v float64) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldTaxAmount, v))
}

// PaidAmountEQ applies the EQ predicate on the "paid_amount" field.
func PaidAmountEQ(v float64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldPaidAmount, v))
}

// PaidAmountNEQ applies the NEQ predicate on the "paid_amount" field.
func PaidAmountNEQ(v float64) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldPaidAmount, v))
}

// PaidAmountIn applies the In predicate on the "paid_amount" field.
func PaidAmountIn(vs ...float64) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldPaidAmount, vs...))
}

// PaidAmountNotIn applies the NotIn predicate on the "paid_amount" field.
func PaidAmountNotIn(vs ...float64) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldPaidAmount, vs...))
}

// PaidAmountGT applies the GT predicate on the "paid_amount" field.
func PaidAmountGT(v float64) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldPaidAmount, v))
}

// PaidAmountGTE applies the GTE predicate on the "paid_amount" field.
func PaidAmountGTE(v float64) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldPaidAmount, v))
}

// PaidAmountLT applies the LT predicate on the "paid_amount" field.
func PaidAmountLT(v float64) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldPaidAmount, v))
}

// PaidAmountLTE applies the LTE predicate on the "paid_amount" field.
func PaidAmountLTE(v float64) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldPaidAmount, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldUpdatedAt, v))
}

// CompletedAtEQ applies the EQ predicate on the "completed_at" field.
func CompletedAtEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCompletedAt, v))
}

// CompletedAtNEQ applies the NEQ predicate on the "completed_at" field.
func CompletedAtNEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldCompletedAt, v))
}

// CompletedAtIn applies the In predicate on the "completed_at" field.
func CompletedAtIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldCompletedAt, vs...))
}

// CompletedAtNotIn applies the NotIn predicate on the "completed_at" field.
func CompletedAtNotIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldCompletedAt, vs...))
}

// CompletedAtGT applies the GT predicate on the "completed_at" field.
func CompletedAtGT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldCompletedAt, v))
}

// CompletedAtGTE applies the GTE predicate on the "completed_at" field.
func CompletedAtGTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldCompletedAt, v))
}

// CompletedAtLT applies the LT predicate on the "completed_at" field.
func CompletedAtLT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldCompletedAt, v))
}

// CompletedAtLTE applies the LTE predicate on the "completed_at" field.
func CompletedAtLTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldCompletedAt, v))
}

// CompletedAtIsNil applies the IsNil predicate on the "completed_at" field.
func CompletedAtIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldCompletedAt))
}

// CompletedAtNotNil applies the NotNil predicate on the "completed_at" field.
func CompletedAtNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldCompletedAt))
}

// CancelledAtEQ applies the EQ predicate on the "cancelled_at" field.
func CancelledAtEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldCancelledAt, v))
}

// CancelledAtNEQ applies the NEQ predicate on the "cancelled_at" field.
func CancelledAtNEQ(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldCancelledAt, v))
}

// CancelledAtIn applies the In predicate on the "cancelled_at" field.
func CancelledAtIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldCancelledAt, vs...))
}

// CancelledAtNotIn applies the NotIn predicate on the "cancelled_at" field.
func CancelledAtNotIn(vs ...time.Time) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldCancelledAt, vs...))
}

// CancelledAtGT applies the GT predicate on the "cancelled_at" field.
func CancelledAtGT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldCancelledAt, v))
}

// CancelledAtGTE applies the GTE predicate on the "cancelled_at" field.
func CancelledAtGTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldCancelledAt, v))
}

// CancelledAtLT applies the LT predicate on the "cancelled_at" field.
func CancelledAtLT(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldCancelledAt, v))
}

// CancelledAtLTE applies the LTE predicate on the "cancelled_at" field.
func CancelledAtLTE(v time.Time) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldCancelledAt, v))
}

// CancelledAtIsNil applies the IsNil predicate on the "cancelled_at" field.
func CancelledAtIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldCancelledAt))
}

// CancelledAtNotNil applies the NotNil predicate on the "cancelled_at" field.
func CancelledAtNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldCancelledAt))
}

// EmployeeNoEQ applies the EQ predicate on the "employee_no" field.
func EmployeeNoEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldEmployeeNo, v))
}

// EmployeeNoNEQ applies the NEQ predicate on the "employee_no" field.
func EmployeeNoNEQ(v string) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldEmployeeNo, v))
}

// EmployeeNoIn applies the In predicate on the "employee_no" field.
func EmployeeNoIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldEmployeeNo, vs...))
}

// EmployeeNoNotIn applies the NotIn predicate on the "employee_no" field.
func EmployeeNoNotIn(vs ...string) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldEmployeeNo, vs...))
}

// EmployeeNoGT applies the GT predicate on the "employee_no" field.
func EmployeeNoGT(v string) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldEmployeeNo, v))
}

// EmployeeNoGTE applies the GTE predicate on the "employee_no" field.
func EmployeeNoGTE(v string) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldEmployeeNo, v))
}

// EmployeeNoLT applies the LT predicate on the "employee_no" field.
func EmployeeNoLT(v string) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldEmployeeNo, v))
}

// EmployeeNoLTE applies the LTE predicate on the "employee_no" field.
func EmployeeNoLTE(v string) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldEmployeeNo, v))
}

// EmployeeNoContains applies the Contains predicate on the "employee_no" field.
func EmployeeNoContains(v string) predicate.Order {
	return predicate.Order(sql.FieldContains(FieldEmployeeNo, v))
}

// EmployeeNoHasPrefix applies the HasPrefix predicate on the "employee_no" field.
func EmployeeNoHasPrefix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasPrefix(FieldEmployeeNo, v))
}

// EmployeeNoHasSuffix applies the HasSuffix predicate on the "employee_no" field.
func EmployeeNoHasSuffix(v string) predicate.Order {
	return predicate.Order(sql.FieldHasSuffix(FieldEmployeeNo, v))
}

// EmployeeNoIsNil applies the IsNil predicate on the "employee_no" field.
func EmployeeNoIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldEmployeeNo))
}

// EmployeeNoNotNil applies the NotNil predicate on the "employee_no" field.
func EmployeeNoNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldEmployeeNo))
}

// EmployeeNoEqualFold applies the EqualFold predicate on the "employee_no" field.
func EmployeeNoEqualFold(v string) predicate.Order {
	return predicate.Order(sql.FieldEqualFold(FieldEmployeeNo, v))
}

// EmployeeNoContainsFold applies the ContainsFold predicate on the "employee_no" field.
func EmployeeNoContainsFold(v string) predicate.Order {
	return predicate.Order(sql.FieldContainsFold(FieldEmployeeNo, v))
}

// StaffCardIDEQ applies the EQ predicate on the "staff_card_id" field.
func StaffCardIDEQ(v int64) predicate.Order {
	return predicate.Order(sql.FieldEQ(FieldStaffCardID, v))
}

// StaffCardIDNEQ applies the NEQ predicate on the "staff_card_id" field.
func StaffCardIDNEQ(v int64) predicate.Order {
	return predicate.Order(sql.FieldNEQ(FieldStaffCardID, v))
}

// StaffCardIDIn applies the In predicate on the "staff_card_id" field.
func StaffCardIDIn(vs ...int64) predicate.Order {
	return predicate.Order(sql.FieldIn(FieldStaffCardID, vs...))
}

// StaffCardIDNotIn applies the NotIn predicate on the "staff_card_id" field.
func StaffCardIDNotIn(vs ...int64) predicate.Order {
	return predicate.Order(sql.FieldNotIn(FieldStaffCardID, vs...))
}

// StaffCardIDGT applies the GT predicate on the "staff_card_id" field.
func StaffCardIDGT(v int64) predicate.Order {
	return predicate.Order(sql.FieldGT(FieldStaffCardID, v))
}

// StaffCardIDGTE applies the GTE predicate on the "staff_card_id" field.
func StaffCardIDGTE(v int64) predicate.Order {
	return predicate.Order(sql.FieldGTE(FieldStaffCardID, v))
}

// StaffCardIDLT applies the LT predicate on the "staff_card_id" field.
func StaffCardIDLT(v int64) predicate.Order {
	return predicate.Order(sql.FieldLT(FieldStaffCardID, v))
}

// StaffCardIDLTE applies the LTE predicate on the "staff_card_id" field.
func StaffCardIDLTE(v int64) predicate.Order {
	return predicate.Order(sql.FieldLTE(FieldStaffCardID, v))
}

// StaffCardIDIsNil applies the IsNil predicate on the "staff_card_id" field.
func StaffCardIDIsNil() predicate.Order {
	return predicate.Order(sql.FieldIsNull(FieldStaffCardID))
}

// StaffCardIDNotNil applies the NotNil predicate on the "staff_card_id" field.
func StaffCardIDNotNil() predicate.Order {
	return predicate.Order(sql.FieldNotNull(FieldStaffCardID))
}

// HasFuelTransactionLink applies the HasEdge predicate on the "fuel_transaction_link" edge.
func HasFuelTransactionLink() predicate.Order {
	return predicate.Order(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, FuelTransactionLinkTable, FuelTransactionLinkColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasFuelTransactionLinkWith applies the HasEdge predicate on the "fuel_transaction_link" edge with a given conditions (other predicates).
func HasFuelTransactionLinkWith(preds ...predicate.FuelTransactionOrderLink) predicate.Order {
	return predicate.Order(func(s *sql.Selector) {
		step := newFuelTransactionLinkStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Order) predicate.Order {
	return predicate.Order(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Order) predicate.Order {
	return predicate.Order(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Order) predicate.Order {
	return predicate.Order(sql.NotPredicates(p))
}
