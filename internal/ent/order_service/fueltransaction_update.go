// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

// FuelTransactionUpdate is the builder for updating FuelTransaction entities.
type FuelTransactionUpdate struct {
	config
	hooks    []Hook
	mutation *FuelTransactionMutation
}

// Where appends a list predicates to the FuelTransactionUpdate builder.
func (ftu *FuelTransactionUpdate) Where(ps ...predicate.FuelTransaction) *FuelTransactionUpdate {
	ftu.mutation.Where(ps...)
	return ftu
}

// SetTransactionNumber sets the "transaction_number" field.
func (ftu *FuelTransactionUpdate) SetTransactionNumber(s string) *FuelTransactionUpdate {
	ftu.mutation.SetTransactionNumber(s)
	return ftu
}

// SetNillableTransactionNumber sets the "transaction_number" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableTransactionNumber(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetTransactionNumber(*s)
	}
	return ftu
}

// SetStationID sets the "station_id" field.
func (ftu *FuelTransactionUpdate) SetStationID(i int64) *FuelTransactionUpdate {
	ftu.mutation.ResetStationID()
	ftu.mutation.SetStationID(i)
	return ftu
}

// SetNillableStationID sets the "station_id" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableStationID(i *int64) *FuelTransactionUpdate {
	if i != nil {
		ftu.SetStationID(*i)
	}
	return ftu
}

// AddStationID adds i to the "station_id" field.
func (ftu *FuelTransactionUpdate) AddStationID(i int64) *FuelTransactionUpdate {
	ftu.mutation.AddStationID(i)
	return ftu
}

// SetPumpID sets the "pump_id" field.
func (ftu *FuelTransactionUpdate) SetPumpID(s string) *FuelTransactionUpdate {
	ftu.mutation.SetPumpID(s)
	return ftu
}

// SetNillablePumpID sets the "pump_id" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillablePumpID(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetPumpID(*s)
	}
	return ftu
}

// SetNozzleID sets the "nozzle_id" field.
func (ftu *FuelTransactionUpdate) SetNozzleID(s string) *FuelTransactionUpdate {
	ftu.mutation.SetNozzleID(s)
	return ftu
}

// SetNillableNozzleID sets the "nozzle_id" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableNozzleID(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetNozzleID(*s)
	}
	return ftu
}

// SetFuelType sets the "fuel_type" field.
func (ftu *FuelTransactionUpdate) SetFuelType(s string) *FuelTransactionUpdate {
	ftu.mutation.SetFuelType(s)
	return ftu
}

// SetNillableFuelType sets the "fuel_type" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableFuelType(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetFuelType(*s)
	}
	return ftu
}

// SetFuelGrade sets the "fuel_grade" field.
func (ftu *FuelTransactionUpdate) SetFuelGrade(s string) *FuelTransactionUpdate {
	ftu.mutation.SetFuelGrade(s)
	return ftu
}

// SetNillableFuelGrade sets the "fuel_grade" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableFuelGrade(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetFuelGrade(*s)
	}
	return ftu
}

// SetTank sets the "tank" field.
func (ftu *FuelTransactionUpdate) SetTank(i int) *FuelTransactionUpdate {
	ftu.mutation.ResetTank()
	ftu.mutation.SetTank(i)
	return ftu
}

// SetNillableTank sets the "tank" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableTank(i *int) *FuelTransactionUpdate {
	if i != nil {
		ftu.SetTank(*i)
	}
	return ftu
}

// AddTank adds i to the "tank" field.
func (ftu *FuelTransactionUpdate) AddTank(i int) *FuelTransactionUpdate {
	ftu.mutation.AddTank(i)
	return ftu
}

// SetUnitPrice sets the "unit_price" field.
func (ftu *FuelTransactionUpdate) SetUnitPrice(f float64) *FuelTransactionUpdate {
	ftu.mutation.ResetUnitPrice()
	ftu.mutation.SetUnitPrice(f)
	return ftu
}

// SetNillableUnitPrice sets the "unit_price" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableUnitPrice(f *float64) *FuelTransactionUpdate {
	if f != nil {
		ftu.SetUnitPrice(*f)
	}
	return ftu
}

// AddUnitPrice adds f to the "unit_price" field.
func (ftu *FuelTransactionUpdate) AddUnitPrice(f float64) *FuelTransactionUpdate {
	ftu.mutation.AddUnitPrice(f)
	return ftu
}

// SetVolume sets the "volume" field.
func (ftu *FuelTransactionUpdate) SetVolume(f float64) *FuelTransactionUpdate {
	ftu.mutation.ResetVolume()
	ftu.mutation.SetVolume(f)
	return ftu
}

// SetNillableVolume sets the "volume" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableVolume(f *float64) *FuelTransactionUpdate {
	if f != nil {
		ftu.SetVolume(*f)
	}
	return ftu
}

// AddVolume adds f to the "volume" field.
func (ftu *FuelTransactionUpdate) AddVolume(f float64) *FuelTransactionUpdate {
	ftu.mutation.AddVolume(f)
	return ftu
}

// SetAmount sets the "amount" field.
func (ftu *FuelTransactionUpdate) SetAmount(f float64) *FuelTransactionUpdate {
	ftu.mutation.ResetAmount()
	ftu.mutation.SetAmount(f)
	return ftu
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableAmount(f *float64) *FuelTransactionUpdate {
	if f != nil {
		ftu.SetAmount(*f)
	}
	return ftu
}

// AddAmount adds f to the "amount" field.
func (ftu *FuelTransactionUpdate) AddAmount(f float64) *FuelTransactionUpdate {
	ftu.mutation.AddAmount(f)
	return ftu
}

// SetTotalVolume sets the "total_volume" field.
func (ftu *FuelTransactionUpdate) SetTotalVolume(f float64) *FuelTransactionUpdate {
	ftu.mutation.ResetTotalVolume()
	ftu.mutation.SetTotalVolume(f)
	return ftu
}

// SetNillableTotalVolume sets the "total_volume" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableTotalVolume(f *float64) *FuelTransactionUpdate {
	if f != nil {
		ftu.SetTotalVolume(*f)
	}
	return ftu
}

// AddTotalVolume adds f to the "total_volume" field.
func (ftu *FuelTransactionUpdate) AddTotalVolume(f float64) *FuelTransactionUpdate {
	ftu.mutation.AddTotalVolume(f)
	return ftu
}

// SetTotalAmount sets the "total_amount" field.
func (ftu *FuelTransactionUpdate) SetTotalAmount(f float64) *FuelTransactionUpdate {
	ftu.mutation.ResetTotalAmount()
	ftu.mutation.SetTotalAmount(f)
	return ftu
}

// SetNillableTotalAmount sets the "total_amount" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableTotalAmount(f *float64) *FuelTransactionUpdate {
	if f != nil {
		ftu.SetTotalAmount(*f)
	}
	return ftu
}

// AddTotalAmount adds f to the "total_amount" field.
func (ftu *FuelTransactionUpdate) AddTotalAmount(f float64) *FuelTransactionUpdate {
	ftu.mutation.AddTotalAmount(f)
	return ftu
}

// SetStatus sets the "status" field.
func (ftu *FuelTransactionUpdate) SetStatus(s string) *FuelTransactionUpdate {
	ftu.mutation.SetStatus(s)
	return ftu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableStatus(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetStatus(*s)
	}
	return ftu
}

// SetMemberCardID sets the "member_card_id" field.
func (ftu *FuelTransactionUpdate) SetMemberCardID(s string) *FuelTransactionUpdate {
	ftu.mutation.SetMemberCardID(s)
	return ftu
}

// SetNillableMemberCardID sets the "member_card_id" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableMemberCardID(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetMemberCardID(*s)
	}
	return ftu
}

// ClearMemberCardID clears the value of the "member_card_id" field.
func (ftu *FuelTransactionUpdate) ClearMemberCardID() *FuelTransactionUpdate {
	ftu.mutation.ClearMemberCardID()
	return ftu
}

// SetMemberID sets the "member_id" field.
func (ftu *FuelTransactionUpdate) SetMemberID(s string) *FuelTransactionUpdate {
	ftu.mutation.SetMemberID(s)
	return ftu
}

// SetNillableMemberID sets the "member_id" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableMemberID(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetMemberID(*s)
	}
	return ftu
}

// ClearMemberID clears the value of the "member_id" field.
func (ftu *FuelTransactionUpdate) ClearMemberID() *FuelTransactionUpdate {
	ftu.mutation.ClearMemberID()
	return ftu
}

// SetEmployeeID sets the "employee_id" field.
func (ftu *FuelTransactionUpdate) SetEmployeeID(s string) *FuelTransactionUpdate {
	ftu.mutation.SetEmployeeID(s)
	return ftu
}

// SetNillableEmployeeID sets the "employee_id" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableEmployeeID(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetEmployeeID(*s)
	}
	return ftu
}

// ClearEmployeeID clears the value of the "employee_id" field.
func (ftu *FuelTransactionUpdate) ClearEmployeeID() *FuelTransactionUpdate {
	ftu.mutation.ClearEmployeeID()
	return ftu
}

// SetFccTransactionID sets the "fcc_transaction_id" field.
func (ftu *FuelTransactionUpdate) SetFccTransactionID(s string) *FuelTransactionUpdate {
	ftu.mutation.SetFccTransactionID(s)
	return ftu
}

// SetNillableFccTransactionID sets the "fcc_transaction_id" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableFccTransactionID(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetFccTransactionID(*s)
	}
	return ftu
}

// ClearFccTransactionID clears the value of the "fcc_transaction_id" field.
func (ftu *FuelTransactionUpdate) ClearFccTransactionID() *FuelTransactionUpdate {
	ftu.mutation.ClearFccTransactionID()
	return ftu
}

// SetPosTerminalID sets the "pos_terminal_id" field.
func (ftu *FuelTransactionUpdate) SetPosTerminalID(s string) *FuelTransactionUpdate {
	ftu.mutation.SetPosTerminalID(s)
	return ftu
}

// SetNillablePosTerminalID sets the "pos_terminal_id" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillablePosTerminalID(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetPosTerminalID(*s)
	}
	return ftu
}

// ClearPosTerminalID clears the value of the "pos_terminal_id" field.
func (ftu *FuelTransactionUpdate) ClearPosTerminalID() *FuelTransactionUpdate {
	ftu.mutation.ClearPosTerminalID()
	return ftu
}

// SetMetadata sets the "metadata" field.
func (ftu *FuelTransactionUpdate) SetMetadata(m map[string]interface{}) *FuelTransactionUpdate {
	ftu.mutation.SetMetadata(m)
	return ftu
}

// ClearMetadata clears the value of the "metadata" field.
func (ftu *FuelTransactionUpdate) ClearMetadata() *FuelTransactionUpdate {
	ftu.mutation.ClearMetadata()
	return ftu
}

// SetUpdatedAt sets the "updated_at" field.
func (ftu *FuelTransactionUpdate) SetUpdatedAt(t time.Time) *FuelTransactionUpdate {
	ftu.mutation.SetUpdatedAt(t)
	return ftu
}

// SetProcessedAt sets the "processed_at" field.
func (ftu *FuelTransactionUpdate) SetProcessedAt(t time.Time) *FuelTransactionUpdate {
	ftu.mutation.SetProcessedAt(t)
	return ftu
}

// SetNillableProcessedAt sets the "processed_at" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableProcessedAt(t *time.Time) *FuelTransactionUpdate {
	if t != nil {
		ftu.SetProcessedAt(*t)
	}
	return ftu
}

// ClearProcessedAt clears the value of the "processed_at" field.
func (ftu *FuelTransactionUpdate) ClearProcessedAt() *FuelTransactionUpdate {
	ftu.mutation.ClearProcessedAt()
	return ftu
}

// SetCancelledAt sets the "cancelled_at" field.
func (ftu *FuelTransactionUpdate) SetCancelledAt(t time.Time) *FuelTransactionUpdate {
	ftu.mutation.SetCancelledAt(t)
	return ftu
}

// SetNillableCancelledAt sets the "cancelled_at" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableCancelledAt(t *time.Time) *FuelTransactionUpdate {
	if t != nil {
		ftu.SetCancelledAt(*t)
	}
	return ftu
}

// ClearCancelledAt clears the value of the "cancelled_at" field.
func (ftu *FuelTransactionUpdate) ClearCancelledAt() *FuelTransactionUpdate {
	ftu.mutation.ClearCancelledAt()
	return ftu
}

// SetStartTotalizer sets the "start_totalizer" field.
func (ftu *FuelTransactionUpdate) SetStartTotalizer(f float64) *FuelTransactionUpdate {
	ftu.mutation.ResetStartTotalizer()
	ftu.mutation.SetStartTotalizer(f)
	return ftu
}

// SetNillableStartTotalizer sets the "start_totalizer" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableStartTotalizer(f *float64) *FuelTransactionUpdate {
	if f != nil {
		ftu.SetStartTotalizer(*f)
	}
	return ftu
}

// AddStartTotalizer adds f to the "start_totalizer" field.
func (ftu *FuelTransactionUpdate) AddStartTotalizer(f float64) *FuelTransactionUpdate {
	ftu.mutation.AddStartTotalizer(f)
	return ftu
}

// ClearStartTotalizer clears the value of the "start_totalizer" field.
func (ftu *FuelTransactionUpdate) ClearStartTotalizer() *FuelTransactionUpdate {
	ftu.mutation.ClearStartTotalizer()
	return ftu
}

// SetEndTotalizer sets the "end_totalizer" field.
func (ftu *FuelTransactionUpdate) SetEndTotalizer(f float64) *FuelTransactionUpdate {
	ftu.mutation.ResetEndTotalizer()
	ftu.mutation.SetEndTotalizer(f)
	return ftu
}

// SetNillableEndTotalizer sets the "end_totalizer" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableEndTotalizer(f *float64) *FuelTransactionUpdate {
	if f != nil {
		ftu.SetEndTotalizer(*f)
	}
	return ftu
}

// AddEndTotalizer adds f to the "end_totalizer" field.
func (ftu *FuelTransactionUpdate) AddEndTotalizer(f float64) *FuelTransactionUpdate {
	ftu.mutation.AddEndTotalizer(f)
	return ftu
}

// ClearEndTotalizer clears the value of the "end_totalizer" field.
func (ftu *FuelTransactionUpdate) ClearEndTotalizer() *FuelTransactionUpdate {
	ftu.mutation.ClearEndTotalizer()
	return ftu
}

// SetNozzleStartTime sets the "nozzle_start_time" field.
func (ftu *FuelTransactionUpdate) SetNozzleStartTime(t time.Time) *FuelTransactionUpdate {
	ftu.mutation.SetNozzleStartTime(t)
	return ftu
}

// SetNillableNozzleStartTime sets the "nozzle_start_time" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableNozzleStartTime(t *time.Time) *FuelTransactionUpdate {
	if t != nil {
		ftu.SetNozzleStartTime(*t)
	}
	return ftu
}

// ClearNozzleStartTime clears the value of the "nozzle_start_time" field.
func (ftu *FuelTransactionUpdate) ClearNozzleStartTime() *FuelTransactionUpdate {
	ftu.mutation.ClearNozzleStartTime()
	return ftu
}

// SetNozzleEndTime sets the "nozzle_end_time" field.
func (ftu *FuelTransactionUpdate) SetNozzleEndTime(t time.Time) *FuelTransactionUpdate {
	ftu.mutation.SetNozzleEndTime(t)
	return ftu
}

// SetNillableNozzleEndTime sets the "nozzle_end_time" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableNozzleEndTime(t *time.Time) *FuelTransactionUpdate {
	if t != nil {
		ftu.SetNozzleEndTime(*t)
	}
	return ftu
}

// ClearNozzleEndTime clears the value of the "nozzle_end_time" field.
func (ftu *FuelTransactionUpdate) ClearNozzleEndTime() *FuelTransactionUpdate {
	ftu.mutation.ClearNozzleEndTime()
	return ftu
}

// SetStaffCardID sets the "staff_card_id" field.
func (ftu *FuelTransactionUpdate) SetStaffCardID(s string) *FuelTransactionUpdate {
	ftu.mutation.SetStaffCardID(s)
	return ftu
}

// SetNillableStaffCardID sets the "staff_card_id" field if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableStaffCardID(s *string) *FuelTransactionUpdate {
	if s != nil {
		ftu.SetStaffCardID(*s)
	}
	return ftu
}

// ClearStaffCardID clears the value of the "staff_card_id" field.
func (ftu *FuelTransactionUpdate) ClearStaffCardID() *FuelTransactionUpdate {
	ftu.mutation.ClearStaffCardID()
	return ftu
}

// SetOrderLinkID sets the "order_link" edge to the FuelTransactionOrderLink entity by ID.
func (ftu *FuelTransactionUpdate) SetOrderLinkID(id string) *FuelTransactionUpdate {
	ftu.mutation.SetOrderLinkID(id)
	return ftu
}

// SetNillableOrderLinkID sets the "order_link" edge to the FuelTransactionOrderLink entity by ID if the given value is not nil.
func (ftu *FuelTransactionUpdate) SetNillableOrderLinkID(id *string) *FuelTransactionUpdate {
	if id != nil {
		ftu = ftu.SetOrderLinkID(*id)
	}
	return ftu
}

// SetOrderLink sets the "order_link" edge to the FuelTransactionOrderLink entity.
func (ftu *FuelTransactionUpdate) SetOrderLink(f *FuelTransactionOrderLink) *FuelTransactionUpdate {
	return ftu.SetOrderLinkID(f.ID)
}

// Mutation returns the FuelTransactionMutation object of the builder.
func (ftu *FuelTransactionUpdate) Mutation() *FuelTransactionMutation {
	return ftu.mutation
}

// ClearOrderLink clears the "order_link" edge to the FuelTransactionOrderLink entity.
func (ftu *FuelTransactionUpdate) ClearOrderLink() *FuelTransactionUpdate {
	ftu.mutation.ClearOrderLink()
	return ftu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ftu *FuelTransactionUpdate) Save(ctx context.Context) (int, error) {
	ftu.defaults()
	return withHooks(ctx, ftu.sqlSave, ftu.mutation, ftu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ftu *FuelTransactionUpdate) SaveX(ctx context.Context) int {
	affected, err := ftu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ftu *FuelTransactionUpdate) Exec(ctx context.Context) error {
	_, err := ftu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ftu *FuelTransactionUpdate) ExecX(ctx context.Context) {
	if err := ftu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ftu *FuelTransactionUpdate) defaults() {
	if _, ok := ftu.mutation.UpdatedAt(); !ok {
		v := fueltransaction.UpdateDefaultUpdatedAt()
		ftu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ftu *FuelTransactionUpdate) check() error {
	if v, ok := ftu.mutation.TransactionNumber(); ok {
		if err := fueltransaction.TransactionNumberValidator(v); err != nil {
			return &ValidationError{Name: "transaction_number", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.transaction_number": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.PumpID(); ok {
		if err := fueltransaction.PumpIDValidator(v); err != nil {
			return &ValidationError{Name: "pump_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.pump_id": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.NozzleID(); ok {
		if err := fueltransaction.NozzleIDValidator(v); err != nil {
			return &ValidationError{Name: "nozzle_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.nozzle_id": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.FuelType(); ok {
		if err := fueltransaction.FuelTypeValidator(v); err != nil {
			return &ValidationError{Name: "fuel_type", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.fuel_type": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.FuelGrade(); ok {
		if err := fueltransaction.FuelGradeValidator(v); err != nil {
			return &ValidationError{Name: "fuel_grade", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.fuel_grade": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.Status(); ok {
		if err := fueltransaction.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.status": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.MemberCardID(); ok {
		if err := fueltransaction.MemberCardIDValidator(v); err != nil {
			return &ValidationError{Name: "member_card_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.member_card_id": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.MemberID(); ok {
		if err := fueltransaction.MemberIDValidator(v); err != nil {
			return &ValidationError{Name: "member_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.member_id": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.EmployeeID(); ok {
		if err := fueltransaction.EmployeeIDValidator(v); err != nil {
			return &ValidationError{Name: "employee_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.employee_id": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.FccTransactionID(); ok {
		if err := fueltransaction.FccTransactionIDValidator(v); err != nil {
			return &ValidationError{Name: "fcc_transaction_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.fcc_transaction_id": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.PosTerminalID(); ok {
		if err := fueltransaction.PosTerminalIDValidator(v); err != nil {
			return &ValidationError{Name: "pos_terminal_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.pos_terminal_id": %w`, err)}
		}
	}
	if v, ok := ftu.mutation.StaffCardID(); ok {
		if err := fueltransaction.StaffCardIDValidator(v); err != nil {
			return &ValidationError{Name: "staff_card_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.staff_card_id": %w`, err)}
		}
	}
	return nil
}

func (ftu *FuelTransactionUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ftu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(fueltransaction.Table, fueltransaction.Columns, sqlgraph.NewFieldSpec(fueltransaction.FieldID, field.TypeString))
	if ps := ftu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ftu.mutation.TransactionNumber(); ok {
		_spec.SetField(fueltransaction.FieldTransactionNumber, field.TypeString, value)
	}
	if value, ok := ftu.mutation.StationID(); ok {
		_spec.SetField(fueltransaction.FieldStationID, field.TypeInt64, value)
	}
	if value, ok := ftu.mutation.AddedStationID(); ok {
		_spec.AddField(fueltransaction.FieldStationID, field.TypeInt64, value)
	}
	if value, ok := ftu.mutation.PumpID(); ok {
		_spec.SetField(fueltransaction.FieldPumpID, field.TypeString, value)
	}
	if value, ok := ftu.mutation.NozzleID(); ok {
		_spec.SetField(fueltransaction.FieldNozzleID, field.TypeString, value)
	}
	if value, ok := ftu.mutation.FuelType(); ok {
		_spec.SetField(fueltransaction.FieldFuelType, field.TypeString, value)
	}
	if value, ok := ftu.mutation.FuelGrade(); ok {
		_spec.SetField(fueltransaction.FieldFuelGrade, field.TypeString, value)
	}
	if value, ok := ftu.mutation.Tank(); ok {
		_spec.SetField(fueltransaction.FieldTank, field.TypeInt, value)
	}
	if value, ok := ftu.mutation.AddedTank(); ok {
		_spec.AddField(fueltransaction.FieldTank, field.TypeInt, value)
	}
	if value, ok := ftu.mutation.UnitPrice(); ok {
		_spec.SetField(fueltransaction.FieldUnitPrice, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.AddedUnitPrice(); ok {
		_spec.AddField(fueltransaction.FieldUnitPrice, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.Volume(); ok {
		_spec.SetField(fueltransaction.FieldVolume, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.AddedVolume(); ok {
		_spec.AddField(fueltransaction.FieldVolume, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.Amount(); ok {
		_spec.SetField(fueltransaction.FieldAmount, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.AddedAmount(); ok {
		_spec.AddField(fueltransaction.FieldAmount, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.TotalVolume(); ok {
		_spec.SetField(fueltransaction.FieldTotalVolume, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.AddedTotalVolume(); ok {
		_spec.AddField(fueltransaction.FieldTotalVolume, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.TotalAmount(); ok {
		_spec.SetField(fueltransaction.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.AddedTotalAmount(); ok {
		_spec.AddField(fueltransaction.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.Status(); ok {
		_spec.SetField(fueltransaction.FieldStatus, field.TypeString, value)
	}
	if value, ok := ftu.mutation.MemberCardID(); ok {
		_spec.SetField(fueltransaction.FieldMemberCardID, field.TypeString, value)
	}
	if ftu.mutation.MemberCardIDCleared() {
		_spec.ClearField(fueltransaction.FieldMemberCardID, field.TypeString)
	}
	if value, ok := ftu.mutation.MemberID(); ok {
		_spec.SetField(fueltransaction.FieldMemberID, field.TypeString, value)
	}
	if ftu.mutation.MemberIDCleared() {
		_spec.ClearField(fueltransaction.FieldMemberID, field.TypeString)
	}
	if value, ok := ftu.mutation.EmployeeID(); ok {
		_spec.SetField(fueltransaction.FieldEmployeeID, field.TypeString, value)
	}
	if ftu.mutation.EmployeeIDCleared() {
		_spec.ClearField(fueltransaction.FieldEmployeeID, field.TypeString)
	}
	if value, ok := ftu.mutation.FccTransactionID(); ok {
		_spec.SetField(fueltransaction.FieldFccTransactionID, field.TypeString, value)
	}
	if ftu.mutation.FccTransactionIDCleared() {
		_spec.ClearField(fueltransaction.FieldFccTransactionID, field.TypeString)
	}
	if value, ok := ftu.mutation.PosTerminalID(); ok {
		_spec.SetField(fueltransaction.FieldPosTerminalID, field.TypeString, value)
	}
	if ftu.mutation.PosTerminalIDCleared() {
		_spec.ClearField(fueltransaction.FieldPosTerminalID, field.TypeString)
	}
	if value, ok := ftu.mutation.Metadata(); ok {
		_spec.SetField(fueltransaction.FieldMetadata, field.TypeJSON, value)
	}
	if ftu.mutation.MetadataCleared() {
		_spec.ClearField(fueltransaction.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ftu.mutation.UpdatedAt(); ok {
		_spec.SetField(fueltransaction.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ftu.mutation.ProcessedAt(); ok {
		_spec.SetField(fueltransaction.FieldProcessedAt, field.TypeTime, value)
	}
	if ftu.mutation.ProcessedAtCleared() {
		_spec.ClearField(fueltransaction.FieldProcessedAt, field.TypeTime)
	}
	if value, ok := ftu.mutation.CancelledAt(); ok {
		_spec.SetField(fueltransaction.FieldCancelledAt, field.TypeTime, value)
	}
	if ftu.mutation.CancelledAtCleared() {
		_spec.ClearField(fueltransaction.FieldCancelledAt, field.TypeTime)
	}
	if value, ok := ftu.mutation.StartTotalizer(); ok {
		_spec.SetField(fueltransaction.FieldStartTotalizer, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.AddedStartTotalizer(); ok {
		_spec.AddField(fueltransaction.FieldStartTotalizer, field.TypeFloat64, value)
	}
	if ftu.mutation.StartTotalizerCleared() {
		_spec.ClearField(fueltransaction.FieldStartTotalizer, field.TypeFloat64)
	}
	if value, ok := ftu.mutation.EndTotalizer(); ok {
		_spec.SetField(fueltransaction.FieldEndTotalizer, field.TypeFloat64, value)
	}
	if value, ok := ftu.mutation.AddedEndTotalizer(); ok {
		_spec.AddField(fueltransaction.FieldEndTotalizer, field.TypeFloat64, value)
	}
	if ftu.mutation.EndTotalizerCleared() {
		_spec.ClearField(fueltransaction.FieldEndTotalizer, field.TypeFloat64)
	}
	if value, ok := ftu.mutation.NozzleStartTime(); ok {
		_spec.SetField(fueltransaction.FieldNozzleStartTime, field.TypeTime, value)
	}
	if ftu.mutation.NozzleStartTimeCleared() {
		_spec.ClearField(fueltransaction.FieldNozzleStartTime, field.TypeTime)
	}
	if value, ok := ftu.mutation.NozzleEndTime(); ok {
		_spec.SetField(fueltransaction.FieldNozzleEndTime, field.TypeTime, value)
	}
	if ftu.mutation.NozzleEndTimeCleared() {
		_spec.ClearField(fueltransaction.FieldNozzleEndTime, field.TypeTime)
	}
	if value, ok := ftu.mutation.StaffCardID(); ok {
		_spec.SetField(fueltransaction.FieldStaffCardID, field.TypeString, value)
	}
	if ftu.mutation.StaffCardIDCleared() {
		_spec.ClearField(fueltransaction.FieldStaffCardID, field.TypeString)
	}
	if ftu.mutation.OrderLinkCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   fueltransaction.OrderLinkTable,
			Columns: []string{fueltransaction.OrderLinkColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ftu.mutation.OrderLinkIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   fueltransaction.OrderLinkTable,
			Columns: []string{fueltransaction.OrderLinkColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ftu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{fueltransaction.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ftu.mutation.done = true
	return n, nil
}

// FuelTransactionUpdateOne is the builder for updating a single FuelTransaction entity.
type FuelTransactionUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *FuelTransactionMutation
}

// SetTransactionNumber sets the "transaction_number" field.
func (ftuo *FuelTransactionUpdateOne) SetTransactionNumber(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetTransactionNumber(s)
	return ftuo
}

// SetNillableTransactionNumber sets the "transaction_number" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableTransactionNumber(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetTransactionNumber(*s)
	}
	return ftuo
}

// SetStationID sets the "station_id" field.
func (ftuo *FuelTransactionUpdateOne) SetStationID(i int64) *FuelTransactionUpdateOne {
	ftuo.mutation.ResetStationID()
	ftuo.mutation.SetStationID(i)
	return ftuo
}

// SetNillableStationID sets the "station_id" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableStationID(i *int64) *FuelTransactionUpdateOne {
	if i != nil {
		ftuo.SetStationID(*i)
	}
	return ftuo
}

// AddStationID adds i to the "station_id" field.
func (ftuo *FuelTransactionUpdateOne) AddStationID(i int64) *FuelTransactionUpdateOne {
	ftuo.mutation.AddStationID(i)
	return ftuo
}

// SetPumpID sets the "pump_id" field.
func (ftuo *FuelTransactionUpdateOne) SetPumpID(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetPumpID(s)
	return ftuo
}

// SetNillablePumpID sets the "pump_id" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillablePumpID(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetPumpID(*s)
	}
	return ftuo
}

// SetNozzleID sets the "nozzle_id" field.
func (ftuo *FuelTransactionUpdateOne) SetNozzleID(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetNozzleID(s)
	return ftuo
}

// SetNillableNozzleID sets the "nozzle_id" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableNozzleID(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetNozzleID(*s)
	}
	return ftuo
}

// SetFuelType sets the "fuel_type" field.
func (ftuo *FuelTransactionUpdateOne) SetFuelType(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetFuelType(s)
	return ftuo
}

// SetNillableFuelType sets the "fuel_type" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableFuelType(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetFuelType(*s)
	}
	return ftuo
}

// SetFuelGrade sets the "fuel_grade" field.
func (ftuo *FuelTransactionUpdateOne) SetFuelGrade(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetFuelGrade(s)
	return ftuo
}

// SetNillableFuelGrade sets the "fuel_grade" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableFuelGrade(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetFuelGrade(*s)
	}
	return ftuo
}

// SetTank sets the "tank" field.
func (ftuo *FuelTransactionUpdateOne) SetTank(i int) *FuelTransactionUpdateOne {
	ftuo.mutation.ResetTank()
	ftuo.mutation.SetTank(i)
	return ftuo
}

// SetNillableTank sets the "tank" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableTank(i *int) *FuelTransactionUpdateOne {
	if i != nil {
		ftuo.SetTank(*i)
	}
	return ftuo
}

// AddTank adds i to the "tank" field.
func (ftuo *FuelTransactionUpdateOne) AddTank(i int) *FuelTransactionUpdateOne {
	ftuo.mutation.AddTank(i)
	return ftuo
}

// SetUnitPrice sets the "unit_price" field.
func (ftuo *FuelTransactionUpdateOne) SetUnitPrice(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.ResetUnitPrice()
	ftuo.mutation.SetUnitPrice(f)
	return ftuo
}

// SetNillableUnitPrice sets the "unit_price" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableUnitPrice(f *float64) *FuelTransactionUpdateOne {
	if f != nil {
		ftuo.SetUnitPrice(*f)
	}
	return ftuo
}

// AddUnitPrice adds f to the "unit_price" field.
func (ftuo *FuelTransactionUpdateOne) AddUnitPrice(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.AddUnitPrice(f)
	return ftuo
}

// SetVolume sets the "volume" field.
func (ftuo *FuelTransactionUpdateOne) SetVolume(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.ResetVolume()
	ftuo.mutation.SetVolume(f)
	return ftuo
}

// SetNillableVolume sets the "volume" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableVolume(f *float64) *FuelTransactionUpdateOne {
	if f != nil {
		ftuo.SetVolume(*f)
	}
	return ftuo
}

// AddVolume adds f to the "volume" field.
func (ftuo *FuelTransactionUpdateOne) AddVolume(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.AddVolume(f)
	return ftuo
}

// SetAmount sets the "amount" field.
func (ftuo *FuelTransactionUpdateOne) SetAmount(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.ResetAmount()
	ftuo.mutation.SetAmount(f)
	return ftuo
}

// SetNillableAmount sets the "amount" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableAmount(f *float64) *FuelTransactionUpdateOne {
	if f != nil {
		ftuo.SetAmount(*f)
	}
	return ftuo
}

// AddAmount adds f to the "amount" field.
func (ftuo *FuelTransactionUpdateOne) AddAmount(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.AddAmount(f)
	return ftuo
}

// SetTotalVolume sets the "total_volume" field.
func (ftuo *FuelTransactionUpdateOne) SetTotalVolume(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.ResetTotalVolume()
	ftuo.mutation.SetTotalVolume(f)
	return ftuo
}

// SetNillableTotalVolume sets the "total_volume" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableTotalVolume(f *float64) *FuelTransactionUpdateOne {
	if f != nil {
		ftuo.SetTotalVolume(*f)
	}
	return ftuo
}

// AddTotalVolume adds f to the "total_volume" field.
func (ftuo *FuelTransactionUpdateOne) AddTotalVolume(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.AddTotalVolume(f)
	return ftuo
}

// SetTotalAmount sets the "total_amount" field.
func (ftuo *FuelTransactionUpdateOne) SetTotalAmount(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.ResetTotalAmount()
	ftuo.mutation.SetTotalAmount(f)
	return ftuo
}

// SetNillableTotalAmount sets the "total_amount" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableTotalAmount(f *float64) *FuelTransactionUpdateOne {
	if f != nil {
		ftuo.SetTotalAmount(*f)
	}
	return ftuo
}

// AddTotalAmount adds f to the "total_amount" field.
func (ftuo *FuelTransactionUpdateOne) AddTotalAmount(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.AddTotalAmount(f)
	return ftuo
}

// SetStatus sets the "status" field.
func (ftuo *FuelTransactionUpdateOne) SetStatus(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetStatus(s)
	return ftuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableStatus(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetStatus(*s)
	}
	return ftuo
}

// SetMemberCardID sets the "member_card_id" field.
func (ftuo *FuelTransactionUpdateOne) SetMemberCardID(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetMemberCardID(s)
	return ftuo
}

// SetNillableMemberCardID sets the "member_card_id" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableMemberCardID(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetMemberCardID(*s)
	}
	return ftuo
}

// ClearMemberCardID clears the value of the "member_card_id" field.
func (ftuo *FuelTransactionUpdateOne) ClearMemberCardID() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearMemberCardID()
	return ftuo
}

// SetMemberID sets the "member_id" field.
func (ftuo *FuelTransactionUpdateOne) SetMemberID(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetMemberID(s)
	return ftuo
}

// SetNillableMemberID sets the "member_id" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableMemberID(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetMemberID(*s)
	}
	return ftuo
}

// ClearMemberID clears the value of the "member_id" field.
func (ftuo *FuelTransactionUpdateOne) ClearMemberID() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearMemberID()
	return ftuo
}

// SetEmployeeID sets the "employee_id" field.
func (ftuo *FuelTransactionUpdateOne) SetEmployeeID(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetEmployeeID(s)
	return ftuo
}

// SetNillableEmployeeID sets the "employee_id" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableEmployeeID(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetEmployeeID(*s)
	}
	return ftuo
}

// ClearEmployeeID clears the value of the "employee_id" field.
func (ftuo *FuelTransactionUpdateOne) ClearEmployeeID() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearEmployeeID()
	return ftuo
}

// SetFccTransactionID sets the "fcc_transaction_id" field.
func (ftuo *FuelTransactionUpdateOne) SetFccTransactionID(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetFccTransactionID(s)
	return ftuo
}

// SetNillableFccTransactionID sets the "fcc_transaction_id" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableFccTransactionID(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetFccTransactionID(*s)
	}
	return ftuo
}

// ClearFccTransactionID clears the value of the "fcc_transaction_id" field.
func (ftuo *FuelTransactionUpdateOne) ClearFccTransactionID() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearFccTransactionID()
	return ftuo
}

// SetPosTerminalID sets the "pos_terminal_id" field.
func (ftuo *FuelTransactionUpdateOne) SetPosTerminalID(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetPosTerminalID(s)
	return ftuo
}

// SetNillablePosTerminalID sets the "pos_terminal_id" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillablePosTerminalID(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetPosTerminalID(*s)
	}
	return ftuo
}

// ClearPosTerminalID clears the value of the "pos_terminal_id" field.
func (ftuo *FuelTransactionUpdateOne) ClearPosTerminalID() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearPosTerminalID()
	return ftuo
}

// SetMetadata sets the "metadata" field.
func (ftuo *FuelTransactionUpdateOne) SetMetadata(m map[string]interface{}) *FuelTransactionUpdateOne {
	ftuo.mutation.SetMetadata(m)
	return ftuo
}

// ClearMetadata clears the value of the "metadata" field.
func (ftuo *FuelTransactionUpdateOne) ClearMetadata() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearMetadata()
	return ftuo
}

// SetUpdatedAt sets the "updated_at" field.
func (ftuo *FuelTransactionUpdateOne) SetUpdatedAt(t time.Time) *FuelTransactionUpdateOne {
	ftuo.mutation.SetUpdatedAt(t)
	return ftuo
}

// SetProcessedAt sets the "processed_at" field.
func (ftuo *FuelTransactionUpdateOne) SetProcessedAt(t time.Time) *FuelTransactionUpdateOne {
	ftuo.mutation.SetProcessedAt(t)
	return ftuo
}

// SetNillableProcessedAt sets the "processed_at" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableProcessedAt(t *time.Time) *FuelTransactionUpdateOne {
	if t != nil {
		ftuo.SetProcessedAt(*t)
	}
	return ftuo
}

// ClearProcessedAt clears the value of the "processed_at" field.
func (ftuo *FuelTransactionUpdateOne) ClearProcessedAt() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearProcessedAt()
	return ftuo
}

// SetCancelledAt sets the "cancelled_at" field.
func (ftuo *FuelTransactionUpdateOne) SetCancelledAt(t time.Time) *FuelTransactionUpdateOne {
	ftuo.mutation.SetCancelledAt(t)
	return ftuo
}

// SetNillableCancelledAt sets the "cancelled_at" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableCancelledAt(t *time.Time) *FuelTransactionUpdateOne {
	if t != nil {
		ftuo.SetCancelledAt(*t)
	}
	return ftuo
}

// ClearCancelledAt clears the value of the "cancelled_at" field.
func (ftuo *FuelTransactionUpdateOne) ClearCancelledAt() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearCancelledAt()
	return ftuo
}

// SetStartTotalizer sets the "start_totalizer" field.
func (ftuo *FuelTransactionUpdateOne) SetStartTotalizer(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.ResetStartTotalizer()
	ftuo.mutation.SetStartTotalizer(f)
	return ftuo
}

// SetNillableStartTotalizer sets the "start_totalizer" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableStartTotalizer(f *float64) *FuelTransactionUpdateOne {
	if f != nil {
		ftuo.SetStartTotalizer(*f)
	}
	return ftuo
}

// AddStartTotalizer adds f to the "start_totalizer" field.
func (ftuo *FuelTransactionUpdateOne) AddStartTotalizer(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.AddStartTotalizer(f)
	return ftuo
}

// ClearStartTotalizer clears the value of the "start_totalizer" field.
func (ftuo *FuelTransactionUpdateOne) ClearStartTotalizer() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearStartTotalizer()
	return ftuo
}

// SetEndTotalizer sets the "end_totalizer" field.
func (ftuo *FuelTransactionUpdateOne) SetEndTotalizer(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.ResetEndTotalizer()
	ftuo.mutation.SetEndTotalizer(f)
	return ftuo
}

// SetNillableEndTotalizer sets the "end_totalizer" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableEndTotalizer(f *float64) *FuelTransactionUpdateOne {
	if f != nil {
		ftuo.SetEndTotalizer(*f)
	}
	return ftuo
}

// AddEndTotalizer adds f to the "end_totalizer" field.
func (ftuo *FuelTransactionUpdateOne) AddEndTotalizer(f float64) *FuelTransactionUpdateOne {
	ftuo.mutation.AddEndTotalizer(f)
	return ftuo
}

// ClearEndTotalizer clears the value of the "end_totalizer" field.
func (ftuo *FuelTransactionUpdateOne) ClearEndTotalizer() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearEndTotalizer()
	return ftuo
}

// SetNozzleStartTime sets the "nozzle_start_time" field.
func (ftuo *FuelTransactionUpdateOne) SetNozzleStartTime(t time.Time) *FuelTransactionUpdateOne {
	ftuo.mutation.SetNozzleStartTime(t)
	return ftuo
}

// SetNillableNozzleStartTime sets the "nozzle_start_time" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableNozzleStartTime(t *time.Time) *FuelTransactionUpdateOne {
	if t != nil {
		ftuo.SetNozzleStartTime(*t)
	}
	return ftuo
}

// ClearNozzleStartTime clears the value of the "nozzle_start_time" field.
func (ftuo *FuelTransactionUpdateOne) ClearNozzleStartTime() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearNozzleStartTime()
	return ftuo
}

// SetNozzleEndTime sets the "nozzle_end_time" field.
func (ftuo *FuelTransactionUpdateOne) SetNozzleEndTime(t time.Time) *FuelTransactionUpdateOne {
	ftuo.mutation.SetNozzleEndTime(t)
	return ftuo
}

// SetNillableNozzleEndTime sets the "nozzle_end_time" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableNozzleEndTime(t *time.Time) *FuelTransactionUpdateOne {
	if t != nil {
		ftuo.SetNozzleEndTime(*t)
	}
	return ftuo
}

// ClearNozzleEndTime clears the value of the "nozzle_end_time" field.
func (ftuo *FuelTransactionUpdateOne) ClearNozzleEndTime() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearNozzleEndTime()
	return ftuo
}

// SetStaffCardID sets the "staff_card_id" field.
func (ftuo *FuelTransactionUpdateOne) SetStaffCardID(s string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetStaffCardID(s)
	return ftuo
}

// SetNillableStaffCardID sets the "staff_card_id" field if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableStaffCardID(s *string) *FuelTransactionUpdateOne {
	if s != nil {
		ftuo.SetStaffCardID(*s)
	}
	return ftuo
}

// ClearStaffCardID clears the value of the "staff_card_id" field.
func (ftuo *FuelTransactionUpdateOne) ClearStaffCardID() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearStaffCardID()
	return ftuo
}

// SetOrderLinkID sets the "order_link" edge to the FuelTransactionOrderLink entity by ID.
func (ftuo *FuelTransactionUpdateOne) SetOrderLinkID(id string) *FuelTransactionUpdateOne {
	ftuo.mutation.SetOrderLinkID(id)
	return ftuo
}

// SetNillableOrderLinkID sets the "order_link" edge to the FuelTransactionOrderLink entity by ID if the given value is not nil.
func (ftuo *FuelTransactionUpdateOne) SetNillableOrderLinkID(id *string) *FuelTransactionUpdateOne {
	if id != nil {
		ftuo = ftuo.SetOrderLinkID(*id)
	}
	return ftuo
}

// SetOrderLink sets the "order_link" edge to the FuelTransactionOrderLink entity.
func (ftuo *FuelTransactionUpdateOne) SetOrderLink(f *FuelTransactionOrderLink) *FuelTransactionUpdateOne {
	return ftuo.SetOrderLinkID(f.ID)
}

// Mutation returns the FuelTransactionMutation object of the builder.
func (ftuo *FuelTransactionUpdateOne) Mutation() *FuelTransactionMutation {
	return ftuo.mutation
}

// ClearOrderLink clears the "order_link" edge to the FuelTransactionOrderLink entity.
func (ftuo *FuelTransactionUpdateOne) ClearOrderLink() *FuelTransactionUpdateOne {
	ftuo.mutation.ClearOrderLink()
	return ftuo
}

// Where appends a list predicates to the FuelTransactionUpdate builder.
func (ftuo *FuelTransactionUpdateOne) Where(ps ...predicate.FuelTransaction) *FuelTransactionUpdateOne {
	ftuo.mutation.Where(ps...)
	return ftuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ftuo *FuelTransactionUpdateOne) Select(field string, fields ...string) *FuelTransactionUpdateOne {
	ftuo.fields = append([]string{field}, fields...)
	return ftuo
}

// Save executes the query and returns the updated FuelTransaction entity.
func (ftuo *FuelTransactionUpdateOne) Save(ctx context.Context) (*FuelTransaction, error) {
	ftuo.defaults()
	return withHooks(ctx, ftuo.sqlSave, ftuo.mutation, ftuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ftuo *FuelTransactionUpdateOne) SaveX(ctx context.Context) *FuelTransaction {
	node, err := ftuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ftuo *FuelTransactionUpdateOne) Exec(ctx context.Context) error {
	_, err := ftuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ftuo *FuelTransactionUpdateOne) ExecX(ctx context.Context) {
	if err := ftuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ftuo *FuelTransactionUpdateOne) defaults() {
	if _, ok := ftuo.mutation.UpdatedAt(); !ok {
		v := fueltransaction.UpdateDefaultUpdatedAt()
		ftuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ftuo *FuelTransactionUpdateOne) check() error {
	if v, ok := ftuo.mutation.TransactionNumber(); ok {
		if err := fueltransaction.TransactionNumberValidator(v); err != nil {
			return &ValidationError{Name: "transaction_number", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.transaction_number": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.PumpID(); ok {
		if err := fueltransaction.PumpIDValidator(v); err != nil {
			return &ValidationError{Name: "pump_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.pump_id": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.NozzleID(); ok {
		if err := fueltransaction.NozzleIDValidator(v); err != nil {
			return &ValidationError{Name: "nozzle_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.nozzle_id": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.FuelType(); ok {
		if err := fueltransaction.FuelTypeValidator(v); err != nil {
			return &ValidationError{Name: "fuel_type", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.fuel_type": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.FuelGrade(); ok {
		if err := fueltransaction.FuelGradeValidator(v); err != nil {
			return &ValidationError{Name: "fuel_grade", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.fuel_grade": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.Status(); ok {
		if err := fueltransaction.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.status": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.MemberCardID(); ok {
		if err := fueltransaction.MemberCardIDValidator(v); err != nil {
			return &ValidationError{Name: "member_card_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.member_card_id": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.MemberID(); ok {
		if err := fueltransaction.MemberIDValidator(v); err != nil {
			return &ValidationError{Name: "member_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.member_id": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.EmployeeID(); ok {
		if err := fueltransaction.EmployeeIDValidator(v); err != nil {
			return &ValidationError{Name: "employee_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.employee_id": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.FccTransactionID(); ok {
		if err := fueltransaction.FccTransactionIDValidator(v); err != nil {
			return &ValidationError{Name: "fcc_transaction_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.fcc_transaction_id": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.PosTerminalID(); ok {
		if err := fueltransaction.PosTerminalIDValidator(v); err != nil {
			return &ValidationError{Name: "pos_terminal_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.pos_terminal_id": %w`, err)}
		}
	}
	if v, ok := ftuo.mutation.StaffCardID(); ok {
		if err := fueltransaction.StaffCardIDValidator(v); err != nil {
			return &ValidationError{Name: "staff_card_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.staff_card_id": %w`, err)}
		}
	}
	return nil
}

func (ftuo *FuelTransactionUpdateOne) sqlSave(ctx context.Context) (_node *FuelTransaction, err error) {
	if err := ftuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(fueltransaction.Table, fueltransaction.Columns, sqlgraph.NewFieldSpec(fueltransaction.FieldID, field.TypeString))
	id, ok := ftuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`order_service: missing "FuelTransaction.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ftuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, fueltransaction.FieldID)
		for _, f := range fields {
			if !fueltransaction.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("order_service: invalid field %q for query", f)}
			}
			if f != fueltransaction.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ftuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ftuo.mutation.TransactionNumber(); ok {
		_spec.SetField(fueltransaction.FieldTransactionNumber, field.TypeString, value)
	}
	if value, ok := ftuo.mutation.StationID(); ok {
		_spec.SetField(fueltransaction.FieldStationID, field.TypeInt64, value)
	}
	if value, ok := ftuo.mutation.AddedStationID(); ok {
		_spec.AddField(fueltransaction.FieldStationID, field.TypeInt64, value)
	}
	if value, ok := ftuo.mutation.PumpID(); ok {
		_spec.SetField(fueltransaction.FieldPumpID, field.TypeString, value)
	}
	if value, ok := ftuo.mutation.NozzleID(); ok {
		_spec.SetField(fueltransaction.FieldNozzleID, field.TypeString, value)
	}
	if value, ok := ftuo.mutation.FuelType(); ok {
		_spec.SetField(fueltransaction.FieldFuelType, field.TypeString, value)
	}
	if value, ok := ftuo.mutation.FuelGrade(); ok {
		_spec.SetField(fueltransaction.FieldFuelGrade, field.TypeString, value)
	}
	if value, ok := ftuo.mutation.Tank(); ok {
		_spec.SetField(fueltransaction.FieldTank, field.TypeInt, value)
	}
	if value, ok := ftuo.mutation.AddedTank(); ok {
		_spec.AddField(fueltransaction.FieldTank, field.TypeInt, value)
	}
	if value, ok := ftuo.mutation.UnitPrice(); ok {
		_spec.SetField(fueltransaction.FieldUnitPrice, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.AddedUnitPrice(); ok {
		_spec.AddField(fueltransaction.FieldUnitPrice, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.Volume(); ok {
		_spec.SetField(fueltransaction.FieldVolume, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.AddedVolume(); ok {
		_spec.AddField(fueltransaction.FieldVolume, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.Amount(); ok {
		_spec.SetField(fueltransaction.FieldAmount, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.AddedAmount(); ok {
		_spec.AddField(fueltransaction.FieldAmount, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.TotalVolume(); ok {
		_spec.SetField(fueltransaction.FieldTotalVolume, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.AddedTotalVolume(); ok {
		_spec.AddField(fueltransaction.FieldTotalVolume, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.TotalAmount(); ok {
		_spec.SetField(fueltransaction.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.AddedTotalAmount(); ok {
		_spec.AddField(fueltransaction.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.Status(); ok {
		_spec.SetField(fueltransaction.FieldStatus, field.TypeString, value)
	}
	if value, ok := ftuo.mutation.MemberCardID(); ok {
		_spec.SetField(fueltransaction.FieldMemberCardID, field.TypeString, value)
	}
	if ftuo.mutation.MemberCardIDCleared() {
		_spec.ClearField(fueltransaction.FieldMemberCardID, field.TypeString)
	}
	if value, ok := ftuo.mutation.MemberID(); ok {
		_spec.SetField(fueltransaction.FieldMemberID, field.TypeString, value)
	}
	if ftuo.mutation.MemberIDCleared() {
		_spec.ClearField(fueltransaction.FieldMemberID, field.TypeString)
	}
	if value, ok := ftuo.mutation.EmployeeID(); ok {
		_spec.SetField(fueltransaction.FieldEmployeeID, field.TypeString, value)
	}
	if ftuo.mutation.EmployeeIDCleared() {
		_spec.ClearField(fueltransaction.FieldEmployeeID, field.TypeString)
	}
	if value, ok := ftuo.mutation.FccTransactionID(); ok {
		_spec.SetField(fueltransaction.FieldFccTransactionID, field.TypeString, value)
	}
	if ftuo.mutation.FccTransactionIDCleared() {
		_spec.ClearField(fueltransaction.FieldFccTransactionID, field.TypeString)
	}
	if value, ok := ftuo.mutation.PosTerminalID(); ok {
		_spec.SetField(fueltransaction.FieldPosTerminalID, field.TypeString, value)
	}
	if ftuo.mutation.PosTerminalIDCleared() {
		_spec.ClearField(fueltransaction.FieldPosTerminalID, field.TypeString)
	}
	if value, ok := ftuo.mutation.Metadata(); ok {
		_spec.SetField(fueltransaction.FieldMetadata, field.TypeJSON, value)
	}
	if ftuo.mutation.MetadataCleared() {
		_spec.ClearField(fueltransaction.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ftuo.mutation.UpdatedAt(); ok {
		_spec.SetField(fueltransaction.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ftuo.mutation.ProcessedAt(); ok {
		_spec.SetField(fueltransaction.FieldProcessedAt, field.TypeTime, value)
	}
	if ftuo.mutation.ProcessedAtCleared() {
		_spec.ClearField(fueltransaction.FieldProcessedAt, field.TypeTime)
	}
	if value, ok := ftuo.mutation.CancelledAt(); ok {
		_spec.SetField(fueltransaction.FieldCancelledAt, field.TypeTime, value)
	}
	if ftuo.mutation.CancelledAtCleared() {
		_spec.ClearField(fueltransaction.FieldCancelledAt, field.TypeTime)
	}
	if value, ok := ftuo.mutation.StartTotalizer(); ok {
		_spec.SetField(fueltransaction.FieldStartTotalizer, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.AddedStartTotalizer(); ok {
		_spec.AddField(fueltransaction.FieldStartTotalizer, field.TypeFloat64, value)
	}
	if ftuo.mutation.StartTotalizerCleared() {
		_spec.ClearField(fueltransaction.FieldStartTotalizer, field.TypeFloat64)
	}
	if value, ok := ftuo.mutation.EndTotalizer(); ok {
		_spec.SetField(fueltransaction.FieldEndTotalizer, field.TypeFloat64, value)
	}
	if value, ok := ftuo.mutation.AddedEndTotalizer(); ok {
		_spec.AddField(fueltransaction.FieldEndTotalizer, field.TypeFloat64, value)
	}
	if ftuo.mutation.EndTotalizerCleared() {
		_spec.ClearField(fueltransaction.FieldEndTotalizer, field.TypeFloat64)
	}
	if value, ok := ftuo.mutation.NozzleStartTime(); ok {
		_spec.SetField(fueltransaction.FieldNozzleStartTime, field.TypeTime, value)
	}
	if ftuo.mutation.NozzleStartTimeCleared() {
		_spec.ClearField(fueltransaction.FieldNozzleStartTime, field.TypeTime)
	}
	if value, ok := ftuo.mutation.NozzleEndTime(); ok {
		_spec.SetField(fueltransaction.FieldNozzleEndTime, field.TypeTime, value)
	}
	if ftuo.mutation.NozzleEndTimeCleared() {
		_spec.ClearField(fueltransaction.FieldNozzleEndTime, field.TypeTime)
	}
	if value, ok := ftuo.mutation.StaffCardID(); ok {
		_spec.SetField(fueltransaction.FieldStaffCardID, field.TypeString, value)
	}
	if ftuo.mutation.StaffCardIDCleared() {
		_spec.ClearField(fueltransaction.FieldStaffCardID, field.TypeString)
	}
	if ftuo.mutation.OrderLinkCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   fueltransaction.OrderLinkTable,
			Columns: []string{fueltransaction.OrderLinkColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ftuo.mutation.OrderLinkIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   fueltransaction.OrderLinkTable,
			Columns: []string{fueltransaction.OrderLinkColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &FuelTransaction{config: ftuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ftuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{fueltransaction.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ftuo.mutation.done = true
	return _node, nil
}
