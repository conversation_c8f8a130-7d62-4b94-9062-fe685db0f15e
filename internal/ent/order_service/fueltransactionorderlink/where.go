// Code generated by ent, DO NOT EDIT.

package fueltransactionorderlink

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldContainsFold(FieldID, id))
}

// FuelTransactionID applies equality check predicate on the "fuel_transaction_id" field. It's identical to FuelTransactionIDEQ.
func FuelTransactionID(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldFuelTransactionID, v))
}

// OrderID applies equality check predicate on the "order_id" field. It's identical to OrderIDEQ.
func OrderID(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldOrderID, v))
}

// AllocatedAmount applies equality check predicate on the "allocated_amount" field. It's identical to AllocatedAmountEQ.
func AllocatedAmount(v float64) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldAllocatedAmount, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldStatus, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeactivatedAt applies equality check predicate on the "deactivated_at" field. It's identical to DeactivatedAtEQ.
func DeactivatedAt(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldDeactivatedAt, v))
}

// FuelTransactionIDEQ applies the EQ predicate on the "fuel_transaction_id" field.
func FuelTransactionIDEQ(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldFuelTransactionID, v))
}

// FuelTransactionIDNEQ applies the NEQ predicate on the "fuel_transaction_id" field.
func FuelTransactionIDNEQ(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNEQ(FieldFuelTransactionID, v))
}

// FuelTransactionIDIn applies the In predicate on the "fuel_transaction_id" field.
func FuelTransactionIDIn(vs ...string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldIn(FieldFuelTransactionID, vs...))
}

// FuelTransactionIDNotIn applies the NotIn predicate on the "fuel_transaction_id" field.
func FuelTransactionIDNotIn(vs ...string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNotIn(FieldFuelTransactionID, vs...))
}

// FuelTransactionIDGT applies the GT predicate on the "fuel_transaction_id" field.
func FuelTransactionIDGT(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGT(FieldFuelTransactionID, v))
}

// FuelTransactionIDGTE applies the GTE predicate on the "fuel_transaction_id" field.
func FuelTransactionIDGTE(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGTE(FieldFuelTransactionID, v))
}

// FuelTransactionIDLT applies the LT predicate on the "fuel_transaction_id" field.
func FuelTransactionIDLT(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLT(FieldFuelTransactionID, v))
}

// FuelTransactionIDLTE applies the LTE predicate on the "fuel_transaction_id" field.
func FuelTransactionIDLTE(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLTE(FieldFuelTransactionID, v))
}

// FuelTransactionIDContains applies the Contains predicate on the "fuel_transaction_id" field.
func FuelTransactionIDContains(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldContains(FieldFuelTransactionID, v))
}

// FuelTransactionIDHasPrefix applies the HasPrefix predicate on the "fuel_transaction_id" field.
func FuelTransactionIDHasPrefix(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldHasPrefix(FieldFuelTransactionID, v))
}

// FuelTransactionIDHasSuffix applies the HasSuffix predicate on the "fuel_transaction_id" field.
func FuelTransactionIDHasSuffix(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldHasSuffix(FieldFuelTransactionID, v))
}

// FuelTransactionIDEqualFold applies the EqualFold predicate on the "fuel_transaction_id" field.
func FuelTransactionIDEqualFold(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEqualFold(FieldFuelTransactionID, v))
}

// FuelTransactionIDContainsFold applies the ContainsFold predicate on the "fuel_transaction_id" field.
func FuelTransactionIDContainsFold(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldContainsFold(FieldFuelTransactionID, v))
}

// OrderIDEQ applies the EQ predicate on the "order_id" field.
func OrderIDEQ(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldOrderID, v))
}

// OrderIDNEQ applies the NEQ predicate on the "order_id" field.
func OrderIDNEQ(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNEQ(FieldOrderID, v))
}

// OrderIDIn applies the In predicate on the "order_id" field.
func OrderIDIn(vs ...string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldIn(FieldOrderID, vs...))
}

// OrderIDNotIn applies the NotIn predicate on the "order_id" field.
func OrderIDNotIn(vs ...string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNotIn(FieldOrderID, vs...))
}

// OrderIDGT applies the GT predicate on the "order_id" field.
func OrderIDGT(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGT(FieldOrderID, v))
}

// OrderIDGTE applies the GTE predicate on the "order_id" field.
func OrderIDGTE(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGTE(FieldOrderID, v))
}

// OrderIDLT applies the LT predicate on the "order_id" field.
func OrderIDLT(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLT(FieldOrderID, v))
}

// OrderIDLTE applies the LTE predicate on the "order_id" field.
func OrderIDLTE(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLTE(FieldOrderID, v))
}

// OrderIDContains applies the Contains predicate on the "order_id" field.
func OrderIDContains(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldContains(FieldOrderID, v))
}

// OrderIDHasPrefix applies the HasPrefix predicate on the "order_id" field.
func OrderIDHasPrefix(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldHasPrefix(FieldOrderID, v))
}

// OrderIDHasSuffix applies the HasSuffix predicate on the "order_id" field.
func OrderIDHasSuffix(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldHasSuffix(FieldOrderID, v))
}

// OrderIDEqualFold applies the EqualFold predicate on the "order_id" field.
func OrderIDEqualFold(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEqualFold(FieldOrderID, v))
}

// OrderIDContainsFold applies the ContainsFold predicate on the "order_id" field.
func OrderIDContainsFold(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldContainsFold(FieldOrderID, v))
}

// AllocatedAmountEQ applies the EQ predicate on the "allocated_amount" field.
func AllocatedAmountEQ(v float64) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldAllocatedAmount, v))
}

// AllocatedAmountNEQ applies the NEQ predicate on the "allocated_amount" field.
func AllocatedAmountNEQ(v float64) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNEQ(FieldAllocatedAmount, v))
}

// AllocatedAmountIn applies the In predicate on the "allocated_amount" field.
func AllocatedAmountIn(vs ...float64) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldIn(FieldAllocatedAmount, vs...))
}

// AllocatedAmountNotIn applies the NotIn predicate on the "allocated_amount" field.
func AllocatedAmountNotIn(vs ...float64) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNotIn(FieldAllocatedAmount, vs...))
}

// AllocatedAmountGT applies the GT predicate on the "allocated_amount" field.
func AllocatedAmountGT(v float64) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGT(FieldAllocatedAmount, v))
}

// AllocatedAmountGTE applies the GTE predicate on the "allocated_amount" field.
func AllocatedAmountGTE(v float64) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGTE(FieldAllocatedAmount, v))
}

// AllocatedAmountLT applies the LT predicate on the "allocated_amount" field.
func AllocatedAmountLT(v float64) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLT(FieldAllocatedAmount, v))
}

// AllocatedAmountLTE applies the LTE predicate on the "allocated_amount" field.
func AllocatedAmountLTE(v float64) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLTE(FieldAllocatedAmount, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldContainsFold(FieldStatus, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeactivatedAtEQ applies the EQ predicate on the "deactivated_at" field.
func DeactivatedAtEQ(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldEQ(FieldDeactivatedAt, v))
}

// DeactivatedAtNEQ applies the NEQ predicate on the "deactivated_at" field.
func DeactivatedAtNEQ(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNEQ(FieldDeactivatedAt, v))
}

// DeactivatedAtIn applies the In predicate on the "deactivated_at" field.
func DeactivatedAtIn(vs ...time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldIn(FieldDeactivatedAt, vs...))
}

// DeactivatedAtNotIn applies the NotIn predicate on the "deactivated_at" field.
func DeactivatedAtNotIn(vs ...time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNotIn(FieldDeactivatedAt, vs...))
}

// DeactivatedAtGT applies the GT predicate on the "deactivated_at" field.
func DeactivatedAtGT(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGT(FieldDeactivatedAt, v))
}

// DeactivatedAtGTE applies the GTE predicate on the "deactivated_at" field.
func DeactivatedAtGTE(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldGTE(FieldDeactivatedAt, v))
}

// DeactivatedAtLT applies the LT predicate on the "deactivated_at" field.
func DeactivatedAtLT(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLT(FieldDeactivatedAt, v))
}

// DeactivatedAtLTE applies the LTE predicate on the "deactivated_at" field.
func DeactivatedAtLTE(v time.Time) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldLTE(FieldDeactivatedAt, v))
}

// DeactivatedAtIsNil applies the IsNil predicate on the "deactivated_at" field.
func DeactivatedAtIsNil() predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldIsNull(FieldDeactivatedAt))
}

// DeactivatedAtNotNil applies the NotNil predicate on the "deactivated_at" field.
func DeactivatedAtNotNil() predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.FieldNotNull(FieldDeactivatedAt))
}

// HasFuelTransaction applies the HasEdge predicate on the "fuel_transaction" edge.
func HasFuelTransaction() predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, FuelTransactionTable, FuelTransactionColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasFuelTransactionWith applies the HasEdge predicate on the "fuel_transaction" edge with a given conditions (other predicates).
func HasFuelTransactionWith(preds ...predicate.FuelTransaction) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(func(s *sql.Selector) {
		step := newFuelTransactionStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasOrder applies the HasEdge predicate on the "order" edge.
func HasOrder() predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, OrderTable, OrderColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasOrderWith applies the HasEdge predicate on the "order" edge with a given conditions (other predicates).
func HasOrderWith(preds ...predicate.Order) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(func(s *sql.Selector) {
		step := newOrderStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.FuelTransactionOrderLink) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.FuelTransactionOrderLink) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.FuelTransactionOrderLink) predicate.FuelTransactionOrderLink {
	return predicate.FuelTransactionOrderLink(sql.NotPredicates(p))
}
