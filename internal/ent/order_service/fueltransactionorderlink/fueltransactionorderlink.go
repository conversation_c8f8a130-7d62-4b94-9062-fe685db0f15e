// Code generated by ent, DO NOT EDIT.

package fueltransactionorderlink

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the fueltransactionorderlink type in the database.
	Label = "fuel_transaction_order_link"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldFuelTransactionID holds the string denoting the fuel_transaction_id field in the database.
	FieldFuelTransactionID = "fuel_transaction_id"
	// FieldOrderID holds the string denoting the order_id field in the database.
	FieldOrderID = "order_id"
	// FieldAllocatedAmount holds the string denoting the allocated_amount field in the database.
	FieldAllocatedAmount = "allocated_amount"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeactivatedAt holds the string denoting the deactivated_at field in the database.
	FieldDeactivatedAt = "deactivated_at"
	// EdgeFuelTransaction holds the string denoting the fuel_transaction edge name in mutations.
	EdgeFuelTransaction = "fuel_transaction"
	// EdgeOrder holds the string denoting the order edge name in mutations.
	EdgeOrder = "order"
	// Table holds the table name of the fueltransactionorderlink in the database.
	Table = "fuel_transaction_order_links"
	// FuelTransactionTable is the table that holds the fuel_transaction relation/edge.
	FuelTransactionTable = "fuel_transaction_order_links"
	// FuelTransactionInverseTable is the table name for the FuelTransaction entity.
	// It exists in this package in order to avoid circular dependency with the "fueltransaction" package.
	FuelTransactionInverseTable = "fuel_transactions"
	// FuelTransactionColumn is the table column denoting the fuel_transaction relation/edge.
	FuelTransactionColumn = "fuel_transaction_id"
	// OrderTable is the table that holds the order relation/edge.
	OrderTable = "fuel_transaction_order_links"
	// OrderInverseTable is the table name for the Order entity.
	// It exists in this package in order to avoid circular dependency with the "order" package.
	OrderInverseTable = "orders"
	// OrderColumn is the table column denoting the order relation/edge.
	OrderColumn = "order_id"
)

// Columns holds all SQL columns for fueltransactionorderlink fields.
var Columns = []string{
	FieldID,
	FieldFuelTransactionID,
	FieldOrderID,
	FieldAllocatedAmount,
	FieldStatus,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeactivatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus string
	// StatusValidator is a validator for the "status" field. It is called by the builders before save.
	StatusValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the FuelTransactionOrderLink queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByFuelTransactionID orders the results by the fuel_transaction_id field.
func ByFuelTransactionID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFuelTransactionID, opts...).ToFunc()
}

// ByOrderID orders the results by the order_id field.
func ByOrderID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrderID, opts...).ToFunc()
}

// ByAllocatedAmount orders the results by the allocated_amount field.
func ByAllocatedAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAllocatedAmount, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeactivatedAt orders the results by the deactivated_at field.
func ByDeactivatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeactivatedAt, opts...).ToFunc()
}

// ByFuelTransactionField orders the results by fuel_transaction field.
func ByFuelTransactionField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newFuelTransactionStep(), sql.OrderByField(field, opts...))
	}
}

// ByOrderField orders the results by order field.
func ByOrderField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newOrderStep(), sql.OrderByField(field, opts...))
	}
}
func newFuelTransactionStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(FuelTransactionInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2O, true, FuelTransactionTable, FuelTransactionColumn),
	)
}
func newOrderStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(OrderInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2O, true, OrderTable, OrderColumn),
	)
}
