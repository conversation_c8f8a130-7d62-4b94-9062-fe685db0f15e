// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
)

// FuelTransactionOrderLink is the model entity for the FuelTransactionOrderLink schema.
type FuelTransactionOrderLink struct {
	config `json:"-"`
	// ID of the ent.
	// 主键ID
	ID string `json:"id,omitempty"`
	// 燃油交易ID
	FuelTransactionID string `json:"fuel_transaction_id,omitempty"`
	// 订单ID
	OrderID string `json:"order_id,omitempty"`
	// 分配金额
	AllocatedAmount float64 `json:"allocated_amount,omitempty"`
	// 关联状态
	Status string `json:"status,omitempty"`
	// 元数据
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 停用时间
	DeactivatedAt *time.Time `json:"deactivated_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the FuelTransactionOrderLinkQuery when eager-loading is set.
	Edges        FuelTransactionOrderLinkEdges `json:"edges"`
	selectValues sql.SelectValues
}

// FuelTransactionOrderLinkEdges holds the relations/edges for other nodes in the graph.
type FuelTransactionOrderLinkEdges struct {
	// FuelTransaction holds the value of the fuel_transaction edge.
	FuelTransaction *FuelTransaction `json:"fuel_transaction,omitempty"`
	// Order holds the value of the order edge.
	Order *Order `json:"order,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// FuelTransactionOrErr returns the FuelTransaction value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e FuelTransactionOrderLinkEdges) FuelTransactionOrErr() (*FuelTransaction, error) {
	if e.FuelTransaction != nil {
		return e.FuelTransaction, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: fueltransaction.Label}
	}
	return nil, &NotLoadedError{edge: "fuel_transaction"}
}

// OrderOrErr returns the Order value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e FuelTransactionOrderLinkEdges) OrderOrErr() (*Order, error) {
	if e.Order != nil {
		return e.Order, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: order.Label}
	}
	return nil, &NotLoadedError{edge: "order"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*FuelTransactionOrderLink) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case fueltransactionorderlink.FieldMetadata:
			values[i] = new([]byte)
		case fueltransactionorderlink.FieldAllocatedAmount:
			values[i] = new(sql.NullFloat64)
		case fueltransactionorderlink.FieldID, fueltransactionorderlink.FieldFuelTransactionID, fueltransactionorderlink.FieldOrderID, fueltransactionorderlink.FieldStatus:
			values[i] = new(sql.NullString)
		case fueltransactionorderlink.FieldCreatedAt, fueltransactionorderlink.FieldUpdatedAt, fueltransactionorderlink.FieldDeactivatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the FuelTransactionOrderLink fields.
func (ftol *FuelTransactionOrderLink) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case fueltransactionorderlink.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				ftol.ID = value.String
			}
		case fueltransactionorderlink.FieldFuelTransactionID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field fuel_transaction_id", values[i])
			} else if value.Valid {
				ftol.FuelTransactionID = value.String
			}
		case fueltransactionorderlink.FieldOrderID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field order_id", values[i])
			} else if value.Valid {
				ftol.OrderID = value.String
			}
		case fueltransactionorderlink.FieldAllocatedAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field allocated_amount", values[i])
			} else if value.Valid {
				ftol.AllocatedAmount = value.Float64
			}
		case fueltransactionorderlink.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				ftol.Status = value.String
			}
		case fueltransactionorderlink.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ftol.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case fueltransactionorderlink.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				ftol.CreatedAt = value.Time
			}
		case fueltransactionorderlink.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				ftol.UpdatedAt = value.Time
			}
		case fueltransactionorderlink.FieldDeactivatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deactivated_at", values[i])
			} else if value.Valid {
				ftol.DeactivatedAt = new(time.Time)
				*ftol.DeactivatedAt = value.Time
			}
		default:
			ftol.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the FuelTransactionOrderLink.
// This includes values selected through modifiers, order, etc.
func (ftol *FuelTransactionOrderLink) Value(name string) (ent.Value, error) {
	return ftol.selectValues.Get(name)
}

// QueryFuelTransaction queries the "fuel_transaction" edge of the FuelTransactionOrderLink entity.
func (ftol *FuelTransactionOrderLink) QueryFuelTransaction() *FuelTransactionQuery {
	return NewFuelTransactionOrderLinkClient(ftol.config).QueryFuelTransaction(ftol)
}

// QueryOrder queries the "order" edge of the FuelTransactionOrderLink entity.
func (ftol *FuelTransactionOrderLink) QueryOrder() *OrderQuery {
	return NewFuelTransactionOrderLinkClient(ftol.config).QueryOrder(ftol)
}

// Update returns a builder for updating this FuelTransactionOrderLink.
// Note that you need to call FuelTransactionOrderLink.Unwrap() before calling this method if this FuelTransactionOrderLink
// was returned from a transaction, and the transaction was committed or rolled back.
func (ftol *FuelTransactionOrderLink) Update() *FuelTransactionOrderLinkUpdateOne {
	return NewFuelTransactionOrderLinkClient(ftol.config).UpdateOne(ftol)
}

// Unwrap unwraps the FuelTransactionOrderLink entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ftol *FuelTransactionOrderLink) Unwrap() *FuelTransactionOrderLink {
	_tx, ok := ftol.config.driver.(*txDriver)
	if !ok {
		panic("order_service: FuelTransactionOrderLink is not a transactional entity")
	}
	ftol.config.driver = _tx.drv
	return ftol
}

// String implements the fmt.Stringer.
func (ftol *FuelTransactionOrderLink) String() string {
	var builder strings.Builder
	builder.WriteString("FuelTransactionOrderLink(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ftol.ID))
	builder.WriteString("fuel_transaction_id=")
	builder.WriteString(ftol.FuelTransactionID)
	builder.WriteString(", ")
	builder.WriteString("order_id=")
	builder.WriteString(ftol.OrderID)
	builder.WriteString(", ")
	builder.WriteString("allocated_amount=")
	builder.WriteString(fmt.Sprintf("%v", ftol.AllocatedAmount))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(ftol.Status)
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", ftol.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(ftol.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(ftol.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := ftol.DeactivatedAt; v != nil {
		builder.WriteString("deactivated_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// FuelTransactionOrderLinks is a parsable slice of FuelTransactionOrderLink.
type FuelTransactionOrderLinks []*FuelTransactionOrderLink
