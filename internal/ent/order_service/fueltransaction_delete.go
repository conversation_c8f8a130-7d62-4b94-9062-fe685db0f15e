// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

// FuelTransactionDelete is the builder for deleting a FuelTransaction entity.
type FuelTransactionDelete struct {
	config
	hooks    []Hook
	mutation *FuelTransactionMutation
}

// Where appends a list predicates to the FuelTransactionDelete builder.
func (ftd *FuelTransactionDelete) Where(ps ...predicate.FuelTransaction) *FuelTransactionDelete {
	ftd.mutation.Where(ps...)
	return ftd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ftd *FuelTransactionDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ftd.sqlExec, ftd.mutation, ftd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ftd *FuelTransactionDelete) ExecX(ctx context.Context) int {
	n, err := ftd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ftd *FuelTransactionDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(fueltransaction.Table, sqlgraph.NewFieldSpec(fueltransaction.FieldID, field.TypeString))
	if ps := ftd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ftd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ftd.mutation.done = true
	return affected, err
}

// FuelTransactionDeleteOne is the builder for deleting a single FuelTransaction entity.
type FuelTransactionDeleteOne struct {
	ftd *FuelTransactionDelete
}

// Where appends a list predicates to the FuelTransactionDelete builder.
func (ftdo *FuelTransactionDeleteOne) Where(ps ...predicate.FuelTransaction) *FuelTransactionDeleteOne {
	ftdo.ftd.mutation.Where(ps...)
	return ftdo
}

// Exec executes the deletion query.
func (ftdo *FuelTransactionDeleteOne) Exec(ctx context.Context) error {
	n, err := ftdo.ftd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{fueltransaction.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ftdo *FuelTransactionDeleteOne) ExecX(ctx context.Context) {
	if err := ftdo.Exec(ctx); err != nil {
		panic(err)
	}
}
