// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
)

// FuelTransaction is the model entity for the FuelTransaction schema.
type FuelTransaction struct {
	config `json:"-"`
	// ID of the ent.
	// 主键ID
	ID string `json:"id,omitempty"`
	// 交易编号
	TransactionNumber string `json:"transaction_number,omitempty"`
	// 加油站ID
	StationID int64 `json:"station_id,omitempty"`
	// 油泵ID
	PumpID string `json:"pump_id,omitempty"`
	// 油枪ID
	NozzleID string `json:"nozzle_id,omitempty"`
	// 燃油类型
	FuelType string `json:"fuel_type,omitempty"`
	// 燃油等级
	FuelGrade string `json:"fuel_grade,omitempty"`
	// 油罐编号
	Tank int `json:"tank,omitempty"`
	// 单价
	UnitPrice float64 `json:"unit_price,omitempty"`
	// 加油量
	Volume float64 `json:"volume,omitempty"`
	// 金额
	Amount float64 `json:"amount,omitempty"`
	// 总加油量
	TotalVolume float64 `json:"total_volume,omitempty"`
	// 总金额
	TotalAmount float64 `json:"total_amount,omitempty"`
	// 状态
	Status string `json:"status,omitempty"`
	// 会员卡ID
	MemberCardID *string `json:"member_card_id,omitempty"`
	// 会员ID
	MemberID *string `json:"member_id,omitempty"`
	// 员工ID
	EmployeeID *string `json:"employee_id,omitempty"`
	// FCC交易ID
	FccTransactionID *string `json:"fcc_transaction_id,omitempty"`
	// POS终端ID
	PosTerminalID *string `json:"pos_terminal_id,omitempty"`
	// 元数据
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 处理时间
	ProcessedAt *time.Time `json:"processed_at,omitempty"`
	// 取消时间
	CancelledAt *time.Time `json:"cancelled_at,omitempty"`
	// 起始计数器
	StartTotalizer *float64 `json:"start_totalizer,omitempty"`
	// 结束计数器
	EndTotalizer *float64 `json:"end_totalizer,omitempty"`
	// 油枪开始时间
	NozzleStartTime *time.Time `json:"nozzle_start_time,omitempty"`
	// 油枪结束时间
	NozzleEndTime *time.Time `json:"nozzle_end_time,omitempty"`
	// 员工卡ID
	StaffCardID *string `json:"staff_card_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the FuelTransactionQuery when eager-loading is set.
	Edges        FuelTransactionEdges `json:"edges"`
	selectValues sql.SelectValues
}

// FuelTransactionEdges holds the relations/edges for other nodes in the graph.
type FuelTransactionEdges struct {
	// OrderLink holds the value of the order_link edge.
	OrderLink *FuelTransactionOrderLink `json:"order_link,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// OrderLinkOrErr returns the OrderLink value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e FuelTransactionEdges) OrderLinkOrErr() (*FuelTransactionOrderLink, error) {
	if e.OrderLink != nil {
		return e.OrderLink, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: fueltransactionorderlink.Label}
	}
	return nil, &NotLoadedError{edge: "order_link"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*FuelTransaction) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case fueltransaction.FieldMetadata:
			values[i] = new([]byte)
		case fueltransaction.FieldUnitPrice, fueltransaction.FieldVolume, fueltransaction.FieldAmount, fueltransaction.FieldTotalVolume, fueltransaction.FieldTotalAmount, fueltransaction.FieldStartTotalizer, fueltransaction.FieldEndTotalizer:
			values[i] = new(sql.NullFloat64)
		case fueltransaction.FieldStationID, fueltransaction.FieldTank:
			values[i] = new(sql.NullInt64)
		case fueltransaction.FieldID, fueltransaction.FieldTransactionNumber, fueltransaction.FieldPumpID, fueltransaction.FieldNozzleID, fueltransaction.FieldFuelType, fueltransaction.FieldFuelGrade, fueltransaction.FieldStatus, fueltransaction.FieldMemberCardID, fueltransaction.FieldMemberID, fueltransaction.FieldEmployeeID, fueltransaction.FieldFccTransactionID, fueltransaction.FieldPosTerminalID, fueltransaction.FieldStaffCardID:
			values[i] = new(sql.NullString)
		case fueltransaction.FieldCreatedAt, fueltransaction.FieldUpdatedAt, fueltransaction.FieldProcessedAt, fueltransaction.FieldCancelledAt, fueltransaction.FieldNozzleStartTime, fueltransaction.FieldNozzleEndTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the FuelTransaction fields.
func (ft *FuelTransaction) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case fueltransaction.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				ft.ID = value.String
			}
		case fueltransaction.FieldTransactionNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transaction_number", values[i])
			} else if value.Valid {
				ft.TransactionNumber = value.String
			}
		case fueltransaction.FieldStationID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field station_id", values[i])
			} else if value.Valid {
				ft.StationID = value.Int64
			}
		case fueltransaction.FieldPumpID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field pump_id", values[i])
			} else if value.Valid {
				ft.PumpID = value.String
			}
		case fueltransaction.FieldNozzleID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field nozzle_id", values[i])
			} else if value.Valid {
				ft.NozzleID = value.String
			}
		case fueltransaction.FieldFuelType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field fuel_type", values[i])
			} else if value.Valid {
				ft.FuelType = value.String
			}
		case fueltransaction.FieldFuelGrade:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field fuel_grade", values[i])
			} else if value.Valid {
				ft.FuelGrade = value.String
			}
		case fueltransaction.FieldTank:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tank", values[i])
			} else if value.Valid {
				ft.Tank = int(value.Int64)
			}
		case fueltransaction.FieldUnitPrice:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field unit_price", values[i])
			} else if value.Valid {
				ft.UnitPrice = value.Float64
			}
		case fueltransaction.FieldVolume:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field volume", values[i])
			} else if value.Valid {
				ft.Volume = value.Float64
			}
		case fueltransaction.FieldAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field amount", values[i])
			} else if value.Valid {
				ft.Amount = value.Float64
			}
		case fueltransaction.FieldTotalVolume:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field total_volume", values[i])
			} else if value.Valid {
				ft.TotalVolume = value.Float64
			}
		case fueltransaction.FieldTotalAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field total_amount", values[i])
			} else if value.Valid {
				ft.TotalAmount = value.Float64
			}
		case fueltransaction.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				ft.Status = value.String
			}
		case fueltransaction.FieldMemberCardID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field member_card_id", values[i])
			} else if value.Valid {
				ft.MemberCardID = new(string)
				*ft.MemberCardID = value.String
			}
		case fueltransaction.FieldMemberID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field member_id", values[i])
			} else if value.Valid {
				ft.MemberID = new(string)
				*ft.MemberID = value.String
			}
		case fueltransaction.FieldEmployeeID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field employee_id", values[i])
			} else if value.Valid {
				ft.EmployeeID = new(string)
				*ft.EmployeeID = value.String
			}
		case fueltransaction.FieldFccTransactionID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field fcc_transaction_id", values[i])
			} else if value.Valid {
				ft.FccTransactionID = new(string)
				*ft.FccTransactionID = value.String
			}
		case fueltransaction.FieldPosTerminalID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field pos_terminal_id", values[i])
			} else if value.Valid {
				ft.PosTerminalID = new(string)
				*ft.PosTerminalID = value.String
			}
		case fueltransaction.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ft.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case fueltransaction.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				ft.CreatedAt = value.Time
			}
		case fueltransaction.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				ft.UpdatedAt = value.Time
			}
		case fueltransaction.FieldProcessedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field processed_at", values[i])
			} else if value.Valid {
				ft.ProcessedAt = new(time.Time)
				*ft.ProcessedAt = value.Time
			}
		case fueltransaction.FieldCancelledAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field cancelled_at", values[i])
			} else if value.Valid {
				ft.CancelledAt = new(time.Time)
				*ft.CancelledAt = value.Time
			}
		case fueltransaction.FieldStartTotalizer:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field start_totalizer", values[i])
			} else if value.Valid {
				ft.StartTotalizer = new(float64)
				*ft.StartTotalizer = value.Float64
			}
		case fueltransaction.FieldEndTotalizer:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field end_totalizer", values[i])
			} else if value.Valid {
				ft.EndTotalizer = new(float64)
				*ft.EndTotalizer = value.Float64
			}
		case fueltransaction.FieldNozzleStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field nozzle_start_time", values[i])
			} else if value.Valid {
				ft.NozzleStartTime = new(time.Time)
				*ft.NozzleStartTime = value.Time
			}
		case fueltransaction.FieldNozzleEndTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field nozzle_end_time", values[i])
			} else if value.Valid {
				ft.NozzleEndTime = new(time.Time)
				*ft.NozzleEndTime = value.Time
			}
		case fueltransaction.FieldStaffCardID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field staff_card_id", values[i])
			} else if value.Valid {
				ft.StaffCardID = new(string)
				*ft.StaffCardID = value.String
			}
		default:
			ft.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the FuelTransaction.
// This includes values selected through modifiers, order, etc.
func (ft *FuelTransaction) Value(name string) (ent.Value, error) {
	return ft.selectValues.Get(name)
}

// QueryOrderLink queries the "order_link" edge of the FuelTransaction entity.
func (ft *FuelTransaction) QueryOrderLink() *FuelTransactionOrderLinkQuery {
	return NewFuelTransactionClient(ft.config).QueryOrderLink(ft)
}

// Update returns a builder for updating this FuelTransaction.
// Note that you need to call FuelTransaction.Unwrap() before calling this method if this FuelTransaction
// was returned from a transaction, and the transaction was committed or rolled back.
func (ft *FuelTransaction) Update() *FuelTransactionUpdateOne {
	return NewFuelTransactionClient(ft.config).UpdateOne(ft)
}

// Unwrap unwraps the FuelTransaction entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ft *FuelTransaction) Unwrap() *FuelTransaction {
	_tx, ok := ft.config.driver.(*txDriver)
	if !ok {
		panic("order_service: FuelTransaction is not a transactional entity")
	}
	ft.config.driver = _tx.drv
	return ft
}

// String implements the fmt.Stringer.
func (ft *FuelTransaction) String() string {
	var builder strings.Builder
	builder.WriteString("FuelTransaction(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ft.ID))
	builder.WriteString("transaction_number=")
	builder.WriteString(ft.TransactionNumber)
	builder.WriteString(", ")
	builder.WriteString("station_id=")
	builder.WriteString(fmt.Sprintf("%v", ft.StationID))
	builder.WriteString(", ")
	builder.WriteString("pump_id=")
	builder.WriteString(ft.PumpID)
	builder.WriteString(", ")
	builder.WriteString("nozzle_id=")
	builder.WriteString(ft.NozzleID)
	builder.WriteString(", ")
	builder.WriteString("fuel_type=")
	builder.WriteString(ft.FuelType)
	builder.WriteString(", ")
	builder.WriteString("fuel_grade=")
	builder.WriteString(ft.FuelGrade)
	builder.WriteString(", ")
	builder.WriteString("tank=")
	builder.WriteString(fmt.Sprintf("%v", ft.Tank))
	builder.WriteString(", ")
	builder.WriteString("unit_price=")
	builder.WriteString(fmt.Sprintf("%v", ft.UnitPrice))
	builder.WriteString(", ")
	builder.WriteString("volume=")
	builder.WriteString(fmt.Sprintf("%v", ft.Volume))
	builder.WriteString(", ")
	builder.WriteString("amount=")
	builder.WriteString(fmt.Sprintf("%v", ft.Amount))
	builder.WriteString(", ")
	builder.WriteString("total_volume=")
	builder.WriteString(fmt.Sprintf("%v", ft.TotalVolume))
	builder.WriteString(", ")
	builder.WriteString("total_amount=")
	builder.WriteString(fmt.Sprintf("%v", ft.TotalAmount))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(ft.Status)
	builder.WriteString(", ")
	if v := ft.MemberCardID; v != nil {
		builder.WriteString("member_card_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := ft.MemberID; v != nil {
		builder.WriteString("member_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := ft.EmployeeID; v != nil {
		builder.WriteString("employee_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := ft.FccTransactionID; v != nil {
		builder.WriteString("fcc_transaction_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := ft.PosTerminalID; v != nil {
		builder.WriteString("pos_terminal_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", ft.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(ft.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(ft.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := ft.ProcessedAt; v != nil {
		builder.WriteString("processed_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := ft.CancelledAt; v != nil {
		builder.WriteString("cancelled_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := ft.StartTotalizer; v != nil {
		builder.WriteString("start_totalizer=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := ft.EndTotalizer; v != nil {
		builder.WriteString("end_totalizer=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := ft.NozzleStartTime; v != nil {
		builder.WriteString("nozzle_start_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := ft.NozzleEndTime; v != nil {
		builder.WriteString("nozzle_end_time=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := ft.StaffCardID; v != nil {
		builder.WriteString("staff_card_id=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// FuelTransactions is a parsable slice of FuelTransaction.
type FuelTransactions []*FuelTransaction
