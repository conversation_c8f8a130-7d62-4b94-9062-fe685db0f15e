// Code generated by ent, DO NOT EDIT.

package hook

import (
	"context"
	"fmt"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service"
)

// The FuelTransactionFunc type is an adapter to allow the use of ordinary
// function as FuelTransaction mutator.
type FuelTransactionFunc func(context.Context, *order_service.FuelTransactionMutation) (order_service.Value, error)

// Mutate calls f(ctx, m).
func (f FuelTransactionFunc) Mutate(ctx context.Context, m order_service.Mutation) (order_service.Value, error) {
	if mv, ok := m.(*order_service.FuelTransactionMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *order_service.FuelTransactionMutation", m)
}

// The FuelTransactionOrderLinkFunc type is an adapter to allow the use of ordinary
// function as FuelTransactionOrderLink mutator.
type FuelTransactionOrderLinkFunc func(context.Context, *order_service.FuelTransactionOrderLinkMutation) (order_service.Value, error)

// Mutate calls f(ctx, m).
func (f FuelTransactionOrderLinkFunc) Mutate(ctx context.Context, m order_service.Mutation) (order_service.Value, error) {
	if mv, ok := m.(*order_service.FuelTransactionOrderLinkMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *order_service.FuelTransactionOrderLinkMutation", m)
}

// The OrderFunc type is an adapter to allow the use of ordinary
// function as Order mutator.
type OrderFunc func(context.Context, *order_service.OrderMutation) (order_service.Value, error)

// Mutate calls f(ctx, m).
func (f OrderFunc) Mutate(ctx context.Context, m order_service.Mutation) (order_service.Value, error) {
	if mv, ok := m.(*order_service.OrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *order_service.OrderMutation", m)
}

// Condition is a hook condition function.
type Condition func(context.Context, order_service.Mutation) bool

// And groups conditions with the AND operator.
func And(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m order_service.Mutation) bool {
		if !first(ctx, m) || !second(ctx, m) {
			return false
		}
		for _, cond := range rest {
			if !cond(ctx, m) {
				return false
			}
		}
		return true
	}
}

// Or groups conditions with the OR operator.
func Or(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m order_service.Mutation) bool {
		if first(ctx, m) || second(ctx, m) {
			return true
		}
		for _, cond := range rest {
			if cond(ctx, m) {
				return true
			}
		}
		return false
	}
}

// Not negates a given condition.
func Not(cond Condition) Condition {
	return func(ctx context.Context, m order_service.Mutation) bool {
		return !cond(ctx, m)
	}
}

// HasOp is a condition testing mutation operation.
func HasOp(op order_service.Op) Condition {
	return func(_ context.Context, m order_service.Mutation) bool {
		return m.Op().Is(op)
	}
}

// HasAddedFields is a condition validating `.AddedField` on fields.
func HasAddedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m order_service.Mutation) bool {
		if _, exists := m.AddedField(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.AddedField(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasClearedFields is a condition validating `.FieldCleared` on fields.
func HasClearedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m order_service.Mutation) bool {
		if exists := m.FieldCleared(field); !exists {
			return false
		}
		for _, field := range fields {
			if exists := m.FieldCleared(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasFields is a condition validating `.Field` on fields.
func HasFields(field string, fields ...string) Condition {
	return func(_ context.Context, m order_service.Mutation) bool {
		if _, exists := m.Field(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.Field(field); !exists {
				return false
			}
		}
		return true
	}
}

// If executes the given hook under condition.
//
//	hook.If(ComputeAverage, And(HasFields(...), HasAddedFields(...)))
func If(hk order_service.Hook, cond Condition) order_service.Hook {
	return func(next order_service.Mutator) order_service.Mutator {
		return order_service.MutateFunc(func(ctx context.Context, m order_service.Mutation) (order_service.Value, error) {
			if cond(ctx, m) {
				return hk(next).Mutate(ctx, m)
			}
			return next.Mutate(ctx, m)
		})
	}
}

// On executes the given hook only for the given operation.
//
//	hook.On(Log, order_service.Delete|order_service.Create)
func On(hk order_service.Hook, op order_service.Op) order_service.Hook {
	return If(hk, HasOp(op))
}

// Unless skips the given hook only for the given operation.
//
//	hook.Unless(Log, order_service.Update|order_service.UpdateOne)
func Unless(hk order_service.Hook, op order_service.Op) order_service.Hook {
	return If(hk, Not(HasOp(op)))
}

// FixedError is a hook returning a fixed error.
func FixedError(err error) order_service.Hook {
	return func(order_service.Mutator) order_service.Mutator {
		return order_service.MutateFunc(func(context.Context, order_service.Mutation) (order_service.Value, error) {
			return nil, err
		})
	}
}

// Reject returns a hook that rejects all operations that match op.
//
//	func (T) Hooks() []order_service.Hook {
//		return []order_service.Hook{
//			Reject(order_service.Delete|order_service.Update),
//		}
//	}
func Reject(op order_service.Op) order_service.Hook {
	hk := FixedError(fmt.Errorf("%s operation is not allowed", op))
	return On(hk, op)
}

// Chain acts as a list of hooks and is effectively immutable.
// Once created, it will always hold the same set of hooks in the same order.
type Chain struct {
	hooks []order_service.Hook
}

// NewChain creates a new chain of hooks.
func NewChain(hooks ...order_service.Hook) Chain {
	return Chain{append([]order_service.Hook(nil), hooks...)}
}

// Hook chains the list of hooks and returns the final hook.
func (c Chain) Hook() order_service.Hook {
	return func(mutator order_service.Mutator) order_service.Mutator {
		for i := len(c.hooks) - 1; i >= 0; i-- {
			mutator = c.hooks[i](mutator)
		}
		return mutator
	}
}

// Append extends a chain, adding the specified hook
// as the last ones in the mutation flow.
func (c Chain) Append(hooks ...order_service.Hook) Chain {
	newHooks := make([]order_service.Hook, 0, len(c.hooks)+len(hooks))
	newHooks = append(newHooks, c.hooks...)
	newHooks = append(newHooks, hooks...)
	return Chain{newHooks}
}

// Extend extends a chain, adding the specified chain
// as the last ones in the mutation flow.
func (c Chain) Extend(chain Chain) Chain {
	return c.Append(chain.hooks...)
}
