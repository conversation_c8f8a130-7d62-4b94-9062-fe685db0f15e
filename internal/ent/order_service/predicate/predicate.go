// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// FuelTransaction is the predicate function for fueltransaction builders.
type FuelTransaction func(*sql.Selector)

// FuelTransactionOrderLink is the predicate function for fueltransactionorderlink builders.
type FuelTransactionOrderLink func(*sql.Selector)

// Order is the predicate function for order builders.
type Order func(*sql.Selector)
