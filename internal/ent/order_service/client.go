// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// FuelTransaction is the client for interacting with the FuelTransaction builders.
	FuelTransaction *FuelTransactionClient
	// FuelTransactionOrderLink is the client for interacting with the FuelTransactionOrderLink builders.
	FuelTransactionOrderLink *FuelTransactionOrderLinkClient
	// Order is the client for interacting with the Order builders.
	Order *OrderClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.FuelTransaction = NewFuelTransactionClient(c.config)
	c.FuelTransactionOrderLink = NewFuelTransactionOrderLinkClient(c.config)
	c.Order = NewOrderClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("order_service: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("order_service: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                      ctx,
		config:                   cfg,
		FuelTransaction:          NewFuelTransactionClient(cfg),
		FuelTransactionOrderLink: NewFuelTransactionOrderLinkClient(cfg),
		Order:                    NewOrderClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                      ctx,
		config:                   cfg,
		FuelTransaction:          NewFuelTransactionClient(cfg),
		FuelTransactionOrderLink: NewFuelTransactionOrderLinkClient(cfg),
		Order:                    NewOrderClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		FuelTransaction.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.FuelTransaction.Use(hooks...)
	c.FuelTransactionOrderLink.Use(hooks...)
	c.Order.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.FuelTransaction.Intercept(interceptors...)
	c.FuelTransactionOrderLink.Intercept(interceptors...)
	c.Order.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *FuelTransactionMutation:
		return c.FuelTransaction.mutate(ctx, m)
	case *FuelTransactionOrderLinkMutation:
		return c.FuelTransactionOrderLink.mutate(ctx, m)
	case *OrderMutation:
		return c.Order.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("order_service: unknown mutation type %T", m)
	}
}

// FuelTransactionClient is a client for the FuelTransaction schema.
type FuelTransactionClient struct {
	config
}

// NewFuelTransactionClient returns a client for the FuelTransaction from the given config.
func NewFuelTransactionClient(c config) *FuelTransactionClient {
	return &FuelTransactionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `fueltransaction.Hooks(f(g(h())))`.
func (c *FuelTransactionClient) Use(hooks ...Hook) {
	c.hooks.FuelTransaction = append(c.hooks.FuelTransaction, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `fueltransaction.Intercept(f(g(h())))`.
func (c *FuelTransactionClient) Intercept(interceptors ...Interceptor) {
	c.inters.FuelTransaction = append(c.inters.FuelTransaction, interceptors...)
}

// Create returns a builder for creating a FuelTransaction entity.
func (c *FuelTransactionClient) Create() *FuelTransactionCreate {
	mutation := newFuelTransactionMutation(c.config, OpCreate)
	return &FuelTransactionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of FuelTransaction entities.
func (c *FuelTransactionClient) CreateBulk(builders ...*FuelTransactionCreate) *FuelTransactionCreateBulk {
	return &FuelTransactionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FuelTransactionClient) MapCreateBulk(slice any, setFunc func(*FuelTransactionCreate, int)) *FuelTransactionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FuelTransactionCreateBulk{err: fmt.Errorf("calling to FuelTransactionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FuelTransactionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FuelTransactionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for FuelTransaction.
func (c *FuelTransactionClient) Update() *FuelTransactionUpdate {
	mutation := newFuelTransactionMutation(c.config, OpUpdate)
	return &FuelTransactionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FuelTransactionClient) UpdateOne(ft *FuelTransaction) *FuelTransactionUpdateOne {
	mutation := newFuelTransactionMutation(c.config, OpUpdateOne, withFuelTransaction(ft))
	return &FuelTransactionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FuelTransactionClient) UpdateOneID(id string) *FuelTransactionUpdateOne {
	mutation := newFuelTransactionMutation(c.config, OpUpdateOne, withFuelTransactionID(id))
	return &FuelTransactionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for FuelTransaction.
func (c *FuelTransactionClient) Delete() *FuelTransactionDelete {
	mutation := newFuelTransactionMutation(c.config, OpDelete)
	return &FuelTransactionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FuelTransactionClient) DeleteOne(ft *FuelTransaction) *FuelTransactionDeleteOne {
	return c.DeleteOneID(ft.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FuelTransactionClient) DeleteOneID(id string) *FuelTransactionDeleteOne {
	builder := c.Delete().Where(fueltransaction.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FuelTransactionDeleteOne{builder}
}

// Query returns a query builder for FuelTransaction.
func (c *FuelTransactionClient) Query() *FuelTransactionQuery {
	return &FuelTransactionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFuelTransaction},
		inters: c.Interceptors(),
	}
}

// Get returns a FuelTransaction entity by its id.
func (c *FuelTransactionClient) Get(ctx context.Context, id string) (*FuelTransaction, error) {
	return c.Query().Where(fueltransaction.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FuelTransactionClient) GetX(ctx context.Context, id string) *FuelTransaction {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryOrderLink queries the order_link edge of a FuelTransaction.
func (c *FuelTransactionClient) QueryOrderLink(ft *FuelTransaction) *FuelTransactionOrderLinkQuery {
	query := (&FuelTransactionOrderLinkClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ft.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(fueltransaction.Table, fueltransaction.FieldID, id),
			sqlgraph.To(fueltransactionorderlink.Table, fueltransactionorderlink.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, fueltransaction.OrderLinkTable, fueltransaction.OrderLinkColumn),
		)
		fromV = sqlgraph.Neighbors(ft.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *FuelTransactionClient) Hooks() []Hook {
	return c.hooks.FuelTransaction
}

// Interceptors returns the client interceptors.
func (c *FuelTransactionClient) Interceptors() []Interceptor {
	return c.inters.FuelTransaction
}

func (c *FuelTransactionClient) mutate(ctx context.Context, m *FuelTransactionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FuelTransactionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FuelTransactionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FuelTransactionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FuelTransactionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("order_service: unknown FuelTransaction mutation op: %q", m.Op())
	}
}

// FuelTransactionOrderLinkClient is a client for the FuelTransactionOrderLink schema.
type FuelTransactionOrderLinkClient struct {
	config
}

// NewFuelTransactionOrderLinkClient returns a client for the FuelTransactionOrderLink from the given config.
func NewFuelTransactionOrderLinkClient(c config) *FuelTransactionOrderLinkClient {
	return &FuelTransactionOrderLinkClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `fueltransactionorderlink.Hooks(f(g(h())))`.
func (c *FuelTransactionOrderLinkClient) Use(hooks ...Hook) {
	c.hooks.FuelTransactionOrderLink = append(c.hooks.FuelTransactionOrderLink, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `fueltransactionorderlink.Intercept(f(g(h())))`.
func (c *FuelTransactionOrderLinkClient) Intercept(interceptors ...Interceptor) {
	c.inters.FuelTransactionOrderLink = append(c.inters.FuelTransactionOrderLink, interceptors...)
}

// Create returns a builder for creating a FuelTransactionOrderLink entity.
func (c *FuelTransactionOrderLinkClient) Create() *FuelTransactionOrderLinkCreate {
	mutation := newFuelTransactionOrderLinkMutation(c.config, OpCreate)
	return &FuelTransactionOrderLinkCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of FuelTransactionOrderLink entities.
func (c *FuelTransactionOrderLinkClient) CreateBulk(builders ...*FuelTransactionOrderLinkCreate) *FuelTransactionOrderLinkCreateBulk {
	return &FuelTransactionOrderLinkCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FuelTransactionOrderLinkClient) MapCreateBulk(slice any, setFunc func(*FuelTransactionOrderLinkCreate, int)) *FuelTransactionOrderLinkCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FuelTransactionOrderLinkCreateBulk{err: fmt.Errorf("calling to FuelTransactionOrderLinkClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FuelTransactionOrderLinkCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FuelTransactionOrderLinkCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for FuelTransactionOrderLink.
func (c *FuelTransactionOrderLinkClient) Update() *FuelTransactionOrderLinkUpdate {
	mutation := newFuelTransactionOrderLinkMutation(c.config, OpUpdate)
	return &FuelTransactionOrderLinkUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FuelTransactionOrderLinkClient) UpdateOne(ftol *FuelTransactionOrderLink) *FuelTransactionOrderLinkUpdateOne {
	mutation := newFuelTransactionOrderLinkMutation(c.config, OpUpdateOne, withFuelTransactionOrderLink(ftol))
	return &FuelTransactionOrderLinkUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FuelTransactionOrderLinkClient) UpdateOneID(id string) *FuelTransactionOrderLinkUpdateOne {
	mutation := newFuelTransactionOrderLinkMutation(c.config, OpUpdateOne, withFuelTransactionOrderLinkID(id))
	return &FuelTransactionOrderLinkUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for FuelTransactionOrderLink.
func (c *FuelTransactionOrderLinkClient) Delete() *FuelTransactionOrderLinkDelete {
	mutation := newFuelTransactionOrderLinkMutation(c.config, OpDelete)
	return &FuelTransactionOrderLinkDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FuelTransactionOrderLinkClient) DeleteOne(ftol *FuelTransactionOrderLink) *FuelTransactionOrderLinkDeleteOne {
	return c.DeleteOneID(ftol.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FuelTransactionOrderLinkClient) DeleteOneID(id string) *FuelTransactionOrderLinkDeleteOne {
	builder := c.Delete().Where(fueltransactionorderlink.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FuelTransactionOrderLinkDeleteOne{builder}
}

// Query returns a query builder for FuelTransactionOrderLink.
func (c *FuelTransactionOrderLinkClient) Query() *FuelTransactionOrderLinkQuery {
	return &FuelTransactionOrderLinkQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFuelTransactionOrderLink},
		inters: c.Interceptors(),
	}
}

// Get returns a FuelTransactionOrderLink entity by its id.
func (c *FuelTransactionOrderLinkClient) Get(ctx context.Context, id string) (*FuelTransactionOrderLink, error) {
	return c.Query().Where(fueltransactionorderlink.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FuelTransactionOrderLinkClient) GetX(ctx context.Context, id string) *FuelTransactionOrderLink {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryFuelTransaction queries the fuel_transaction edge of a FuelTransactionOrderLink.
func (c *FuelTransactionOrderLinkClient) QueryFuelTransaction(ftol *FuelTransactionOrderLink) *FuelTransactionQuery {
	query := (&FuelTransactionClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ftol.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(fueltransactionorderlink.Table, fueltransactionorderlink.FieldID, id),
			sqlgraph.To(fueltransaction.Table, fueltransaction.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, fueltransactionorderlink.FuelTransactionTable, fueltransactionorderlink.FuelTransactionColumn),
		)
		fromV = sqlgraph.Neighbors(ftol.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryOrder queries the order edge of a FuelTransactionOrderLink.
func (c *FuelTransactionOrderLinkClient) QueryOrder(ftol *FuelTransactionOrderLink) *OrderQuery {
	query := (&OrderClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ftol.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(fueltransactionorderlink.Table, fueltransactionorderlink.FieldID, id),
			sqlgraph.To(order.Table, order.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, fueltransactionorderlink.OrderTable, fueltransactionorderlink.OrderColumn),
		)
		fromV = sqlgraph.Neighbors(ftol.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *FuelTransactionOrderLinkClient) Hooks() []Hook {
	return c.hooks.FuelTransactionOrderLink
}

// Interceptors returns the client interceptors.
func (c *FuelTransactionOrderLinkClient) Interceptors() []Interceptor {
	return c.inters.FuelTransactionOrderLink
}

func (c *FuelTransactionOrderLinkClient) mutate(ctx context.Context, m *FuelTransactionOrderLinkMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FuelTransactionOrderLinkCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FuelTransactionOrderLinkUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FuelTransactionOrderLinkUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FuelTransactionOrderLinkDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("order_service: unknown FuelTransactionOrderLink mutation op: %q", m.Op())
	}
}

// OrderClient is a client for the Order schema.
type OrderClient struct {
	config
}

// NewOrderClient returns a client for the Order from the given config.
func NewOrderClient(c config) *OrderClient {
	return &OrderClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `order.Hooks(f(g(h())))`.
func (c *OrderClient) Use(hooks ...Hook) {
	c.hooks.Order = append(c.hooks.Order, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `order.Intercept(f(g(h())))`.
func (c *OrderClient) Intercept(interceptors ...Interceptor) {
	c.inters.Order = append(c.inters.Order, interceptors...)
}

// Create returns a builder for creating a Order entity.
func (c *OrderClient) Create() *OrderCreate {
	mutation := newOrderMutation(c.config, OpCreate)
	return &OrderCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Order entities.
func (c *OrderClient) CreateBulk(builders ...*OrderCreate) *OrderCreateBulk {
	return &OrderCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *OrderClient) MapCreateBulk(slice any, setFunc func(*OrderCreate, int)) *OrderCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &OrderCreateBulk{err: fmt.Errorf("calling to OrderClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*OrderCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &OrderCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Order.
func (c *OrderClient) Update() *OrderUpdate {
	mutation := newOrderMutation(c.config, OpUpdate)
	return &OrderUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *OrderClient) UpdateOne(o *Order) *OrderUpdateOne {
	mutation := newOrderMutation(c.config, OpUpdateOne, withOrder(o))
	return &OrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *OrderClient) UpdateOneID(id string) *OrderUpdateOne {
	mutation := newOrderMutation(c.config, OpUpdateOne, withOrderID(id))
	return &OrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Order.
func (c *OrderClient) Delete() *OrderDelete {
	mutation := newOrderMutation(c.config, OpDelete)
	return &OrderDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *OrderClient) DeleteOne(o *Order) *OrderDeleteOne {
	return c.DeleteOneID(o.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *OrderClient) DeleteOneID(id string) *OrderDeleteOne {
	builder := c.Delete().Where(order.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &OrderDeleteOne{builder}
}

// Query returns a query builder for Order.
func (c *OrderClient) Query() *OrderQuery {
	return &OrderQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeOrder},
		inters: c.Interceptors(),
	}
}

// Get returns a Order entity by its id.
func (c *OrderClient) Get(ctx context.Context, id string) (*Order, error) {
	return c.Query().Where(order.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *OrderClient) GetX(ctx context.Context, id string) *Order {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryFuelTransactionLink queries the fuel_transaction_link edge of a Order.
func (c *OrderClient) QueryFuelTransactionLink(o *Order) *FuelTransactionOrderLinkQuery {
	query := (&FuelTransactionOrderLinkClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(order.Table, order.FieldID, id),
			sqlgraph.To(fueltransactionorderlink.Table, fueltransactionorderlink.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, order.FuelTransactionLinkTable, order.FuelTransactionLinkColumn),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *OrderClient) Hooks() []Hook {
	return c.hooks.Order
}

// Interceptors returns the client interceptors.
func (c *OrderClient) Interceptors() []Interceptor {
	return c.inters.Order
}

func (c *OrderClient) mutate(ctx context.Context, m *OrderMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&OrderCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&OrderUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&OrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&OrderDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("order_service: unknown Order mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		FuelTransaction, FuelTransactionOrderLink, Order []ent.Hook
	}
	inters struct {
		FuelTransaction, FuelTransactionOrderLink, Order []ent.Interceptor
	}
)
