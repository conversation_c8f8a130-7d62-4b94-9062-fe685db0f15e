// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
)

// Order is the model entity for the Order schema.
type Order struct {
	config `json:"-"`
	// ID of the ent.
	// 主键ID
	ID string `json:"id,omitempty"`
	// 订单编号
	OrderNumber string `json:"order_number,omitempty"`
	// 客户ID
	CustomerID *int64 `json:"customer_id,omitempty"`
	// 客户手机号
	CustomerPhone *string `json:"customer_phone,omitempty"`
	// 车牌号
	LicensePlate *string `json:"license_plate,omitempty"`
	// 客户姓名
	CustomerName *string `json:"customer_name,omitempty"`
	// 加油站ID
	StationID int64 `json:"station_id,omitempty"`
	// 订单状态
	Status string `json:"status,omitempty"`
	// 总金额
	TotalAmount float64 `json:"total_amount,omitempty"`
	// 折扣金额
	DiscountAmount float64 `json:"discount_amount,omitempty"`
	// 最终金额
	FinalAmount float64 `json:"final_amount,omitempty"`
	// 税费金额
	TaxAmount float64 `json:"tax_amount,omitempty"`
	// 已支付金额
	PaidAmount float64 `json:"paid_amount,omitempty"`
	// 元数据
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 完成时间
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	// 取消时间
	CancelledAt *time.Time `json:"cancelled_at,omitempty"`
	// 员工编号
	EmployeeNo *string `json:"employee_no,omitempty"`
	// 员工卡ID
	StaffCardID *int64 `json:"staff_card_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the OrderQuery when eager-loading is set.
	Edges        OrderEdges `json:"edges"`
	selectValues sql.SelectValues
}

// OrderEdges holds the relations/edges for other nodes in the graph.
type OrderEdges struct {
	// FuelTransactionLink holds the value of the fuel_transaction_link edge.
	FuelTransactionLink *FuelTransactionOrderLink `json:"fuel_transaction_link,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// FuelTransactionLinkOrErr returns the FuelTransactionLink value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e OrderEdges) FuelTransactionLinkOrErr() (*FuelTransactionOrderLink, error) {
	if e.FuelTransactionLink != nil {
		return e.FuelTransactionLink, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: fueltransactionorderlink.Label}
	}
	return nil, &NotLoadedError{edge: "fuel_transaction_link"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Order) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case order.FieldMetadata:
			values[i] = new([]byte)
		case order.FieldTotalAmount, order.FieldDiscountAmount, order.FieldFinalAmount, order.FieldTaxAmount, order.FieldPaidAmount:
			values[i] = new(sql.NullFloat64)
		case order.FieldCustomerID, order.FieldStationID, order.FieldStaffCardID:
			values[i] = new(sql.NullInt64)
		case order.FieldID, order.FieldOrderNumber, order.FieldCustomerPhone, order.FieldLicensePlate, order.FieldCustomerName, order.FieldStatus, order.FieldEmployeeNo:
			values[i] = new(sql.NullString)
		case order.FieldCreatedAt, order.FieldUpdatedAt, order.FieldCompletedAt, order.FieldCancelledAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Order fields.
func (o *Order) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case order.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				o.ID = value.String
			}
		case order.FieldOrderNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field order_number", values[i])
			} else if value.Valid {
				o.OrderNumber = value.String
			}
		case order.FieldCustomerID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field customer_id", values[i])
			} else if value.Valid {
				o.CustomerID = new(int64)
				*o.CustomerID = value.Int64
			}
		case order.FieldCustomerPhone:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field customer_phone", values[i])
			} else if value.Valid {
				o.CustomerPhone = new(string)
				*o.CustomerPhone = value.String
			}
		case order.FieldLicensePlate:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field license_plate", values[i])
			} else if value.Valid {
				o.LicensePlate = new(string)
				*o.LicensePlate = value.String
			}
		case order.FieldCustomerName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field customer_name", values[i])
			} else if value.Valid {
				o.CustomerName = new(string)
				*o.CustomerName = value.String
			}
		case order.FieldStationID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field station_id", values[i])
			} else if value.Valid {
				o.StationID = value.Int64
			}
		case order.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				o.Status = value.String
			}
		case order.FieldTotalAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field total_amount", values[i])
			} else if value.Valid {
				o.TotalAmount = value.Float64
			}
		case order.FieldDiscountAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field discount_amount", values[i])
			} else if value.Valid {
				o.DiscountAmount = value.Float64
			}
		case order.FieldFinalAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field final_amount", values[i])
			} else if value.Valid {
				o.FinalAmount = value.Float64
			}
		case order.FieldTaxAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field tax_amount", values[i])
			} else if value.Valid {
				o.TaxAmount = value.Float64
			}
		case order.FieldPaidAmount:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field paid_amount", values[i])
			} else if value.Valid {
				o.PaidAmount = value.Float64
			}
		case order.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &o.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		case order.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				o.CreatedAt = value.Time
			}
		case order.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				o.UpdatedAt = value.Time
			}
		case order.FieldCompletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field completed_at", values[i])
			} else if value.Valid {
				o.CompletedAt = new(time.Time)
				*o.CompletedAt = value.Time
			}
		case order.FieldCancelledAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field cancelled_at", values[i])
			} else if value.Valid {
				o.CancelledAt = new(time.Time)
				*o.CancelledAt = value.Time
			}
		case order.FieldEmployeeNo:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field employee_no", values[i])
			} else if value.Valid {
				o.EmployeeNo = new(string)
				*o.EmployeeNo = value.String
			}
		case order.FieldStaffCardID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field staff_card_id", values[i])
			} else if value.Valid {
				o.StaffCardID = new(int64)
				*o.StaffCardID = value.Int64
			}
		default:
			o.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Order.
// This includes values selected through modifiers, order, etc.
func (o *Order) Value(name string) (ent.Value, error) {
	return o.selectValues.Get(name)
}

// QueryFuelTransactionLink queries the "fuel_transaction_link" edge of the Order entity.
func (o *Order) QueryFuelTransactionLink() *FuelTransactionOrderLinkQuery {
	return NewOrderClient(o.config).QueryFuelTransactionLink(o)
}

// Update returns a builder for updating this Order.
// Note that you need to call Order.Unwrap() before calling this method if this Order
// was returned from a transaction, and the transaction was committed or rolled back.
func (o *Order) Update() *OrderUpdateOne {
	return NewOrderClient(o.config).UpdateOne(o)
}

// Unwrap unwraps the Order entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (o *Order) Unwrap() *Order {
	_tx, ok := o.config.driver.(*txDriver)
	if !ok {
		panic("order_service: Order is not a transactional entity")
	}
	o.config.driver = _tx.drv
	return o
}

// String implements the fmt.Stringer.
func (o *Order) String() string {
	var builder strings.Builder
	builder.WriteString("Order(")
	builder.WriteString(fmt.Sprintf("id=%v, ", o.ID))
	builder.WriteString("order_number=")
	builder.WriteString(o.OrderNumber)
	builder.WriteString(", ")
	if v := o.CustomerID; v != nil {
		builder.WriteString("customer_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := o.CustomerPhone; v != nil {
		builder.WriteString("customer_phone=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := o.LicensePlate; v != nil {
		builder.WriteString("license_plate=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := o.CustomerName; v != nil {
		builder.WriteString("customer_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("station_id=")
	builder.WriteString(fmt.Sprintf("%v", o.StationID))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(o.Status)
	builder.WriteString(", ")
	builder.WriteString("total_amount=")
	builder.WriteString(fmt.Sprintf("%v", o.TotalAmount))
	builder.WriteString(", ")
	builder.WriteString("discount_amount=")
	builder.WriteString(fmt.Sprintf("%v", o.DiscountAmount))
	builder.WriteString(", ")
	builder.WriteString("final_amount=")
	builder.WriteString(fmt.Sprintf("%v", o.FinalAmount))
	builder.WriteString(", ")
	builder.WriteString("tax_amount=")
	builder.WriteString(fmt.Sprintf("%v", o.TaxAmount))
	builder.WriteString(", ")
	builder.WriteString("paid_amount=")
	builder.WriteString(fmt.Sprintf("%v", o.PaidAmount))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", o.Metadata))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(o.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(o.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := o.CompletedAt; v != nil {
		builder.WriteString("completed_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := o.CancelledAt; v != nil {
		builder.WriteString("cancelled_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	if v := o.EmployeeNo; v != nil {
		builder.WriteString("employee_no=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := o.StaffCardID; v != nil {
		builder.WriteString("staff_card_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteByte(')')
	return builder.String()
}

// Orders is a parsable slice of Order.
type Orders []*Order
