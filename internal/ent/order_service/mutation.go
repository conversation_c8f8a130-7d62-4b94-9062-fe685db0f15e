// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeFuelTransaction          = "FuelTransaction"
	TypeFuelTransactionOrderLink = "FuelTransactionOrderLink"
	TypeOrder                    = "Order"
)

// FuelTransactionMutation represents an operation that mutates the FuelTransaction nodes in the graph.
type FuelTransactionMutation struct {
	config
	op                 Op
	typ                string
	id                 *string
	transaction_number *string
	station_id         *int64
	addstation_id      *int64
	pump_id            *string
	nozzle_id          *string
	fuel_type          *string
	fuel_grade         *string
	tank               *int
	addtank            *int
	unit_price         *float64
	addunit_price      *float64
	volume             *float64
	addvolume          *float64
	amount             *float64
	addamount          *float64
	total_volume       *float64
	addtotal_volume    *float64
	total_amount       *float64
	addtotal_amount    *float64
	status             *string
	member_card_id     *string
	member_id          *string
	employee_id        *string
	fcc_transaction_id *string
	pos_terminal_id    *string
	metadata           *map[string]interface{}
	created_at         *time.Time
	updated_at         *time.Time
	processed_at       *time.Time
	cancelled_at       *time.Time
	start_totalizer    *float64
	addstart_totalizer *float64
	end_totalizer      *float64
	addend_totalizer   *float64
	nozzle_start_time  *time.Time
	nozzle_end_time    *time.Time
	staff_card_id      *string
	clearedFields      map[string]struct{}
	order_link         *string
	clearedorder_link  bool
	done               bool
	oldValue           func(context.Context) (*FuelTransaction, error)
	predicates         []predicate.FuelTransaction
}

var _ ent.Mutation = (*FuelTransactionMutation)(nil)

// fueltransactionOption allows management of the mutation configuration using functional options.
type fueltransactionOption func(*FuelTransactionMutation)

// newFuelTransactionMutation creates new mutation for the FuelTransaction entity.
func newFuelTransactionMutation(c config, op Op, opts ...fueltransactionOption) *FuelTransactionMutation {
	m := &FuelTransactionMutation{
		config:        c,
		op:            op,
		typ:           TypeFuelTransaction,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withFuelTransactionID sets the ID field of the mutation.
func withFuelTransactionID(id string) fueltransactionOption {
	return func(m *FuelTransactionMutation) {
		var (
			err   error
			once  sync.Once
			value *FuelTransaction
		)
		m.oldValue = func(ctx context.Context) (*FuelTransaction, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().FuelTransaction.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withFuelTransaction sets the old FuelTransaction of the mutation.
func withFuelTransaction(node *FuelTransaction) fueltransactionOption {
	return func(m *FuelTransactionMutation) {
		m.oldValue = func(context.Context) (*FuelTransaction, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m FuelTransactionMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m FuelTransactionMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("order_service: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of FuelTransaction entities.
func (m *FuelTransactionMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *FuelTransactionMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *FuelTransactionMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().FuelTransaction.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetTransactionNumber sets the "transaction_number" field.
func (m *FuelTransactionMutation) SetTransactionNumber(s string) {
	m.transaction_number = &s
}

// TransactionNumber returns the value of the "transaction_number" field in the mutation.
func (m *FuelTransactionMutation) TransactionNumber() (r string, exists bool) {
	v := m.transaction_number
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactionNumber returns the old "transaction_number" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldTransactionNumber(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactionNumber is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactionNumber requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactionNumber: %w", err)
	}
	return oldValue.TransactionNumber, nil
}

// ResetTransactionNumber resets all changes to the "transaction_number" field.
func (m *FuelTransactionMutation) ResetTransactionNumber() {
	m.transaction_number = nil
}

// SetStationID sets the "station_id" field.
func (m *FuelTransactionMutation) SetStationID(i int64) {
	m.station_id = &i
	m.addstation_id = nil
}

// StationID returns the value of the "station_id" field in the mutation.
func (m *FuelTransactionMutation) StationID() (r int64, exists bool) {
	v := m.station_id
	if v == nil {
		return
	}
	return *v, true
}

// OldStationID returns the old "station_id" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldStationID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStationID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStationID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStationID: %w", err)
	}
	return oldValue.StationID, nil
}

// AddStationID adds i to the "station_id" field.
func (m *FuelTransactionMutation) AddStationID(i int64) {
	if m.addstation_id != nil {
		*m.addstation_id += i
	} else {
		m.addstation_id = &i
	}
}

// AddedStationID returns the value that was added to the "station_id" field in this mutation.
func (m *FuelTransactionMutation) AddedStationID() (r int64, exists bool) {
	v := m.addstation_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetStationID resets all changes to the "station_id" field.
func (m *FuelTransactionMutation) ResetStationID() {
	m.station_id = nil
	m.addstation_id = nil
}

// SetPumpID sets the "pump_id" field.
func (m *FuelTransactionMutation) SetPumpID(s string) {
	m.pump_id = &s
}

// PumpID returns the value of the "pump_id" field in the mutation.
func (m *FuelTransactionMutation) PumpID() (r string, exists bool) {
	v := m.pump_id
	if v == nil {
		return
	}
	return *v, true
}

// OldPumpID returns the old "pump_id" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldPumpID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPumpID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPumpID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPumpID: %w", err)
	}
	return oldValue.PumpID, nil
}

// ResetPumpID resets all changes to the "pump_id" field.
func (m *FuelTransactionMutation) ResetPumpID() {
	m.pump_id = nil
}

// SetNozzleID sets the "nozzle_id" field.
func (m *FuelTransactionMutation) SetNozzleID(s string) {
	m.nozzle_id = &s
}

// NozzleID returns the value of the "nozzle_id" field in the mutation.
func (m *FuelTransactionMutation) NozzleID() (r string, exists bool) {
	v := m.nozzle_id
	if v == nil {
		return
	}
	return *v, true
}

// OldNozzleID returns the old "nozzle_id" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldNozzleID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNozzleID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNozzleID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNozzleID: %w", err)
	}
	return oldValue.NozzleID, nil
}

// ResetNozzleID resets all changes to the "nozzle_id" field.
func (m *FuelTransactionMutation) ResetNozzleID() {
	m.nozzle_id = nil
}

// SetFuelType sets the "fuel_type" field.
func (m *FuelTransactionMutation) SetFuelType(s string) {
	m.fuel_type = &s
}

// FuelType returns the value of the "fuel_type" field in the mutation.
func (m *FuelTransactionMutation) FuelType() (r string, exists bool) {
	v := m.fuel_type
	if v == nil {
		return
	}
	return *v, true
}

// OldFuelType returns the old "fuel_type" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldFuelType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFuelType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFuelType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFuelType: %w", err)
	}
	return oldValue.FuelType, nil
}

// ResetFuelType resets all changes to the "fuel_type" field.
func (m *FuelTransactionMutation) ResetFuelType() {
	m.fuel_type = nil
}

// SetFuelGrade sets the "fuel_grade" field.
func (m *FuelTransactionMutation) SetFuelGrade(s string) {
	m.fuel_grade = &s
}

// FuelGrade returns the value of the "fuel_grade" field in the mutation.
func (m *FuelTransactionMutation) FuelGrade() (r string, exists bool) {
	v := m.fuel_grade
	if v == nil {
		return
	}
	return *v, true
}

// OldFuelGrade returns the old "fuel_grade" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldFuelGrade(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFuelGrade is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFuelGrade requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFuelGrade: %w", err)
	}
	return oldValue.FuelGrade, nil
}

// ResetFuelGrade resets all changes to the "fuel_grade" field.
func (m *FuelTransactionMutation) ResetFuelGrade() {
	m.fuel_grade = nil
}

// SetTank sets the "tank" field.
func (m *FuelTransactionMutation) SetTank(i int) {
	m.tank = &i
	m.addtank = nil
}

// Tank returns the value of the "tank" field in the mutation.
func (m *FuelTransactionMutation) Tank() (r int, exists bool) {
	v := m.tank
	if v == nil {
		return
	}
	return *v, true
}

// OldTank returns the old "tank" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldTank(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTank is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTank requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTank: %w", err)
	}
	return oldValue.Tank, nil
}

// AddTank adds i to the "tank" field.
func (m *FuelTransactionMutation) AddTank(i int) {
	if m.addtank != nil {
		*m.addtank += i
	} else {
		m.addtank = &i
	}
}

// AddedTank returns the value that was added to the "tank" field in this mutation.
func (m *FuelTransactionMutation) AddedTank() (r int, exists bool) {
	v := m.addtank
	if v == nil {
		return
	}
	return *v, true
}

// ResetTank resets all changes to the "tank" field.
func (m *FuelTransactionMutation) ResetTank() {
	m.tank = nil
	m.addtank = nil
}

// SetUnitPrice sets the "unit_price" field.
func (m *FuelTransactionMutation) SetUnitPrice(f float64) {
	m.unit_price = &f
	m.addunit_price = nil
}

// UnitPrice returns the value of the "unit_price" field in the mutation.
func (m *FuelTransactionMutation) UnitPrice() (r float64, exists bool) {
	v := m.unit_price
	if v == nil {
		return
	}
	return *v, true
}

// OldUnitPrice returns the old "unit_price" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldUnitPrice(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUnitPrice is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUnitPrice requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUnitPrice: %w", err)
	}
	return oldValue.UnitPrice, nil
}

// AddUnitPrice adds f to the "unit_price" field.
func (m *FuelTransactionMutation) AddUnitPrice(f float64) {
	if m.addunit_price != nil {
		*m.addunit_price += f
	} else {
		m.addunit_price = &f
	}
}

// AddedUnitPrice returns the value that was added to the "unit_price" field in this mutation.
func (m *FuelTransactionMutation) AddedUnitPrice() (r float64, exists bool) {
	v := m.addunit_price
	if v == nil {
		return
	}
	return *v, true
}

// ResetUnitPrice resets all changes to the "unit_price" field.
func (m *FuelTransactionMutation) ResetUnitPrice() {
	m.unit_price = nil
	m.addunit_price = nil
}

// SetVolume sets the "volume" field.
func (m *FuelTransactionMutation) SetVolume(f float64) {
	m.volume = &f
	m.addvolume = nil
}

// Volume returns the value of the "volume" field in the mutation.
func (m *FuelTransactionMutation) Volume() (r float64, exists bool) {
	v := m.volume
	if v == nil {
		return
	}
	return *v, true
}

// OldVolume returns the old "volume" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldVolume(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVolume is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVolume requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVolume: %w", err)
	}
	return oldValue.Volume, nil
}

// AddVolume adds f to the "volume" field.
func (m *FuelTransactionMutation) AddVolume(f float64) {
	if m.addvolume != nil {
		*m.addvolume += f
	} else {
		m.addvolume = &f
	}
}

// AddedVolume returns the value that was added to the "volume" field in this mutation.
func (m *FuelTransactionMutation) AddedVolume() (r float64, exists bool) {
	v := m.addvolume
	if v == nil {
		return
	}
	return *v, true
}

// ResetVolume resets all changes to the "volume" field.
func (m *FuelTransactionMutation) ResetVolume() {
	m.volume = nil
	m.addvolume = nil
}

// SetAmount sets the "amount" field.
func (m *FuelTransactionMutation) SetAmount(f float64) {
	m.amount = &f
	m.addamount = nil
}

// Amount returns the value of the "amount" field in the mutation.
func (m *FuelTransactionMutation) Amount() (r float64, exists bool) {
	v := m.amount
	if v == nil {
		return
	}
	return *v, true
}

// OldAmount returns the old "amount" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldAmount(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAmount: %w", err)
	}
	return oldValue.Amount, nil
}

// AddAmount adds f to the "amount" field.
func (m *FuelTransactionMutation) AddAmount(f float64) {
	if m.addamount != nil {
		*m.addamount += f
	} else {
		m.addamount = &f
	}
}

// AddedAmount returns the value that was added to the "amount" field in this mutation.
func (m *FuelTransactionMutation) AddedAmount() (r float64, exists bool) {
	v := m.addamount
	if v == nil {
		return
	}
	return *v, true
}

// ResetAmount resets all changes to the "amount" field.
func (m *FuelTransactionMutation) ResetAmount() {
	m.amount = nil
	m.addamount = nil
}

// SetTotalVolume sets the "total_volume" field.
func (m *FuelTransactionMutation) SetTotalVolume(f float64) {
	m.total_volume = &f
	m.addtotal_volume = nil
}

// TotalVolume returns the value of the "total_volume" field in the mutation.
func (m *FuelTransactionMutation) TotalVolume() (r float64, exists bool) {
	v := m.total_volume
	if v == nil {
		return
	}
	return *v, true
}

// OldTotalVolume returns the old "total_volume" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldTotalVolume(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTotalVolume is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTotalVolume requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTotalVolume: %w", err)
	}
	return oldValue.TotalVolume, nil
}

// AddTotalVolume adds f to the "total_volume" field.
func (m *FuelTransactionMutation) AddTotalVolume(f float64) {
	if m.addtotal_volume != nil {
		*m.addtotal_volume += f
	} else {
		m.addtotal_volume = &f
	}
}

// AddedTotalVolume returns the value that was added to the "total_volume" field in this mutation.
func (m *FuelTransactionMutation) AddedTotalVolume() (r float64, exists bool) {
	v := m.addtotal_volume
	if v == nil {
		return
	}
	return *v, true
}

// ResetTotalVolume resets all changes to the "total_volume" field.
func (m *FuelTransactionMutation) ResetTotalVolume() {
	m.total_volume = nil
	m.addtotal_volume = nil
}

// SetTotalAmount sets the "total_amount" field.
func (m *FuelTransactionMutation) SetTotalAmount(f float64) {
	m.total_amount = &f
	m.addtotal_amount = nil
}

// TotalAmount returns the value of the "total_amount" field in the mutation.
func (m *FuelTransactionMutation) TotalAmount() (r float64, exists bool) {
	v := m.total_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldTotalAmount returns the old "total_amount" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldTotalAmount(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTotalAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTotalAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTotalAmount: %w", err)
	}
	return oldValue.TotalAmount, nil
}

// AddTotalAmount adds f to the "total_amount" field.
func (m *FuelTransactionMutation) AddTotalAmount(f float64) {
	if m.addtotal_amount != nil {
		*m.addtotal_amount += f
	} else {
		m.addtotal_amount = &f
	}
}

// AddedTotalAmount returns the value that was added to the "total_amount" field in this mutation.
func (m *FuelTransactionMutation) AddedTotalAmount() (r float64, exists bool) {
	v := m.addtotal_amount
	if v == nil {
		return
	}
	return *v, true
}

// ResetTotalAmount resets all changes to the "total_amount" field.
func (m *FuelTransactionMutation) ResetTotalAmount() {
	m.total_amount = nil
	m.addtotal_amount = nil
}

// SetStatus sets the "status" field.
func (m *FuelTransactionMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *FuelTransactionMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *FuelTransactionMutation) ResetStatus() {
	m.status = nil
}

// SetMemberCardID sets the "member_card_id" field.
func (m *FuelTransactionMutation) SetMemberCardID(s string) {
	m.member_card_id = &s
}

// MemberCardID returns the value of the "member_card_id" field in the mutation.
func (m *FuelTransactionMutation) MemberCardID() (r string, exists bool) {
	v := m.member_card_id
	if v == nil {
		return
	}
	return *v, true
}

// OldMemberCardID returns the old "member_card_id" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldMemberCardID(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMemberCardID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMemberCardID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMemberCardID: %w", err)
	}
	return oldValue.MemberCardID, nil
}

// ClearMemberCardID clears the value of the "member_card_id" field.
func (m *FuelTransactionMutation) ClearMemberCardID() {
	m.member_card_id = nil
	m.clearedFields[fueltransaction.FieldMemberCardID] = struct{}{}
}

// MemberCardIDCleared returns if the "member_card_id" field was cleared in this mutation.
func (m *FuelTransactionMutation) MemberCardIDCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldMemberCardID]
	return ok
}

// ResetMemberCardID resets all changes to the "member_card_id" field.
func (m *FuelTransactionMutation) ResetMemberCardID() {
	m.member_card_id = nil
	delete(m.clearedFields, fueltransaction.FieldMemberCardID)
}

// SetMemberID sets the "member_id" field.
func (m *FuelTransactionMutation) SetMemberID(s string) {
	m.member_id = &s
}

// MemberID returns the value of the "member_id" field in the mutation.
func (m *FuelTransactionMutation) MemberID() (r string, exists bool) {
	v := m.member_id
	if v == nil {
		return
	}
	return *v, true
}

// OldMemberID returns the old "member_id" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldMemberID(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMemberID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMemberID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMemberID: %w", err)
	}
	return oldValue.MemberID, nil
}

// ClearMemberID clears the value of the "member_id" field.
func (m *FuelTransactionMutation) ClearMemberID() {
	m.member_id = nil
	m.clearedFields[fueltransaction.FieldMemberID] = struct{}{}
}

// MemberIDCleared returns if the "member_id" field was cleared in this mutation.
func (m *FuelTransactionMutation) MemberIDCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldMemberID]
	return ok
}

// ResetMemberID resets all changes to the "member_id" field.
func (m *FuelTransactionMutation) ResetMemberID() {
	m.member_id = nil
	delete(m.clearedFields, fueltransaction.FieldMemberID)
}

// SetEmployeeID sets the "employee_id" field.
func (m *FuelTransactionMutation) SetEmployeeID(s string) {
	m.employee_id = &s
}

// EmployeeID returns the value of the "employee_id" field in the mutation.
func (m *FuelTransactionMutation) EmployeeID() (r string, exists bool) {
	v := m.employee_id
	if v == nil {
		return
	}
	return *v, true
}

// OldEmployeeID returns the old "employee_id" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldEmployeeID(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmployeeID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmployeeID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmployeeID: %w", err)
	}
	return oldValue.EmployeeID, nil
}

// ClearEmployeeID clears the value of the "employee_id" field.
func (m *FuelTransactionMutation) ClearEmployeeID() {
	m.employee_id = nil
	m.clearedFields[fueltransaction.FieldEmployeeID] = struct{}{}
}

// EmployeeIDCleared returns if the "employee_id" field was cleared in this mutation.
func (m *FuelTransactionMutation) EmployeeIDCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldEmployeeID]
	return ok
}

// ResetEmployeeID resets all changes to the "employee_id" field.
func (m *FuelTransactionMutation) ResetEmployeeID() {
	m.employee_id = nil
	delete(m.clearedFields, fueltransaction.FieldEmployeeID)
}

// SetFccTransactionID sets the "fcc_transaction_id" field.
func (m *FuelTransactionMutation) SetFccTransactionID(s string) {
	m.fcc_transaction_id = &s
}

// FccTransactionID returns the value of the "fcc_transaction_id" field in the mutation.
func (m *FuelTransactionMutation) FccTransactionID() (r string, exists bool) {
	v := m.fcc_transaction_id
	if v == nil {
		return
	}
	return *v, true
}

// OldFccTransactionID returns the old "fcc_transaction_id" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldFccTransactionID(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFccTransactionID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFccTransactionID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFccTransactionID: %w", err)
	}
	return oldValue.FccTransactionID, nil
}

// ClearFccTransactionID clears the value of the "fcc_transaction_id" field.
func (m *FuelTransactionMutation) ClearFccTransactionID() {
	m.fcc_transaction_id = nil
	m.clearedFields[fueltransaction.FieldFccTransactionID] = struct{}{}
}

// FccTransactionIDCleared returns if the "fcc_transaction_id" field was cleared in this mutation.
func (m *FuelTransactionMutation) FccTransactionIDCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldFccTransactionID]
	return ok
}

// ResetFccTransactionID resets all changes to the "fcc_transaction_id" field.
func (m *FuelTransactionMutation) ResetFccTransactionID() {
	m.fcc_transaction_id = nil
	delete(m.clearedFields, fueltransaction.FieldFccTransactionID)
}

// SetPosTerminalID sets the "pos_terminal_id" field.
func (m *FuelTransactionMutation) SetPosTerminalID(s string) {
	m.pos_terminal_id = &s
}

// PosTerminalID returns the value of the "pos_terminal_id" field in the mutation.
func (m *FuelTransactionMutation) PosTerminalID() (r string, exists bool) {
	v := m.pos_terminal_id
	if v == nil {
		return
	}
	return *v, true
}

// OldPosTerminalID returns the old "pos_terminal_id" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldPosTerminalID(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPosTerminalID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPosTerminalID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPosTerminalID: %w", err)
	}
	return oldValue.PosTerminalID, nil
}

// ClearPosTerminalID clears the value of the "pos_terminal_id" field.
func (m *FuelTransactionMutation) ClearPosTerminalID() {
	m.pos_terminal_id = nil
	m.clearedFields[fueltransaction.FieldPosTerminalID] = struct{}{}
}

// PosTerminalIDCleared returns if the "pos_terminal_id" field was cleared in this mutation.
func (m *FuelTransactionMutation) PosTerminalIDCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldPosTerminalID]
	return ok
}

// ResetPosTerminalID resets all changes to the "pos_terminal_id" field.
func (m *FuelTransactionMutation) ResetPosTerminalID() {
	m.pos_terminal_id = nil
	delete(m.clearedFields, fueltransaction.FieldPosTerminalID)
}

// SetMetadata sets the "metadata" field.
func (m *FuelTransactionMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *FuelTransactionMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *FuelTransactionMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[fueltransaction.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *FuelTransactionMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *FuelTransactionMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, fueltransaction.FieldMetadata)
}

// SetCreatedAt sets the "created_at" field.
func (m *FuelTransactionMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *FuelTransactionMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *FuelTransactionMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *FuelTransactionMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *FuelTransactionMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *FuelTransactionMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetProcessedAt sets the "processed_at" field.
func (m *FuelTransactionMutation) SetProcessedAt(t time.Time) {
	m.processed_at = &t
}

// ProcessedAt returns the value of the "processed_at" field in the mutation.
func (m *FuelTransactionMutation) ProcessedAt() (r time.Time, exists bool) {
	v := m.processed_at
	if v == nil {
		return
	}
	return *v, true
}

// OldProcessedAt returns the old "processed_at" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldProcessedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProcessedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProcessedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProcessedAt: %w", err)
	}
	return oldValue.ProcessedAt, nil
}

// ClearProcessedAt clears the value of the "processed_at" field.
func (m *FuelTransactionMutation) ClearProcessedAt() {
	m.processed_at = nil
	m.clearedFields[fueltransaction.FieldProcessedAt] = struct{}{}
}

// ProcessedAtCleared returns if the "processed_at" field was cleared in this mutation.
func (m *FuelTransactionMutation) ProcessedAtCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldProcessedAt]
	return ok
}

// ResetProcessedAt resets all changes to the "processed_at" field.
func (m *FuelTransactionMutation) ResetProcessedAt() {
	m.processed_at = nil
	delete(m.clearedFields, fueltransaction.FieldProcessedAt)
}

// SetCancelledAt sets the "cancelled_at" field.
func (m *FuelTransactionMutation) SetCancelledAt(t time.Time) {
	m.cancelled_at = &t
}

// CancelledAt returns the value of the "cancelled_at" field in the mutation.
func (m *FuelTransactionMutation) CancelledAt() (r time.Time, exists bool) {
	v := m.cancelled_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCancelledAt returns the old "cancelled_at" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldCancelledAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCancelledAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCancelledAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCancelledAt: %w", err)
	}
	return oldValue.CancelledAt, nil
}

// ClearCancelledAt clears the value of the "cancelled_at" field.
func (m *FuelTransactionMutation) ClearCancelledAt() {
	m.cancelled_at = nil
	m.clearedFields[fueltransaction.FieldCancelledAt] = struct{}{}
}

// CancelledAtCleared returns if the "cancelled_at" field was cleared in this mutation.
func (m *FuelTransactionMutation) CancelledAtCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldCancelledAt]
	return ok
}

// ResetCancelledAt resets all changes to the "cancelled_at" field.
func (m *FuelTransactionMutation) ResetCancelledAt() {
	m.cancelled_at = nil
	delete(m.clearedFields, fueltransaction.FieldCancelledAt)
}

// SetStartTotalizer sets the "start_totalizer" field.
func (m *FuelTransactionMutation) SetStartTotalizer(f float64) {
	m.start_totalizer = &f
	m.addstart_totalizer = nil
}

// StartTotalizer returns the value of the "start_totalizer" field in the mutation.
func (m *FuelTransactionMutation) StartTotalizer() (r float64, exists bool) {
	v := m.start_totalizer
	if v == nil {
		return
	}
	return *v, true
}

// OldStartTotalizer returns the old "start_totalizer" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldStartTotalizer(ctx context.Context) (v *float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStartTotalizer is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStartTotalizer requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStartTotalizer: %w", err)
	}
	return oldValue.StartTotalizer, nil
}

// AddStartTotalizer adds f to the "start_totalizer" field.
func (m *FuelTransactionMutation) AddStartTotalizer(f float64) {
	if m.addstart_totalizer != nil {
		*m.addstart_totalizer += f
	} else {
		m.addstart_totalizer = &f
	}
}

// AddedStartTotalizer returns the value that was added to the "start_totalizer" field in this mutation.
func (m *FuelTransactionMutation) AddedStartTotalizer() (r float64, exists bool) {
	v := m.addstart_totalizer
	if v == nil {
		return
	}
	return *v, true
}

// ClearStartTotalizer clears the value of the "start_totalizer" field.
func (m *FuelTransactionMutation) ClearStartTotalizer() {
	m.start_totalizer = nil
	m.addstart_totalizer = nil
	m.clearedFields[fueltransaction.FieldStartTotalizer] = struct{}{}
}

// StartTotalizerCleared returns if the "start_totalizer" field was cleared in this mutation.
func (m *FuelTransactionMutation) StartTotalizerCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldStartTotalizer]
	return ok
}

// ResetStartTotalizer resets all changes to the "start_totalizer" field.
func (m *FuelTransactionMutation) ResetStartTotalizer() {
	m.start_totalizer = nil
	m.addstart_totalizer = nil
	delete(m.clearedFields, fueltransaction.FieldStartTotalizer)
}

// SetEndTotalizer sets the "end_totalizer" field.
func (m *FuelTransactionMutation) SetEndTotalizer(f float64) {
	m.end_totalizer = &f
	m.addend_totalizer = nil
}

// EndTotalizer returns the value of the "end_totalizer" field in the mutation.
func (m *FuelTransactionMutation) EndTotalizer() (r float64, exists bool) {
	v := m.end_totalizer
	if v == nil {
		return
	}
	return *v, true
}

// OldEndTotalizer returns the old "end_totalizer" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldEndTotalizer(ctx context.Context) (v *float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEndTotalizer is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEndTotalizer requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEndTotalizer: %w", err)
	}
	return oldValue.EndTotalizer, nil
}

// AddEndTotalizer adds f to the "end_totalizer" field.
func (m *FuelTransactionMutation) AddEndTotalizer(f float64) {
	if m.addend_totalizer != nil {
		*m.addend_totalizer += f
	} else {
		m.addend_totalizer = &f
	}
}

// AddedEndTotalizer returns the value that was added to the "end_totalizer" field in this mutation.
func (m *FuelTransactionMutation) AddedEndTotalizer() (r float64, exists bool) {
	v := m.addend_totalizer
	if v == nil {
		return
	}
	return *v, true
}

// ClearEndTotalizer clears the value of the "end_totalizer" field.
func (m *FuelTransactionMutation) ClearEndTotalizer() {
	m.end_totalizer = nil
	m.addend_totalizer = nil
	m.clearedFields[fueltransaction.FieldEndTotalizer] = struct{}{}
}

// EndTotalizerCleared returns if the "end_totalizer" field was cleared in this mutation.
func (m *FuelTransactionMutation) EndTotalizerCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldEndTotalizer]
	return ok
}

// ResetEndTotalizer resets all changes to the "end_totalizer" field.
func (m *FuelTransactionMutation) ResetEndTotalizer() {
	m.end_totalizer = nil
	m.addend_totalizer = nil
	delete(m.clearedFields, fueltransaction.FieldEndTotalizer)
}

// SetNozzleStartTime sets the "nozzle_start_time" field.
func (m *FuelTransactionMutation) SetNozzleStartTime(t time.Time) {
	m.nozzle_start_time = &t
}

// NozzleStartTime returns the value of the "nozzle_start_time" field in the mutation.
func (m *FuelTransactionMutation) NozzleStartTime() (r time.Time, exists bool) {
	v := m.nozzle_start_time
	if v == nil {
		return
	}
	return *v, true
}

// OldNozzleStartTime returns the old "nozzle_start_time" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldNozzleStartTime(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNozzleStartTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNozzleStartTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNozzleStartTime: %w", err)
	}
	return oldValue.NozzleStartTime, nil
}

// ClearNozzleStartTime clears the value of the "nozzle_start_time" field.
func (m *FuelTransactionMutation) ClearNozzleStartTime() {
	m.nozzle_start_time = nil
	m.clearedFields[fueltransaction.FieldNozzleStartTime] = struct{}{}
}

// NozzleStartTimeCleared returns if the "nozzle_start_time" field was cleared in this mutation.
func (m *FuelTransactionMutation) NozzleStartTimeCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldNozzleStartTime]
	return ok
}

// ResetNozzleStartTime resets all changes to the "nozzle_start_time" field.
func (m *FuelTransactionMutation) ResetNozzleStartTime() {
	m.nozzle_start_time = nil
	delete(m.clearedFields, fueltransaction.FieldNozzleStartTime)
}

// SetNozzleEndTime sets the "nozzle_end_time" field.
func (m *FuelTransactionMutation) SetNozzleEndTime(t time.Time) {
	m.nozzle_end_time = &t
}

// NozzleEndTime returns the value of the "nozzle_end_time" field in the mutation.
func (m *FuelTransactionMutation) NozzleEndTime() (r time.Time, exists bool) {
	v := m.nozzle_end_time
	if v == nil {
		return
	}
	return *v, true
}

// OldNozzleEndTime returns the old "nozzle_end_time" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldNozzleEndTime(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNozzleEndTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNozzleEndTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNozzleEndTime: %w", err)
	}
	return oldValue.NozzleEndTime, nil
}

// ClearNozzleEndTime clears the value of the "nozzle_end_time" field.
func (m *FuelTransactionMutation) ClearNozzleEndTime() {
	m.nozzle_end_time = nil
	m.clearedFields[fueltransaction.FieldNozzleEndTime] = struct{}{}
}

// NozzleEndTimeCleared returns if the "nozzle_end_time" field was cleared in this mutation.
func (m *FuelTransactionMutation) NozzleEndTimeCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldNozzleEndTime]
	return ok
}

// ResetNozzleEndTime resets all changes to the "nozzle_end_time" field.
func (m *FuelTransactionMutation) ResetNozzleEndTime() {
	m.nozzle_end_time = nil
	delete(m.clearedFields, fueltransaction.FieldNozzleEndTime)
}

// SetStaffCardID sets the "staff_card_id" field.
func (m *FuelTransactionMutation) SetStaffCardID(s string) {
	m.staff_card_id = &s
}

// StaffCardID returns the value of the "staff_card_id" field in the mutation.
func (m *FuelTransactionMutation) StaffCardID() (r string, exists bool) {
	v := m.staff_card_id
	if v == nil {
		return
	}
	return *v, true
}

// OldStaffCardID returns the old "staff_card_id" field's value of the FuelTransaction entity.
// If the FuelTransaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionMutation) OldStaffCardID(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStaffCardID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStaffCardID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStaffCardID: %w", err)
	}
	return oldValue.StaffCardID, nil
}

// ClearStaffCardID clears the value of the "staff_card_id" field.
func (m *FuelTransactionMutation) ClearStaffCardID() {
	m.staff_card_id = nil
	m.clearedFields[fueltransaction.FieldStaffCardID] = struct{}{}
}

// StaffCardIDCleared returns if the "staff_card_id" field was cleared in this mutation.
func (m *FuelTransactionMutation) StaffCardIDCleared() bool {
	_, ok := m.clearedFields[fueltransaction.FieldStaffCardID]
	return ok
}

// ResetStaffCardID resets all changes to the "staff_card_id" field.
func (m *FuelTransactionMutation) ResetStaffCardID() {
	m.staff_card_id = nil
	delete(m.clearedFields, fueltransaction.FieldStaffCardID)
}

// SetOrderLinkID sets the "order_link" edge to the FuelTransactionOrderLink entity by id.
func (m *FuelTransactionMutation) SetOrderLinkID(id string) {
	m.order_link = &id
}

// ClearOrderLink clears the "order_link" edge to the FuelTransactionOrderLink entity.
func (m *FuelTransactionMutation) ClearOrderLink() {
	m.clearedorder_link = true
}

// OrderLinkCleared reports if the "order_link" edge to the FuelTransactionOrderLink entity was cleared.
func (m *FuelTransactionMutation) OrderLinkCleared() bool {
	return m.clearedorder_link
}

// OrderLinkID returns the "order_link" edge ID in the mutation.
func (m *FuelTransactionMutation) OrderLinkID() (id string, exists bool) {
	if m.order_link != nil {
		return *m.order_link, true
	}
	return
}

// OrderLinkIDs returns the "order_link" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// OrderLinkID instead. It exists only for internal usage by the builders.
func (m *FuelTransactionMutation) OrderLinkIDs() (ids []string) {
	if id := m.order_link; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetOrderLink resets all changes to the "order_link" edge.
func (m *FuelTransactionMutation) ResetOrderLink() {
	m.order_link = nil
	m.clearedorder_link = false
}

// Where appends a list predicates to the FuelTransactionMutation builder.
func (m *FuelTransactionMutation) Where(ps ...predicate.FuelTransaction) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the FuelTransactionMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *FuelTransactionMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.FuelTransaction, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *FuelTransactionMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *FuelTransactionMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (FuelTransaction).
func (m *FuelTransactionMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *FuelTransactionMutation) Fields() []string {
	fields := make([]string, 0, 28)
	if m.transaction_number != nil {
		fields = append(fields, fueltransaction.FieldTransactionNumber)
	}
	if m.station_id != nil {
		fields = append(fields, fueltransaction.FieldStationID)
	}
	if m.pump_id != nil {
		fields = append(fields, fueltransaction.FieldPumpID)
	}
	if m.nozzle_id != nil {
		fields = append(fields, fueltransaction.FieldNozzleID)
	}
	if m.fuel_type != nil {
		fields = append(fields, fueltransaction.FieldFuelType)
	}
	if m.fuel_grade != nil {
		fields = append(fields, fueltransaction.FieldFuelGrade)
	}
	if m.tank != nil {
		fields = append(fields, fueltransaction.FieldTank)
	}
	if m.unit_price != nil {
		fields = append(fields, fueltransaction.FieldUnitPrice)
	}
	if m.volume != nil {
		fields = append(fields, fueltransaction.FieldVolume)
	}
	if m.amount != nil {
		fields = append(fields, fueltransaction.FieldAmount)
	}
	if m.total_volume != nil {
		fields = append(fields, fueltransaction.FieldTotalVolume)
	}
	if m.total_amount != nil {
		fields = append(fields, fueltransaction.FieldTotalAmount)
	}
	if m.status != nil {
		fields = append(fields, fueltransaction.FieldStatus)
	}
	if m.member_card_id != nil {
		fields = append(fields, fueltransaction.FieldMemberCardID)
	}
	if m.member_id != nil {
		fields = append(fields, fueltransaction.FieldMemberID)
	}
	if m.employee_id != nil {
		fields = append(fields, fueltransaction.FieldEmployeeID)
	}
	if m.fcc_transaction_id != nil {
		fields = append(fields, fueltransaction.FieldFccTransactionID)
	}
	if m.pos_terminal_id != nil {
		fields = append(fields, fueltransaction.FieldPosTerminalID)
	}
	if m.metadata != nil {
		fields = append(fields, fueltransaction.FieldMetadata)
	}
	if m.created_at != nil {
		fields = append(fields, fueltransaction.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, fueltransaction.FieldUpdatedAt)
	}
	if m.processed_at != nil {
		fields = append(fields, fueltransaction.FieldProcessedAt)
	}
	if m.cancelled_at != nil {
		fields = append(fields, fueltransaction.FieldCancelledAt)
	}
	if m.start_totalizer != nil {
		fields = append(fields, fueltransaction.FieldStartTotalizer)
	}
	if m.end_totalizer != nil {
		fields = append(fields, fueltransaction.FieldEndTotalizer)
	}
	if m.nozzle_start_time != nil {
		fields = append(fields, fueltransaction.FieldNozzleStartTime)
	}
	if m.nozzle_end_time != nil {
		fields = append(fields, fueltransaction.FieldNozzleEndTime)
	}
	if m.staff_card_id != nil {
		fields = append(fields, fueltransaction.FieldStaffCardID)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *FuelTransactionMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case fueltransaction.FieldTransactionNumber:
		return m.TransactionNumber()
	case fueltransaction.FieldStationID:
		return m.StationID()
	case fueltransaction.FieldPumpID:
		return m.PumpID()
	case fueltransaction.FieldNozzleID:
		return m.NozzleID()
	case fueltransaction.FieldFuelType:
		return m.FuelType()
	case fueltransaction.FieldFuelGrade:
		return m.FuelGrade()
	case fueltransaction.FieldTank:
		return m.Tank()
	case fueltransaction.FieldUnitPrice:
		return m.UnitPrice()
	case fueltransaction.FieldVolume:
		return m.Volume()
	case fueltransaction.FieldAmount:
		return m.Amount()
	case fueltransaction.FieldTotalVolume:
		return m.TotalVolume()
	case fueltransaction.FieldTotalAmount:
		return m.TotalAmount()
	case fueltransaction.FieldStatus:
		return m.Status()
	case fueltransaction.FieldMemberCardID:
		return m.MemberCardID()
	case fueltransaction.FieldMemberID:
		return m.MemberID()
	case fueltransaction.FieldEmployeeID:
		return m.EmployeeID()
	case fueltransaction.FieldFccTransactionID:
		return m.FccTransactionID()
	case fueltransaction.FieldPosTerminalID:
		return m.PosTerminalID()
	case fueltransaction.FieldMetadata:
		return m.Metadata()
	case fueltransaction.FieldCreatedAt:
		return m.CreatedAt()
	case fueltransaction.FieldUpdatedAt:
		return m.UpdatedAt()
	case fueltransaction.FieldProcessedAt:
		return m.ProcessedAt()
	case fueltransaction.FieldCancelledAt:
		return m.CancelledAt()
	case fueltransaction.FieldStartTotalizer:
		return m.StartTotalizer()
	case fueltransaction.FieldEndTotalizer:
		return m.EndTotalizer()
	case fueltransaction.FieldNozzleStartTime:
		return m.NozzleStartTime()
	case fueltransaction.FieldNozzleEndTime:
		return m.NozzleEndTime()
	case fueltransaction.FieldStaffCardID:
		return m.StaffCardID()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *FuelTransactionMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case fueltransaction.FieldTransactionNumber:
		return m.OldTransactionNumber(ctx)
	case fueltransaction.FieldStationID:
		return m.OldStationID(ctx)
	case fueltransaction.FieldPumpID:
		return m.OldPumpID(ctx)
	case fueltransaction.FieldNozzleID:
		return m.OldNozzleID(ctx)
	case fueltransaction.FieldFuelType:
		return m.OldFuelType(ctx)
	case fueltransaction.FieldFuelGrade:
		return m.OldFuelGrade(ctx)
	case fueltransaction.FieldTank:
		return m.OldTank(ctx)
	case fueltransaction.FieldUnitPrice:
		return m.OldUnitPrice(ctx)
	case fueltransaction.FieldVolume:
		return m.OldVolume(ctx)
	case fueltransaction.FieldAmount:
		return m.OldAmount(ctx)
	case fueltransaction.FieldTotalVolume:
		return m.OldTotalVolume(ctx)
	case fueltransaction.FieldTotalAmount:
		return m.OldTotalAmount(ctx)
	case fueltransaction.FieldStatus:
		return m.OldStatus(ctx)
	case fueltransaction.FieldMemberCardID:
		return m.OldMemberCardID(ctx)
	case fueltransaction.FieldMemberID:
		return m.OldMemberID(ctx)
	case fueltransaction.FieldEmployeeID:
		return m.OldEmployeeID(ctx)
	case fueltransaction.FieldFccTransactionID:
		return m.OldFccTransactionID(ctx)
	case fueltransaction.FieldPosTerminalID:
		return m.OldPosTerminalID(ctx)
	case fueltransaction.FieldMetadata:
		return m.OldMetadata(ctx)
	case fueltransaction.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case fueltransaction.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case fueltransaction.FieldProcessedAt:
		return m.OldProcessedAt(ctx)
	case fueltransaction.FieldCancelledAt:
		return m.OldCancelledAt(ctx)
	case fueltransaction.FieldStartTotalizer:
		return m.OldStartTotalizer(ctx)
	case fueltransaction.FieldEndTotalizer:
		return m.OldEndTotalizer(ctx)
	case fueltransaction.FieldNozzleStartTime:
		return m.OldNozzleStartTime(ctx)
	case fueltransaction.FieldNozzleEndTime:
		return m.OldNozzleEndTime(ctx)
	case fueltransaction.FieldStaffCardID:
		return m.OldStaffCardID(ctx)
	}
	return nil, fmt.Errorf("unknown FuelTransaction field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FuelTransactionMutation) SetField(name string, value ent.Value) error {
	switch name {
	case fueltransaction.FieldTransactionNumber:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactionNumber(v)
		return nil
	case fueltransaction.FieldStationID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStationID(v)
		return nil
	case fueltransaction.FieldPumpID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPumpID(v)
		return nil
	case fueltransaction.FieldNozzleID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNozzleID(v)
		return nil
	case fueltransaction.FieldFuelType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFuelType(v)
		return nil
	case fueltransaction.FieldFuelGrade:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFuelGrade(v)
		return nil
	case fueltransaction.FieldTank:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTank(v)
		return nil
	case fueltransaction.FieldUnitPrice:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUnitPrice(v)
		return nil
	case fueltransaction.FieldVolume:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVolume(v)
		return nil
	case fueltransaction.FieldAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAmount(v)
		return nil
	case fueltransaction.FieldTotalVolume:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTotalVolume(v)
		return nil
	case fueltransaction.FieldTotalAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTotalAmount(v)
		return nil
	case fueltransaction.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case fueltransaction.FieldMemberCardID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMemberCardID(v)
		return nil
	case fueltransaction.FieldMemberID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMemberID(v)
		return nil
	case fueltransaction.FieldEmployeeID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmployeeID(v)
		return nil
	case fueltransaction.FieldFccTransactionID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFccTransactionID(v)
		return nil
	case fueltransaction.FieldPosTerminalID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPosTerminalID(v)
		return nil
	case fueltransaction.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case fueltransaction.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case fueltransaction.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case fueltransaction.FieldProcessedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProcessedAt(v)
		return nil
	case fueltransaction.FieldCancelledAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCancelledAt(v)
		return nil
	case fueltransaction.FieldStartTotalizer:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStartTotalizer(v)
		return nil
	case fueltransaction.FieldEndTotalizer:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEndTotalizer(v)
		return nil
	case fueltransaction.FieldNozzleStartTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNozzleStartTime(v)
		return nil
	case fueltransaction.FieldNozzleEndTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNozzleEndTime(v)
		return nil
	case fueltransaction.FieldStaffCardID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStaffCardID(v)
		return nil
	}
	return fmt.Errorf("unknown FuelTransaction field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *FuelTransactionMutation) AddedFields() []string {
	var fields []string
	if m.addstation_id != nil {
		fields = append(fields, fueltransaction.FieldStationID)
	}
	if m.addtank != nil {
		fields = append(fields, fueltransaction.FieldTank)
	}
	if m.addunit_price != nil {
		fields = append(fields, fueltransaction.FieldUnitPrice)
	}
	if m.addvolume != nil {
		fields = append(fields, fueltransaction.FieldVolume)
	}
	if m.addamount != nil {
		fields = append(fields, fueltransaction.FieldAmount)
	}
	if m.addtotal_volume != nil {
		fields = append(fields, fueltransaction.FieldTotalVolume)
	}
	if m.addtotal_amount != nil {
		fields = append(fields, fueltransaction.FieldTotalAmount)
	}
	if m.addstart_totalizer != nil {
		fields = append(fields, fueltransaction.FieldStartTotalizer)
	}
	if m.addend_totalizer != nil {
		fields = append(fields, fueltransaction.FieldEndTotalizer)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *FuelTransactionMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case fueltransaction.FieldStationID:
		return m.AddedStationID()
	case fueltransaction.FieldTank:
		return m.AddedTank()
	case fueltransaction.FieldUnitPrice:
		return m.AddedUnitPrice()
	case fueltransaction.FieldVolume:
		return m.AddedVolume()
	case fueltransaction.FieldAmount:
		return m.AddedAmount()
	case fueltransaction.FieldTotalVolume:
		return m.AddedTotalVolume()
	case fueltransaction.FieldTotalAmount:
		return m.AddedTotalAmount()
	case fueltransaction.FieldStartTotalizer:
		return m.AddedStartTotalizer()
	case fueltransaction.FieldEndTotalizer:
		return m.AddedEndTotalizer()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FuelTransactionMutation) AddField(name string, value ent.Value) error {
	switch name {
	case fueltransaction.FieldStationID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStationID(v)
		return nil
	case fueltransaction.FieldTank:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTank(v)
		return nil
	case fueltransaction.FieldUnitPrice:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddUnitPrice(v)
		return nil
	case fueltransaction.FieldVolume:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddVolume(v)
		return nil
	case fueltransaction.FieldAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddAmount(v)
		return nil
	case fueltransaction.FieldTotalVolume:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTotalVolume(v)
		return nil
	case fueltransaction.FieldTotalAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTotalAmount(v)
		return nil
	case fueltransaction.FieldStartTotalizer:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStartTotalizer(v)
		return nil
	case fueltransaction.FieldEndTotalizer:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddEndTotalizer(v)
		return nil
	}
	return fmt.Errorf("unknown FuelTransaction numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *FuelTransactionMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(fueltransaction.FieldMemberCardID) {
		fields = append(fields, fueltransaction.FieldMemberCardID)
	}
	if m.FieldCleared(fueltransaction.FieldMemberID) {
		fields = append(fields, fueltransaction.FieldMemberID)
	}
	if m.FieldCleared(fueltransaction.FieldEmployeeID) {
		fields = append(fields, fueltransaction.FieldEmployeeID)
	}
	if m.FieldCleared(fueltransaction.FieldFccTransactionID) {
		fields = append(fields, fueltransaction.FieldFccTransactionID)
	}
	if m.FieldCleared(fueltransaction.FieldPosTerminalID) {
		fields = append(fields, fueltransaction.FieldPosTerminalID)
	}
	if m.FieldCleared(fueltransaction.FieldMetadata) {
		fields = append(fields, fueltransaction.FieldMetadata)
	}
	if m.FieldCleared(fueltransaction.FieldProcessedAt) {
		fields = append(fields, fueltransaction.FieldProcessedAt)
	}
	if m.FieldCleared(fueltransaction.FieldCancelledAt) {
		fields = append(fields, fueltransaction.FieldCancelledAt)
	}
	if m.FieldCleared(fueltransaction.FieldStartTotalizer) {
		fields = append(fields, fueltransaction.FieldStartTotalizer)
	}
	if m.FieldCleared(fueltransaction.FieldEndTotalizer) {
		fields = append(fields, fueltransaction.FieldEndTotalizer)
	}
	if m.FieldCleared(fueltransaction.FieldNozzleStartTime) {
		fields = append(fields, fueltransaction.FieldNozzleStartTime)
	}
	if m.FieldCleared(fueltransaction.FieldNozzleEndTime) {
		fields = append(fields, fueltransaction.FieldNozzleEndTime)
	}
	if m.FieldCleared(fueltransaction.FieldStaffCardID) {
		fields = append(fields, fueltransaction.FieldStaffCardID)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *FuelTransactionMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *FuelTransactionMutation) ClearField(name string) error {
	switch name {
	case fueltransaction.FieldMemberCardID:
		m.ClearMemberCardID()
		return nil
	case fueltransaction.FieldMemberID:
		m.ClearMemberID()
		return nil
	case fueltransaction.FieldEmployeeID:
		m.ClearEmployeeID()
		return nil
	case fueltransaction.FieldFccTransactionID:
		m.ClearFccTransactionID()
		return nil
	case fueltransaction.FieldPosTerminalID:
		m.ClearPosTerminalID()
		return nil
	case fueltransaction.FieldMetadata:
		m.ClearMetadata()
		return nil
	case fueltransaction.FieldProcessedAt:
		m.ClearProcessedAt()
		return nil
	case fueltransaction.FieldCancelledAt:
		m.ClearCancelledAt()
		return nil
	case fueltransaction.FieldStartTotalizer:
		m.ClearStartTotalizer()
		return nil
	case fueltransaction.FieldEndTotalizer:
		m.ClearEndTotalizer()
		return nil
	case fueltransaction.FieldNozzleStartTime:
		m.ClearNozzleStartTime()
		return nil
	case fueltransaction.FieldNozzleEndTime:
		m.ClearNozzleEndTime()
		return nil
	case fueltransaction.FieldStaffCardID:
		m.ClearStaffCardID()
		return nil
	}
	return fmt.Errorf("unknown FuelTransaction nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *FuelTransactionMutation) ResetField(name string) error {
	switch name {
	case fueltransaction.FieldTransactionNumber:
		m.ResetTransactionNumber()
		return nil
	case fueltransaction.FieldStationID:
		m.ResetStationID()
		return nil
	case fueltransaction.FieldPumpID:
		m.ResetPumpID()
		return nil
	case fueltransaction.FieldNozzleID:
		m.ResetNozzleID()
		return nil
	case fueltransaction.FieldFuelType:
		m.ResetFuelType()
		return nil
	case fueltransaction.FieldFuelGrade:
		m.ResetFuelGrade()
		return nil
	case fueltransaction.FieldTank:
		m.ResetTank()
		return nil
	case fueltransaction.FieldUnitPrice:
		m.ResetUnitPrice()
		return nil
	case fueltransaction.FieldVolume:
		m.ResetVolume()
		return nil
	case fueltransaction.FieldAmount:
		m.ResetAmount()
		return nil
	case fueltransaction.FieldTotalVolume:
		m.ResetTotalVolume()
		return nil
	case fueltransaction.FieldTotalAmount:
		m.ResetTotalAmount()
		return nil
	case fueltransaction.FieldStatus:
		m.ResetStatus()
		return nil
	case fueltransaction.FieldMemberCardID:
		m.ResetMemberCardID()
		return nil
	case fueltransaction.FieldMemberID:
		m.ResetMemberID()
		return nil
	case fueltransaction.FieldEmployeeID:
		m.ResetEmployeeID()
		return nil
	case fueltransaction.FieldFccTransactionID:
		m.ResetFccTransactionID()
		return nil
	case fueltransaction.FieldPosTerminalID:
		m.ResetPosTerminalID()
		return nil
	case fueltransaction.FieldMetadata:
		m.ResetMetadata()
		return nil
	case fueltransaction.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case fueltransaction.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case fueltransaction.FieldProcessedAt:
		m.ResetProcessedAt()
		return nil
	case fueltransaction.FieldCancelledAt:
		m.ResetCancelledAt()
		return nil
	case fueltransaction.FieldStartTotalizer:
		m.ResetStartTotalizer()
		return nil
	case fueltransaction.FieldEndTotalizer:
		m.ResetEndTotalizer()
		return nil
	case fueltransaction.FieldNozzleStartTime:
		m.ResetNozzleStartTime()
		return nil
	case fueltransaction.FieldNozzleEndTime:
		m.ResetNozzleEndTime()
		return nil
	case fueltransaction.FieldStaffCardID:
		m.ResetStaffCardID()
		return nil
	}
	return fmt.Errorf("unknown FuelTransaction field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *FuelTransactionMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.order_link != nil {
		edges = append(edges, fueltransaction.EdgeOrderLink)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *FuelTransactionMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case fueltransaction.EdgeOrderLink:
		if id := m.order_link; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *FuelTransactionMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *FuelTransactionMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *FuelTransactionMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedorder_link {
		edges = append(edges, fueltransaction.EdgeOrderLink)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *FuelTransactionMutation) EdgeCleared(name string) bool {
	switch name {
	case fueltransaction.EdgeOrderLink:
		return m.clearedorder_link
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *FuelTransactionMutation) ClearEdge(name string) error {
	switch name {
	case fueltransaction.EdgeOrderLink:
		m.ClearOrderLink()
		return nil
	}
	return fmt.Errorf("unknown FuelTransaction unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *FuelTransactionMutation) ResetEdge(name string) error {
	switch name {
	case fueltransaction.EdgeOrderLink:
		m.ResetOrderLink()
		return nil
	}
	return fmt.Errorf("unknown FuelTransaction edge %s", name)
}

// FuelTransactionOrderLinkMutation represents an operation that mutates the FuelTransactionOrderLink nodes in the graph.
type FuelTransactionOrderLinkMutation struct {
	config
	op                      Op
	typ                     string
	id                      *string
	allocated_amount        *float64
	addallocated_amount     *float64
	status                  *string
	metadata                *map[string]interface{}
	created_at              *time.Time
	updated_at              *time.Time
	deactivated_at          *time.Time
	clearedFields           map[string]struct{}
	fuel_transaction        *string
	clearedfuel_transaction bool
	_order                  *string
	cleared_order           bool
	done                    bool
	oldValue                func(context.Context) (*FuelTransactionOrderLink, error)
	predicates              []predicate.FuelTransactionOrderLink
}

var _ ent.Mutation = (*FuelTransactionOrderLinkMutation)(nil)

// fueltransactionorderlinkOption allows management of the mutation configuration using functional options.
type fueltransactionorderlinkOption func(*FuelTransactionOrderLinkMutation)

// newFuelTransactionOrderLinkMutation creates new mutation for the FuelTransactionOrderLink entity.
func newFuelTransactionOrderLinkMutation(c config, op Op, opts ...fueltransactionorderlinkOption) *FuelTransactionOrderLinkMutation {
	m := &FuelTransactionOrderLinkMutation{
		config:        c,
		op:            op,
		typ:           TypeFuelTransactionOrderLink,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withFuelTransactionOrderLinkID sets the ID field of the mutation.
func withFuelTransactionOrderLinkID(id string) fueltransactionorderlinkOption {
	return func(m *FuelTransactionOrderLinkMutation) {
		var (
			err   error
			once  sync.Once
			value *FuelTransactionOrderLink
		)
		m.oldValue = func(ctx context.Context) (*FuelTransactionOrderLink, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().FuelTransactionOrderLink.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withFuelTransactionOrderLink sets the old FuelTransactionOrderLink of the mutation.
func withFuelTransactionOrderLink(node *FuelTransactionOrderLink) fueltransactionorderlinkOption {
	return func(m *FuelTransactionOrderLinkMutation) {
		m.oldValue = func(context.Context) (*FuelTransactionOrderLink, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m FuelTransactionOrderLinkMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m FuelTransactionOrderLinkMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("order_service: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of FuelTransactionOrderLink entities.
func (m *FuelTransactionOrderLinkMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *FuelTransactionOrderLinkMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *FuelTransactionOrderLinkMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().FuelTransactionOrderLink.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetFuelTransactionID sets the "fuel_transaction_id" field.
func (m *FuelTransactionOrderLinkMutation) SetFuelTransactionID(s string) {
	m.fuel_transaction = &s
}

// FuelTransactionID returns the value of the "fuel_transaction_id" field in the mutation.
func (m *FuelTransactionOrderLinkMutation) FuelTransactionID() (r string, exists bool) {
	v := m.fuel_transaction
	if v == nil {
		return
	}
	return *v, true
}

// OldFuelTransactionID returns the old "fuel_transaction_id" field's value of the FuelTransactionOrderLink entity.
// If the FuelTransactionOrderLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionOrderLinkMutation) OldFuelTransactionID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFuelTransactionID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFuelTransactionID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFuelTransactionID: %w", err)
	}
	return oldValue.FuelTransactionID, nil
}

// ResetFuelTransactionID resets all changes to the "fuel_transaction_id" field.
func (m *FuelTransactionOrderLinkMutation) ResetFuelTransactionID() {
	m.fuel_transaction = nil
}

// SetOrderID sets the "order_id" field.
func (m *FuelTransactionOrderLinkMutation) SetOrderID(s string) {
	m._order = &s
}

// OrderID returns the value of the "order_id" field in the mutation.
func (m *FuelTransactionOrderLinkMutation) OrderID() (r string, exists bool) {
	v := m._order
	if v == nil {
		return
	}
	return *v, true
}

// OldOrderID returns the old "order_id" field's value of the FuelTransactionOrderLink entity.
// If the FuelTransactionOrderLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionOrderLinkMutation) OldOrderID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOrderID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOrderID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOrderID: %w", err)
	}
	return oldValue.OrderID, nil
}

// ResetOrderID resets all changes to the "order_id" field.
func (m *FuelTransactionOrderLinkMutation) ResetOrderID() {
	m._order = nil
}

// SetAllocatedAmount sets the "allocated_amount" field.
func (m *FuelTransactionOrderLinkMutation) SetAllocatedAmount(f float64) {
	m.allocated_amount = &f
	m.addallocated_amount = nil
}

// AllocatedAmount returns the value of the "allocated_amount" field in the mutation.
func (m *FuelTransactionOrderLinkMutation) AllocatedAmount() (r float64, exists bool) {
	v := m.allocated_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldAllocatedAmount returns the old "allocated_amount" field's value of the FuelTransactionOrderLink entity.
// If the FuelTransactionOrderLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionOrderLinkMutation) OldAllocatedAmount(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAllocatedAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAllocatedAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAllocatedAmount: %w", err)
	}
	return oldValue.AllocatedAmount, nil
}

// AddAllocatedAmount adds f to the "allocated_amount" field.
func (m *FuelTransactionOrderLinkMutation) AddAllocatedAmount(f float64) {
	if m.addallocated_amount != nil {
		*m.addallocated_amount += f
	} else {
		m.addallocated_amount = &f
	}
}

// AddedAllocatedAmount returns the value that was added to the "allocated_amount" field in this mutation.
func (m *FuelTransactionOrderLinkMutation) AddedAllocatedAmount() (r float64, exists bool) {
	v := m.addallocated_amount
	if v == nil {
		return
	}
	return *v, true
}

// ResetAllocatedAmount resets all changes to the "allocated_amount" field.
func (m *FuelTransactionOrderLinkMutation) ResetAllocatedAmount() {
	m.allocated_amount = nil
	m.addallocated_amount = nil
}

// SetStatus sets the "status" field.
func (m *FuelTransactionOrderLinkMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *FuelTransactionOrderLinkMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the FuelTransactionOrderLink entity.
// If the FuelTransactionOrderLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionOrderLinkMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *FuelTransactionOrderLinkMutation) ResetStatus() {
	m.status = nil
}

// SetMetadata sets the "metadata" field.
func (m *FuelTransactionOrderLinkMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *FuelTransactionOrderLinkMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the FuelTransactionOrderLink entity.
// If the FuelTransactionOrderLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionOrderLinkMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *FuelTransactionOrderLinkMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[fueltransactionorderlink.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *FuelTransactionOrderLinkMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[fueltransactionorderlink.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *FuelTransactionOrderLinkMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, fueltransactionorderlink.FieldMetadata)
}

// SetCreatedAt sets the "created_at" field.
func (m *FuelTransactionOrderLinkMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *FuelTransactionOrderLinkMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the FuelTransactionOrderLink entity.
// If the FuelTransactionOrderLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionOrderLinkMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *FuelTransactionOrderLinkMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *FuelTransactionOrderLinkMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *FuelTransactionOrderLinkMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the FuelTransactionOrderLink entity.
// If the FuelTransactionOrderLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionOrderLinkMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *FuelTransactionOrderLinkMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeactivatedAt sets the "deactivated_at" field.
func (m *FuelTransactionOrderLinkMutation) SetDeactivatedAt(t time.Time) {
	m.deactivated_at = &t
}

// DeactivatedAt returns the value of the "deactivated_at" field in the mutation.
func (m *FuelTransactionOrderLinkMutation) DeactivatedAt() (r time.Time, exists bool) {
	v := m.deactivated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeactivatedAt returns the old "deactivated_at" field's value of the FuelTransactionOrderLink entity.
// If the FuelTransactionOrderLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FuelTransactionOrderLinkMutation) OldDeactivatedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeactivatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeactivatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeactivatedAt: %w", err)
	}
	return oldValue.DeactivatedAt, nil
}

// ClearDeactivatedAt clears the value of the "deactivated_at" field.
func (m *FuelTransactionOrderLinkMutation) ClearDeactivatedAt() {
	m.deactivated_at = nil
	m.clearedFields[fueltransactionorderlink.FieldDeactivatedAt] = struct{}{}
}

// DeactivatedAtCleared returns if the "deactivated_at" field was cleared in this mutation.
func (m *FuelTransactionOrderLinkMutation) DeactivatedAtCleared() bool {
	_, ok := m.clearedFields[fueltransactionorderlink.FieldDeactivatedAt]
	return ok
}

// ResetDeactivatedAt resets all changes to the "deactivated_at" field.
func (m *FuelTransactionOrderLinkMutation) ResetDeactivatedAt() {
	m.deactivated_at = nil
	delete(m.clearedFields, fueltransactionorderlink.FieldDeactivatedAt)
}

// ClearFuelTransaction clears the "fuel_transaction" edge to the FuelTransaction entity.
func (m *FuelTransactionOrderLinkMutation) ClearFuelTransaction() {
	m.clearedfuel_transaction = true
	m.clearedFields[fueltransactionorderlink.FieldFuelTransactionID] = struct{}{}
}

// FuelTransactionCleared reports if the "fuel_transaction" edge to the FuelTransaction entity was cleared.
func (m *FuelTransactionOrderLinkMutation) FuelTransactionCleared() bool {
	return m.clearedfuel_transaction
}

// FuelTransactionIDs returns the "fuel_transaction" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// FuelTransactionID instead. It exists only for internal usage by the builders.
func (m *FuelTransactionOrderLinkMutation) FuelTransactionIDs() (ids []string) {
	if id := m.fuel_transaction; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetFuelTransaction resets all changes to the "fuel_transaction" edge.
func (m *FuelTransactionOrderLinkMutation) ResetFuelTransaction() {
	m.fuel_transaction = nil
	m.clearedfuel_transaction = false
}

// ClearOrder clears the "order" edge to the Order entity.
func (m *FuelTransactionOrderLinkMutation) ClearOrder() {
	m.cleared_order = true
	m.clearedFields[fueltransactionorderlink.FieldOrderID] = struct{}{}
}

// OrderCleared reports if the "order" edge to the Order entity was cleared.
func (m *FuelTransactionOrderLinkMutation) OrderCleared() bool {
	return m.cleared_order
}

// OrderIDs returns the "order" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// OrderID instead. It exists only for internal usage by the builders.
func (m *FuelTransactionOrderLinkMutation) OrderIDs() (ids []string) {
	if id := m._order; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetOrder resets all changes to the "order" edge.
func (m *FuelTransactionOrderLinkMutation) ResetOrder() {
	m._order = nil
	m.cleared_order = false
}

// Where appends a list predicates to the FuelTransactionOrderLinkMutation builder.
func (m *FuelTransactionOrderLinkMutation) Where(ps ...predicate.FuelTransactionOrderLink) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the FuelTransactionOrderLinkMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *FuelTransactionOrderLinkMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.FuelTransactionOrderLink, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *FuelTransactionOrderLinkMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *FuelTransactionOrderLinkMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (FuelTransactionOrderLink).
func (m *FuelTransactionOrderLinkMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *FuelTransactionOrderLinkMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.fuel_transaction != nil {
		fields = append(fields, fueltransactionorderlink.FieldFuelTransactionID)
	}
	if m._order != nil {
		fields = append(fields, fueltransactionorderlink.FieldOrderID)
	}
	if m.allocated_amount != nil {
		fields = append(fields, fueltransactionorderlink.FieldAllocatedAmount)
	}
	if m.status != nil {
		fields = append(fields, fueltransactionorderlink.FieldStatus)
	}
	if m.metadata != nil {
		fields = append(fields, fueltransactionorderlink.FieldMetadata)
	}
	if m.created_at != nil {
		fields = append(fields, fueltransactionorderlink.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, fueltransactionorderlink.FieldUpdatedAt)
	}
	if m.deactivated_at != nil {
		fields = append(fields, fueltransactionorderlink.FieldDeactivatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *FuelTransactionOrderLinkMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case fueltransactionorderlink.FieldFuelTransactionID:
		return m.FuelTransactionID()
	case fueltransactionorderlink.FieldOrderID:
		return m.OrderID()
	case fueltransactionorderlink.FieldAllocatedAmount:
		return m.AllocatedAmount()
	case fueltransactionorderlink.FieldStatus:
		return m.Status()
	case fueltransactionorderlink.FieldMetadata:
		return m.Metadata()
	case fueltransactionorderlink.FieldCreatedAt:
		return m.CreatedAt()
	case fueltransactionorderlink.FieldUpdatedAt:
		return m.UpdatedAt()
	case fueltransactionorderlink.FieldDeactivatedAt:
		return m.DeactivatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *FuelTransactionOrderLinkMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case fueltransactionorderlink.FieldFuelTransactionID:
		return m.OldFuelTransactionID(ctx)
	case fueltransactionorderlink.FieldOrderID:
		return m.OldOrderID(ctx)
	case fueltransactionorderlink.FieldAllocatedAmount:
		return m.OldAllocatedAmount(ctx)
	case fueltransactionorderlink.FieldStatus:
		return m.OldStatus(ctx)
	case fueltransactionorderlink.FieldMetadata:
		return m.OldMetadata(ctx)
	case fueltransactionorderlink.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case fueltransactionorderlink.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case fueltransactionorderlink.FieldDeactivatedAt:
		return m.OldDeactivatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown FuelTransactionOrderLink field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FuelTransactionOrderLinkMutation) SetField(name string, value ent.Value) error {
	switch name {
	case fueltransactionorderlink.FieldFuelTransactionID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFuelTransactionID(v)
		return nil
	case fueltransactionorderlink.FieldOrderID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOrderID(v)
		return nil
	case fueltransactionorderlink.FieldAllocatedAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAllocatedAmount(v)
		return nil
	case fueltransactionorderlink.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case fueltransactionorderlink.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case fueltransactionorderlink.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case fueltransactionorderlink.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case fueltransactionorderlink.FieldDeactivatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeactivatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown FuelTransactionOrderLink field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *FuelTransactionOrderLinkMutation) AddedFields() []string {
	var fields []string
	if m.addallocated_amount != nil {
		fields = append(fields, fueltransactionorderlink.FieldAllocatedAmount)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *FuelTransactionOrderLinkMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case fueltransactionorderlink.FieldAllocatedAmount:
		return m.AddedAllocatedAmount()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FuelTransactionOrderLinkMutation) AddField(name string, value ent.Value) error {
	switch name {
	case fueltransactionorderlink.FieldAllocatedAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddAllocatedAmount(v)
		return nil
	}
	return fmt.Errorf("unknown FuelTransactionOrderLink numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *FuelTransactionOrderLinkMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(fueltransactionorderlink.FieldMetadata) {
		fields = append(fields, fueltransactionorderlink.FieldMetadata)
	}
	if m.FieldCleared(fueltransactionorderlink.FieldDeactivatedAt) {
		fields = append(fields, fueltransactionorderlink.FieldDeactivatedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *FuelTransactionOrderLinkMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *FuelTransactionOrderLinkMutation) ClearField(name string) error {
	switch name {
	case fueltransactionorderlink.FieldMetadata:
		m.ClearMetadata()
		return nil
	case fueltransactionorderlink.FieldDeactivatedAt:
		m.ClearDeactivatedAt()
		return nil
	}
	return fmt.Errorf("unknown FuelTransactionOrderLink nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *FuelTransactionOrderLinkMutation) ResetField(name string) error {
	switch name {
	case fueltransactionorderlink.FieldFuelTransactionID:
		m.ResetFuelTransactionID()
		return nil
	case fueltransactionorderlink.FieldOrderID:
		m.ResetOrderID()
		return nil
	case fueltransactionorderlink.FieldAllocatedAmount:
		m.ResetAllocatedAmount()
		return nil
	case fueltransactionorderlink.FieldStatus:
		m.ResetStatus()
		return nil
	case fueltransactionorderlink.FieldMetadata:
		m.ResetMetadata()
		return nil
	case fueltransactionorderlink.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case fueltransactionorderlink.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case fueltransactionorderlink.FieldDeactivatedAt:
		m.ResetDeactivatedAt()
		return nil
	}
	return fmt.Errorf("unknown FuelTransactionOrderLink field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *FuelTransactionOrderLinkMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.fuel_transaction != nil {
		edges = append(edges, fueltransactionorderlink.EdgeFuelTransaction)
	}
	if m._order != nil {
		edges = append(edges, fueltransactionorderlink.EdgeOrder)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *FuelTransactionOrderLinkMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case fueltransactionorderlink.EdgeFuelTransaction:
		if id := m.fuel_transaction; id != nil {
			return []ent.Value{*id}
		}
	case fueltransactionorderlink.EdgeOrder:
		if id := m._order; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *FuelTransactionOrderLinkMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *FuelTransactionOrderLinkMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *FuelTransactionOrderLinkMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedfuel_transaction {
		edges = append(edges, fueltransactionorderlink.EdgeFuelTransaction)
	}
	if m.cleared_order {
		edges = append(edges, fueltransactionorderlink.EdgeOrder)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *FuelTransactionOrderLinkMutation) EdgeCleared(name string) bool {
	switch name {
	case fueltransactionorderlink.EdgeFuelTransaction:
		return m.clearedfuel_transaction
	case fueltransactionorderlink.EdgeOrder:
		return m.cleared_order
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *FuelTransactionOrderLinkMutation) ClearEdge(name string) error {
	switch name {
	case fueltransactionorderlink.EdgeFuelTransaction:
		m.ClearFuelTransaction()
		return nil
	case fueltransactionorderlink.EdgeOrder:
		m.ClearOrder()
		return nil
	}
	return fmt.Errorf("unknown FuelTransactionOrderLink unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *FuelTransactionOrderLinkMutation) ResetEdge(name string) error {
	switch name {
	case fueltransactionorderlink.EdgeFuelTransaction:
		m.ResetFuelTransaction()
		return nil
	case fueltransactionorderlink.EdgeOrder:
		m.ResetOrder()
		return nil
	}
	return fmt.Errorf("unknown FuelTransactionOrderLink edge %s", name)
}

// OrderMutation represents an operation that mutates the Order nodes in the graph.
type OrderMutation struct {
	config
	op                           Op
	typ                          string
	id                           *string
	order_number                 *string
	customer_id                  *int64
	addcustomer_id               *int64
	customer_phone               *string
	license_plate                *string
	customer_name                *string
	station_id                   *int64
	addstation_id                *int64
	status                       *string
	total_amount                 *float64
	addtotal_amount              *float64
	discount_amount              *float64
	adddiscount_amount           *float64
	final_amount                 *float64
	addfinal_amount              *float64
	tax_amount                   *float64
	addtax_amount                *float64
	paid_amount                  *float64
	addpaid_amount               *float64
	metadata                     *map[string]interface{}
	created_at                   *time.Time
	updated_at                   *time.Time
	completed_at                 *time.Time
	cancelled_at                 *time.Time
	employee_no                  *string
	staff_card_id                *int64
	addstaff_card_id             *int64
	clearedFields                map[string]struct{}
	fuel_transaction_link        *string
	clearedfuel_transaction_link bool
	done                         bool
	oldValue                     func(context.Context) (*Order, error)
	predicates                   []predicate.Order
}

var _ ent.Mutation = (*OrderMutation)(nil)

// orderOption allows management of the mutation configuration using functional options.
type orderOption func(*OrderMutation)

// newOrderMutation creates new mutation for the Order entity.
func newOrderMutation(c config, op Op, opts ...orderOption) *OrderMutation {
	m := &OrderMutation{
		config:        c,
		op:            op,
		typ:           TypeOrder,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withOrderID sets the ID field of the mutation.
func withOrderID(id string) orderOption {
	return func(m *OrderMutation) {
		var (
			err   error
			once  sync.Once
			value *Order
		)
		m.oldValue = func(ctx context.Context) (*Order, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Order.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withOrder sets the old Order of the mutation.
func withOrder(node *Order) orderOption {
	return func(m *OrderMutation) {
		m.oldValue = func(context.Context) (*Order, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m OrderMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m OrderMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("order_service: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Order entities.
func (m *OrderMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *OrderMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *OrderMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Order.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetOrderNumber sets the "order_number" field.
func (m *OrderMutation) SetOrderNumber(s string) {
	m.order_number = &s
}

// OrderNumber returns the value of the "order_number" field in the mutation.
func (m *OrderMutation) OrderNumber() (r string, exists bool) {
	v := m.order_number
	if v == nil {
		return
	}
	return *v, true
}

// OldOrderNumber returns the old "order_number" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldOrderNumber(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOrderNumber is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOrderNumber requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOrderNumber: %w", err)
	}
	return oldValue.OrderNumber, nil
}

// ResetOrderNumber resets all changes to the "order_number" field.
func (m *OrderMutation) ResetOrderNumber() {
	m.order_number = nil
}

// SetCustomerID sets the "customer_id" field.
func (m *OrderMutation) SetCustomerID(i int64) {
	m.customer_id = &i
	m.addcustomer_id = nil
}

// CustomerID returns the value of the "customer_id" field in the mutation.
func (m *OrderMutation) CustomerID() (r int64, exists bool) {
	v := m.customer_id
	if v == nil {
		return
	}
	return *v, true
}

// OldCustomerID returns the old "customer_id" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldCustomerID(ctx context.Context) (v *int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCustomerID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCustomerID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCustomerID: %w", err)
	}
	return oldValue.CustomerID, nil
}

// AddCustomerID adds i to the "customer_id" field.
func (m *OrderMutation) AddCustomerID(i int64) {
	if m.addcustomer_id != nil {
		*m.addcustomer_id += i
	} else {
		m.addcustomer_id = &i
	}
}

// AddedCustomerID returns the value that was added to the "customer_id" field in this mutation.
func (m *OrderMutation) AddedCustomerID() (r int64, exists bool) {
	v := m.addcustomer_id
	if v == nil {
		return
	}
	return *v, true
}

// ClearCustomerID clears the value of the "customer_id" field.
func (m *OrderMutation) ClearCustomerID() {
	m.customer_id = nil
	m.addcustomer_id = nil
	m.clearedFields[order.FieldCustomerID] = struct{}{}
}

// CustomerIDCleared returns if the "customer_id" field was cleared in this mutation.
func (m *OrderMutation) CustomerIDCleared() bool {
	_, ok := m.clearedFields[order.FieldCustomerID]
	return ok
}

// ResetCustomerID resets all changes to the "customer_id" field.
func (m *OrderMutation) ResetCustomerID() {
	m.customer_id = nil
	m.addcustomer_id = nil
	delete(m.clearedFields, order.FieldCustomerID)
}

// SetCustomerPhone sets the "customer_phone" field.
func (m *OrderMutation) SetCustomerPhone(s string) {
	m.customer_phone = &s
}

// CustomerPhone returns the value of the "customer_phone" field in the mutation.
func (m *OrderMutation) CustomerPhone() (r string, exists bool) {
	v := m.customer_phone
	if v == nil {
		return
	}
	return *v, true
}

// OldCustomerPhone returns the old "customer_phone" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldCustomerPhone(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCustomerPhone is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCustomerPhone requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCustomerPhone: %w", err)
	}
	return oldValue.CustomerPhone, nil
}

// ClearCustomerPhone clears the value of the "customer_phone" field.
func (m *OrderMutation) ClearCustomerPhone() {
	m.customer_phone = nil
	m.clearedFields[order.FieldCustomerPhone] = struct{}{}
}

// CustomerPhoneCleared returns if the "customer_phone" field was cleared in this mutation.
func (m *OrderMutation) CustomerPhoneCleared() bool {
	_, ok := m.clearedFields[order.FieldCustomerPhone]
	return ok
}

// ResetCustomerPhone resets all changes to the "customer_phone" field.
func (m *OrderMutation) ResetCustomerPhone() {
	m.customer_phone = nil
	delete(m.clearedFields, order.FieldCustomerPhone)
}

// SetLicensePlate sets the "license_plate" field.
func (m *OrderMutation) SetLicensePlate(s string) {
	m.license_plate = &s
}

// LicensePlate returns the value of the "license_plate" field in the mutation.
func (m *OrderMutation) LicensePlate() (r string, exists bool) {
	v := m.license_plate
	if v == nil {
		return
	}
	return *v, true
}

// OldLicensePlate returns the old "license_plate" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldLicensePlate(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLicensePlate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLicensePlate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLicensePlate: %w", err)
	}
	return oldValue.LicensePlate, nil
}

// ClearLicensePlate clears the value of the "license_plate" field.
func (m *OrderMutation) ClearLicensePlate() {
	m.license_plate = nil
	m.clearedFields[order.FieldLicensePlate] = struct{}{}
}

// LicensePlateCleared returns if the "license_plate" field was cleared in this mutation.
func (m *OrderMutation) LicensePlateCleared() bool {
	_, ok := m.clearedFields[order.FieldLicensePlate]
	return ok
}

// ResetLicensePlate resets all changes to the "license_plate" field.
func (m *OrderMutation) ResetLicensePlate() {
	m.license_plate = nil
	delete(m.clearedFields, order.FieldLicensePlate)
}

// SetCustomerName sets the "customer_name" field.
func (m *OrderMutation) SetCustomerName(s string) {
	m.customer_name = &s
}

// CustomerName returns the value of the "customer_name" field in the mutation.
func (m *OrderMutation) CustomerName() (r string, exists bool) {
	v := m.customer_name
	if v == nil {
		return
	}
	return *v, true
}

// OldCustomerName returns the old "customer_name" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldCustomerName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCustomerName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCustomerName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCustomerName: %w", err)
	}
	return oldValue.CustomerName, nil
}

// ClearCustomerName clears the value of the "customer_name" field.
func (m *OrderMutation) ClearCustomerName() {
	m.customer_name = nil
	m.clearedFields[order.FieldCustomerName] = struct{}{}
}

// CustomerNameCleared returns if the "customer_name" field was cleared in this mutation.
func (m *OrderMutation) CustomerNameCleared() bool {
	_, ok := m.clearedFields[order.FieldCustomerName]
	return ok
}

// ResetCustomerName resets all changes to the "customer_name" field.
func (m *OrderMutation) ResetCustomerName() {
	m.customer_name = nil
	delete(m.clearedFields, order.FieldCustomerName)
}

// SetStationID sets the "station_id" field.
func (m *OrderMutation) SetStationID(i int64) {
	m.station_id = &i
	m.addstation_id = nil
}

// StationID returns the value of the "station_id" field in the mutation.
func (m *OrderMutation) StationID() (r int64, exists bool) {
	v := m.station_id
	if v == nil {
		return
	}
	return *v, true
}

// OldStationID returns the old "station_id" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldStationID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStationID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStationID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStationID: %w", err)
	}
	return oldValue.StationID, nil
}

// AddStationID adds i to the "station_id" field.
func (m *OrderMutation) AddStationID(i int64) {
	if m.addstation_id != nil {
		*m.addstation_id += i
	} else {
		m.addstation_id = &i
	}
}

// AddedStationID returns the value that was added to the "station_id" field in this mutation.
func (m *OrderMutation) AddedStationID() (r int64, exists bool) {
	v := m.addstation_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetStationID resets all changes to the "station_id" field.
func (m *OrderMutation) ResetStationID() {
	m.station_id = nil
	m.addstation_id = nil
}

// SetStatus sets the "status" field.
func (m *OrderMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *OrderMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *OrderMutation) ResetStatus() {
	m.status = nil
}

// SetTotalAmount sets the "total_amount" field.
func (m *OrderMutation) SetTotalAmount(f float64) {
	m.total_amount = &f
	m.addtotal_amount = nil
}

// TotalAmount returns the value of the "total_amount" field in the mutation.
func (m *OrderMutation) TotalAmount() (r float64, exists bool) {
	v := m.total_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldTotalAmount returns the old "total_amount" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldTotalAmount(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTotalAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTotalAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTotalAmount: %w", err)
	}
	return oldValue.TotalAmount, nil
}

// AddTotalAmount adds f to the "total_amount" field.
func (m *OrderMutation) AddTotalAmount(f float64) {
	if m.addtotal_amount != nil {
		*m.addtotal_amount += f
	} else {
		m.addtotal_amount = &f
	}
}

// AddedTotalAmount returns the value that was added to the "total_amount" field in this mutation.
func (m *OrderMutation) AddedTotalAmount() (r float64, exists bool) {
	v := m.addtotal_amount
	if v == nil {
		return
	}
	return *v, true
}

// ResetTotalAmount resets all changes to the "total_amount" field.
func (m *OrderMutation) ResetTotalAmount() {
	m.total_amount = nil
	m.addtotal_amount = nil
}

// SetDiscountAmount sets the "discount_amount" field.
func (m *OrderMutation) SetDiscountAmount(f float64) {
	m.discount_amount = &f
	m.adddiscount_amount = nil
}

// DiscountAmount returns the value of the "discount_amount" field in the mutation.
func (m *OrderMutation) DiscountAmount() (r float64, exists bool) {
	v := m.discount_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldDiscountAmount returns the old "discount_amount" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldDiscountAmount(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDiscountAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDiscountAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDiscountAmount: %w", err)
	}
	return oldValue.DiscountAmount, nil
}

// AddDiscountAmount adds f to the "discount_amount" field.
func (m *OrderMutation) AddDiscountAmount(f float64) {
	if m.adddiscount_amount != nil {
		*m.adddiscount_amount += f
	} else {
		m.adddiscount_amount = &f
	}
}

// AddedDiscountAmount returns the value that was added to the "discount_amount" field in this mutation.
func (m *OrderMutation) AddedDiscountAmount() (r float64, exists bool) {
	v := m.adddiscount_amount
	if v == nil {
		return
	}
	return *v, true
}

// ResetDiscountAmount resets all changes to the "discount_amount" field.
func (m *OrderMutation) ResetDiscountAmount() {
	m.discount_amount = nil
	m.adddiscount_amount = nil
}

// SetFinalAmount sets the "final_amount" field.
func (m *OrderMutation) SetFinalAmount(f float64) {
	m.final_amount = &f
	m.addfinal_amount = nil
}

// FinalAmount returns the value of the "final_amount" field in the mutation.
func (m *OrderMutation) FinalAmount() (r float64, exists bool) {
	v := m.final_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldFinalAmount returns the old "final_amount" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldFinalAmount(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFinalAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFinalAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFinalAmount: %w", err)
	}
	return oldValue.FinalAmount, nil
}

// AddFinalAmount adds f to the "final_amount" field.
func (m *OrderMutation) AddFinalAmount(f float64) {
	if m.addfinal_amount != nil {
		*m.addfinal_amount += f
	} else {
		m.addfinal_amount = &f
	}
}

// AddedFinalAmount returns the value that was added to the "final_amount" field in this mutation.
func (m *OrderMutation) AddedFinalAmount() (r float64, exists bool) {
	v := m.addfinal_amount
	if v == nil {
		return
	}
	return *v, true
}

// ResetFinalAmount resets all changes to the "final_amount" field.
func (m *OrderMutation) ResetFinalAmount() {
	m.final_amount = nil
	m.addfinal_amount = nil
}

// SetTaxAmount sets the "tax_amount" field.
func (m *OrderMutation) SetTaxAmount(f float64) {
	m.tax_amount = &f
	m.addtax_amount = nil
}

// TaxAmount returns the value of the "tax_amount" field in the mutation.
func (m *OrderMutation) TaxAmount() (r float64, exists bool) {
	v := m.tax_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldTaxAmount returns the old "tax_amount" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldTaxAmount(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTaxAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTaxAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTaxAmount: %w", err)
	}
	return oldValue.TaxAmount, nil
}

// AddTaxAmount adds f to the "tax_amount" field.
func (m *OrderMutation) AddTaxAmount(f float64) {
	if m.addtax_amount != nil {
		*m.addtax_amount += f
	} else {
		m.addtax_amount = &f
	}
}

// AddedTaxAmount returns the value that was added to the "tax_amount" field in this mutation.
func (m *OrderMutation) AddedTaxAmount() (r float64, exists bool) {
	v := m.addtax_amount
	if v == nil {
		return
	}
	return *v, true
}

// ResetTaxAmount resets all changes to the "tax_amount" field.
func (m *OrderMutation) ResetTaxAmount() {
	m.tax_amount = nil
	m.addtax_amount = nil
}

// SetPaidAmount sets the "paid_amount" field.
func (m *OrderMutation) SetPaidAmount(f float64) {
	m.paid_amount = &f
	m.addpaid_amount = nil
}

// PaidAmount returns the value of the "paid_amount" field in the mutation.
func (m *OrderMutation) PaidAmount() (r float64, exists bool) {
	v := m.paid_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldPaidAmount returns the old "paid_amount" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldPaidAmount(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaidAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaidAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaidAmount: %w", err)
	}
	return oldValue.PaidAmount, nil
}

// AddPaidAmount adds f to the "paid_amount" field.
func (m *OrderMutation) AddPaidAmount(f float64) {
	if m.addpaid_amount != nil {
		*m.addpaid_amount += f
	} else {
		m.addpaid_amount = &f
	}
}

// AddedPaidAmount returns the value that was added to the "paid_amount" field in this mutation.
func (m *OrderMutation) AddedPaidAmount() (r float64, exists bool) {
	v := m.addpaid_amount
	if v == nil {
		return
	}
	return *v, true
}

// ResetPaidAmount resets all changes to the "paid_amount" field.
func (m *OrderMutation) ResetPaidAmount() {
	m.paid_amount = nil
	m.addpaid_amount = nil
}

// SetMetadata sets the "metadata" field.
func (m *OrderMutation) SetMetadata(value map[string]interface{}) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *OrderMutation) Metadata() (r map[string]interface{}, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldMetadata(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *OrderMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[order.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *OrderMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[order.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *OrderMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, order.FieldMetadata)
}

// SetCreatedAt sets the "created_at" field.
func (m *OrderMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *OrderMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *OrderMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *OrderMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *OrderMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *OrderMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetCompletedAt sets the "completed_at" field.
func (m *OrderMutation) SetCompletedAt(t time.Time) {
	m.completed_at = &t
}

// CompletedAt returns the value of the "completed_at" field in the mutation.
func (m *OrderMutation) CompletedAt() (r time.Time, exists bool) {
	v := m.completed_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCompletedAt returns the old "completed_at" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldCompletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCompletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCompletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCompletedAt: %w", err)
	}
	return oldValue.CompletedAt, nil
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (m *OrderMutation) ClearCompletedAt() {
	m.completed_at = nil
	m.clearedFields[order.FieldCompletedAt] = struct{}{}
}

// CompletedAtCleared returns if the "completed_at" field was cleared in this mutation.
func (m *OrderMutation) CompletedAtCleared() bool {
	_, ok := m.clearedFields[order.FieldCompletedAt]
	return ok
}

// ResetCompletedAt resets all changes to the "completed_at" field.
func (m *OrderMutation) ResetCompletedAt() {
	m.completed_at = nil
	delete(m.clearedFields, order.FieldCompletedAt)
}

// SetCancelledAt sets the "cancelled_at" field.
func (m *OrderMutation) SetCancelledAt(t time.Time) {
	m.cancelled_at = &t
}

// CancelledAt returns the value of the "cancelled_at" field in the mutation.
func (m *OrderMutation) CancelledAt() (r time.Time, exists bool) {
	v := m.cancelled_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCancelledAt returns the old "cancelled_at" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldCancelledAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCancelledAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCancelledAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCancelledAt: %w", err)
	}
	return oldValue.CancelledAt, nil
}

// ClearCancelledAt clears the value of the "cancelled_at" field.
func (m *OrderMutation) ClearCancelledAt() {
	m.cancelled_at = nil
	m.clearedFields[order.FieldCancelledAt] = struct{}{}
}

// CancelledAtCleared returns if the "cancelled_at" field was cleared in this mutation.
func (m *OrderMutation) CancelledAtCleared() bool {
	_, ok := m.clearedFields[order.FieldCancelledAt]
	return ok
}

// ResetCancelledAt resets all changes to the "cancelled_at" field.
func (m *OrderMutation) ResetCancelledAt() {
	m.cancelled_at = nil
	delete(m.clearedFields, order.FieldCancelledAt)
}

// SetEmployeeNo sets the "employee_no" field.
func (m *OrderMutation) SetEmployeeNo(s string) {
	m.employee_no = &s
}

// EmployeeNo returns the value of the "employee_no" field in the mutation.
func (m *OrderMutation) EmployeeNo() (r string, exists bool) {
	v := m.employee_no
	if v == nil {
		return
	}
	return *v, true
}

// OldEmployeeNo returns the old "employee_no" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldEmployeeNo(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmployeeNo is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmployeeNo requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmployeeNo: %w", err)
	}
	return oldValue.EmployeeNo, nil
}

// ClearEmployeeNo clears the value of the "employee_no" field.
func (m *OrderMutation) ClearEmployeeNo() {
	m.employee_no = nil
	m.clearedFields[order.FieldEmployeeNo] = struct{}{}
}

// EmployeeNoCleared returns if the "employee_no" field was cleared in this mutation.
func (m *OrderMutation) EmployeeNoCleared() bool {
	_, ok := m.clearedFields[order.FieldEmployeeNo]
	return ok
}

// ResetEmployeeNo resets all changes to the "employee_no" field.
func (m *OrderMutation) ResetEmployeeNo() {
	m.employee_no = nil
	delete(m.clearedFields, order.FieldEmployeeNo)
}

// SetStaffCardID sets the "staff_card_id" field.
func (m *OrderMutation) SetStaffCardID(i int64) {
	m.staff_card_id = &i
	m.addstaff_card_id = nil
}

// StaffCardID returns the value of the "staff_card_id" field in the mutation.
func (m *OrderMutation) StaffCardID() (r int64, exists bool) {
	v := m.staff_card_id
	if v == nil {
		return
	}
	return *v, true
}

// OldStaffCardID returns the old "staff_card_id" field's value of the Order entity.
// If the Order object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *OrderMutation) OldStaffCardID(ctx context.Context) (v *int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStaffCardID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStaffCardID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStaffCardID: %w", err)
	}
	return oldValue.StaffCardID, nil
}

// AddStaffCardID adds i to the "staff_card_id" field.
func (m *OrderMutation) AddStaffCardID(i int64) {
	if m.addstaff_card_id != nil {
		*m.addstaff_card_id += i
	} else {
		m.addstaff_card_id = &i
	}
}

// AddedStaffCardID returns the value that was added to the "staff_card_id" field in this mutation.
func (m *OrderMutation) AddedStaffCardID() (r int64, exists bool) {
	v := m.addstaff_card_id
	if v == nil {
		return
	}
	return *v, true
}

// ClearStaffCardID clears the value of the "staff_card_id" field.
func (m *OrderMutation) ClearStaffCardID() {
	m.staff_card_id = nil
	m.addstaff_card_id = nil
	m.clearedFields[order.FieldStaffCardID] = struct{}{}
}

// StaffCardIDCleared returns if the "staff_card_id" field was cleared in this mutation.
func (m *OrderMutation) StaffCardIDCleared() bool {
	_, ok := m.clearedFields[order.FieldStaffCardID]
	return ok
}

// ResetStaffCardID resets all changes to the "staff_card_id" field.
func (m *OrderMutation) ResetStaffCardID() {
	m.staff_card_id = nil
	m.addstaff_card_id = nil
	delete(m.clearedFields, order.FieldStaffCardID)
}

// SetFuelTransactionLinkID sets the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity by id.
func (m *OrderMutation) SetFuelTransactionLinkID(id string) {
	m.fuel_transaction_link = &id
}

// ClearFuelTransactionLink clears the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity.
func (m *OrderMutation) ClearFuelTransactionLink() {
	m.clearedfuel_transaction_link = true
}

// FuelTransactionLinkCleared reports if the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity was cleared.
func (m *OrderMutation) FuelTransactionLinkCleared() bool {
	return m.clearedfuel_transaction_link
}

// FuelTransactionLinkID returns the "fuel_transaction_link" edge ID in the mutation.
func (m *OrderMutation) FuelTransactionLinkID() (id string, exists bool) {
	if m.fuel_transaction_link != nil {
		return *m.fuel_transaction_link, true
	}
	return
}

// FuelTransactionLinkIDs returns the "fuel_transaction_link" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// FuelTransactionLinkID instead. It exists only for internal usage by the builders.
func (m *OrderMutation) FuelTransactionLinkIDs() (ids []string) {
	if id := m.fuel_transaction_link; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetFuelTransactionLink resets all changes to the "fuel_transaction_link" edge.
func (m *OrderMutation) ResetFuelTransactionLink() {
	m.fuel_transaction_link = nil
	m.clearedfuel_transaction_link = false
}

// Where appends a list predicates to the OrderMutation builder.
func (m *OrderMutation) Where(ps ...predicate.Order) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the OrderMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *OrderMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Order, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *OrderMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *OrderMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Order).
func (m *OrderMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *OrderMutation) Fields() []string {
	fields := make([]string, 0, 19)
	if m.order_number != nil {
		fields = append(fields, order.FieldOrderNumber)
	}
	if m.customer_id != nil {
		fields = append(fields, order.FieldCustomerID)
	}
	if m.customer_phone != nil {
		fields = append(fields, order.FieldCustomerPhone)
	}
	if m.license_plate != nil {
		fields = append(fields, order.FieldLicensePlate)
	}
	if m.customer_name != nil {
		fields = append(fields, order.FieldCustomerName)
	}
	if m.station_id != nil {
		fields = append(fields, order.FieldStationID)
	}
	if m.status != nil {
		fields = append(fields, order.FieldStatus)
	}
	if m.total_amount != nil {
		fields = append(fields, order.FieldTotalAmount)
	}
	if m.discount_amount != nil {
		fields = append(fields, order.FieldDiscountAmount)
	}
	if m.final_amount != nil {
		fields = append(fields, order.FieldFinalAmount)
	}
	if m.tax_amount != nil {
		fields = append(fields, order.FieldTaxAmount)
	}
	if m.paid_amount != nil {
		fields = append(fields, order.FieldPaidAmount)
	}
	if m.metadata != nil {
		fields = append(fields, order.FieldMetadata)
	}
	if m.created_at != nil {
		fields = append(fields, order.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, order.FieldUpdatedAt)
	}
	if m.completed_at != nil {
		fields = append(fields, order.FieldCompletedAt)
	}
	if m.cancelled_at != nil {
		fields = append(fields, order.FieldCancelledAt)
	}
	if m.employee_no != nil {
		fields = append(fields, order.FieldEmployeeNo)
	}
	if m.staff_card_id != nil {
		fields = append(fields, order.FieldStaffCardID)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *OrderMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case order.FieldOrderNumber:
		return m.OrderNumber()
	case order.FieldCustomerID:
		return m.CustomerID()
	case order.FieldCustomerPhone:
		return m.CustomerPhone()
	case order.FieldLicensePlate:
		return m.LicensePlate()
	case order.FieldCustomerName:
		return m.CustomerName()
	case order.FieldStationID:
		return m.StationID()
	case order.FieldStatus:
		return m.Status()
	case order.FieldTotalAmount:
		return m.TotalAmount()
	case order.FieldDiscountAmount:
		return m.DiscountAmount()
	case order.FieldFinalAmount:
		return m.FinalAmount()
	case order.FieldTaxAmount:
		return m.TaxAmount()
	case order.FieldPaidAmount:
		return m.PaidAmount()
	case order.FieldMetadata:
		return m.Metadata()
	case order.FieldCreatedAt:
		return m.CreatedAt()
	case order.FieldUpdatedAt:
		return m.UpdatedAt()
	case order.FieldCompletedAt:
		return m.CompletedAt()
	case order.FieldCancelledAt:
		return m.CancelledAt()
	case order.FieldEmployeeNo:
		return m.EmployeeNo()
	case order.FieldStaffCardID:
		return m.StaffCardID()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *OrderMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case order.FieldOrderNumber:
		return m.OldOrderNumber(ctx)
	case order.FieldCustomerID:
		return m.OldCustomerID(ctx)
	case order.FieldCustomerPhone:
		return m.OldCustomerPhone(ctx)
	case order.FieldLicensePlate:
		return m.OldLicensePlate(ctx)
	case order.FieldCustomerName:
		return m.OldCustomerName(ctx)
	case order.FieldStationID:
		return m.OldStationID(ctx)
	case order.FieldStatus:
		return m.OldStatus(ctx)
	case order.FieldTotalAmount:
		return m.OldTotalAmount(ctx)
	case order.FieldDiscountAmount:
		return m.OldDiscountAmount(ctx)
	case order.FieldFinalAmount:
		return m.OldFinalAmount(ctx)
	case order.FieldTaxAmount:
		return m.OldTaxAmount(ctx)
	case order.FieldPaidAmount:
		return m.OldPaidAmount(ctx)
	case order.FieldMetadata:
		return m.OldMetadata(ctx)
	case order.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case order.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case order.FieldCompletedAt:
		return m.OldCompletedAt(ctx)
	case order.FieldCancelledAt:
		return m.OldCancelledAt(ctx)
	case order.FieldEmployeeNo:
		return m.OldEmployeeNo(ctx)
	case order.FieldStaffCardID:
		return m.OldStaffCardID(ctx)
	}
	return nil, fmt.Errorf("unknown Order field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *OrderMutation) SetField(name string, value ent.Value) error {
	switch name {
	case order.FieldOrderNumber:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOrderNumber(v)
		return nil
	case order.FieldCustomerID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCustomerID(v)
		return nil
	case order.FieldCustomerPhone:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCustomerPhone(v)
		return nil
	case order.FieldLicensePlate:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLicensePlate(v)
		return nil
	case order.FieldCustomerName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCustomerName(v)
		return nil
	case order.FieldStationID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStationID(v)
		return nil
	case order.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case order.FieldTotalAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTotalAmount(v)
		return nil
	case order.FieldDiscountAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDiscountAmount(v)
		return nil
	case order.FieldFinalAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFinalAmount(v)
		return nil
	case order.FieldTaxAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTaxAmount(v)
		return nil
	case order.FieldPaidAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaidAmount(v)
		return nil
	case order.FieldMetadata:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	case order.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case order.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case order.FieldCompletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCompletedAt(v)
		return nil
	case order.FieldCancelledAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCancelledAt(v)
		return nil
	case order.FieldEmployeeNo:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmployeeNo(v)
		return nil
	case order.FieldStaffCardID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStaffCardID(v)
		return nil
	}
	return fmt.Errorf("unknown Order field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *OrderMutation) AddedFields() []string {
	var fields []string
	if m.addcustomer_id != nil {
		fields = append(fields, order.FieldCustomerID)
	}
	if m.addstation_id != nil {
		fields = append(fields, order.FieldStationID)
	}
	if m.addtotal_amount != nil {
		fields = append(fields, order.FieldTotalAmount)
	}
	if m.adddiscount_amount != nil {
		fields = append(fields, order.FieldDiscountAmount)
	}
	if m.addfinal_amount != nil {
		fields = append(fields, order.FieldFinalAmount)
	}
	if m.addtax_amount != nil {
		fields = append(fields, order.FieldTaxAmount)
	}
	if m.addpaid_amount != nil {
		fields = append(fields, order.FieldPaidAmount)
	}
	if m.addstaff_card_id != nil {
		fields = append(fields, order.FieldStaffCardID)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *OrderMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case order.FieldCustomerID:
		return m.AddedCustomerID()
	case order.FieldStationID:
		return m.AddedStationID()
	case order.FieldTotalAmount:
		return m.AddedTotalAmount()
	case order.FieldDiscountAmount:
		return m.AddedDiscountAmount()
	case order.FieldFinalAmount:
		return m.AddedFinalAmount()
	case order.FieldTaxAmount:
		return m.AddedTaxAmount()
	case order.FieldPaidAmount:
		return m.AddedPaidAmount()
	case order.FieldStaffCardID:
		return m.AddedStaffCardID()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *OrderMutation) AddField(name string, value ent.Value) error {
	switch name {
	case order.FieldCustomerID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddCustomerID(v)
		return nil
	case order.FieldStationID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStationID(v)
		return nil
	case order.FieldTotalAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTotalAmount(v)
		return nil
	case order.FieldDiscountAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddDiscountAmount(v)
		return nil
	case order.FieldFinalAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddFinalAmount(v)
		return nil
	case order.FieldTaxAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTaxAmount(v)
		return nil
	case order.FieldPaidAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPaidAmount(v)
		return nil
	case order.FieldStaffCardID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStaffCardID(v)
		return nil
	}
	return fmt.Errorf("unknown Order numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *OrderMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(order.FieldCustomerID) {
		fields = append(fields, order.FieldCustomerID)
	}
	if m.FieldCleared(order.FieldCustomerPhone) {
		fields = append(fields, order.FieldCustomerPhone)
	}
	if m.FieldCleared(order.FieldLicensePlate) {
		fields = append(fields, order.FieldLicensePlate)
	}
	if m.FieldCleared(order.FieldCustomerName) {
		fields = append(fields, order.FieldCustomerName)
	}
	if m.FieldCleared(order.FieldMetadata) {
		fields = append(fields, order.FieldMetadata)
	}
	if m.FieldCleared(order.FieldCompletedAt) {
		fields = append(fields, order.FieldCompletedAt)
	}
	if m.FieldCleared(order.FieldCancelledAt) {
		fields = append(fields, order.FieldCancelledAt)
	}
	if m.FieldCleared(order.FieldEmployeeNo) {
		fields = append(fields, order.FieldEmployeeNo)
	}
	if m.FieldCleared(order.FieldStaffCardID) {
		fields = append(fields, order.FieldStaffCardID)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *OrderMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *OrderMutation) ClearField(name string) error {
	switch name {
	case order.FieldCustomerID:
		m.ClearCustomerID()
		return nil
	case order.FieldCustomerPhone:
		m.ClearCustomerPhone()
		return nil
	case order.FieldLicensePlate:
		m.ClearLicensePlate()
		return nil
	case order.FieldCustomerName:
		m.ClearCustomerName()
		return nil
	case order.FieldMetadata:
		m.ClearMetadata()
		return nil
	case order.FieldCompletedAt:
		m.ClearCompletedAt()
		return nil
	case order.FieldCancelledAt:
		m.ClearCancelledAt()
		return nil
	case order.FieldEmployeeNo:
		m.ClearEmployeeNo()
		return nil
	case order.FieldStaffCardID:
		m.ClearStaffCardID()
		return nil
	}
	return fmt.Errorf("unknown Order nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *OrderMutation) ResetField(name string) error {
	switch name {
	case order.FieldOrderNumber:
		m.ResetOrderNumber()
		return nil
	case order.FieldCustomerID:
		m.ResetCustomerID()
		return nil
	case order.FieldCustomerPhone:
		m.ResetCustomerPhone()
		return nil
	case order.FieldLicensePlate:
		m.ResetLicensePlate()
		return nil
	case order.FieldCustomerName:
		m.ResetCustomerName()
		return nil
	case order.FieldStationID:
		m.ResetStationID()
		return nil
	case order.FieldStatus:
		m.ResetStatus()
		return nil
	case order.FieldTotalAmount:
		m.ResetTotalAmount()
		return nil
	case order.FieldDiscountAmount:
		m.ResetDiscountAmount()
		return nil
	case order.FieldFinalAmount:
		m.ResetFinalAmount()
		return nil
	case order.FieldTaxAmount:
		m.ResetTaxAmount()
		return nil
	case order.FieldPaidAmount:
		m.ResetPaidAmount()
		return nil
	case order.FieldMetadata:
		m.ResetMetadata()
		return nil
	case order.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case order.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case order.FieldCompletedAt:
		m.ResetCompletedAt()
		return nil
	case order.FieldCancelledAt:
		m.ResetCancelledAt()
		return nil
	case order.FieldEmployeeNo:
		m.ResetEmployeeNo()
		return nil
	case order.FieldStaffCardID:
		m.ResetStaffCardID()
		return nil
	}
	return fmt.Errorf("unknown Order field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *OrderMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.fuel_transaction_link != nil {
		edges = append(edges, order.EdgeFuelTransactionLink)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *OrderMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case order.EdgeFuelTransactionLink:
		if id := m.fuel_transaction_link; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *OrderMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *OrderMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *OrderMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedfuel_transaction_link {
		edges = append(edges, order.EdgeFuelTransactionLink)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *OrderMutation) EdgeCleared(name string) bool {
	switch name {
	case order.EdgeFuelTransactionLink:
		return m.clearedfuel_transaction_link
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *OrderMutation) ClearEdge(name string) error {
	switch name {
	case order.EdgeFuelTransactionLink:
		m.ClearFuelTransactionLink()
		return nil
	}
	return fmt.Errorf("unknown Order unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *OrderMutation) ResetEdge(name string) error {
	switch name {
	case order.EdgeFuelTransactionLink:
		m.ResetFuelTransactionLink()
		return nil
	}
	return fmt.Errorf("unknown Order edge %s", name)
}
