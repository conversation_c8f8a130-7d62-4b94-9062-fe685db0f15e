// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
)

// OrderCreate is the builder for creating a Order entity.
type OrderCreate struct {
	config
	mutation *OrderMutation
	hooks    []Hook
}

// SetOrderNumber sets the "order_number" field.
func (oc *OrderCreate) SetOrderNumber(s string) *OrderCreate {
	oc.mutation.SetOrderNumber(s)
	return oc
}

// SetCustomerID sets the "customer_id" field.
func (oc *OrderCreate) SetCustomerID(i int64) *OrderCreate {
	oc.mutation.SetCustomerID(i)
	return oc
}

// SetNillableCustomerID sets the "customer_id" field if the given value is not nil.
func (oc *OrderCreate) SetNillableCustomerID(i *int64) *OrderCreate {
	if i != nil {
		oc.SetCustomerID(*i)
	}
	return oc
}

// SetCustomerPhone sets the "customer_phone" field.
func (oc *OrderCreate) SetCustomerPhone(s string) *OrderCreate {
	oc.mutation.SetCustomerPhone(s)
	return oc
}

// SetNillableCustomerPhone sets the "customer_phone" field if the given value is not nil.
func (oc *OrderCreate) SetNillableCustomerPhone(s *string) *OrderCreate {
	if s != nil {
		oc.SetCustomerPhone(*s)
	}
	return oc
}

// SetLicensePlate sets the "license_plate" field.
func (oc *OrderCreate) SetLicensePlate(s string) *OrderCreate {
	oc.mutation.SetLicensePlate(s)
	return oc
}

// SetNillableLicensePlate sets the "license_plate" field if the given value is not nil.
func (oc *OrderCreate) SetNillableLicensePlate(s *string) *OrderCreate {
	if s != nil {
		oc.SetLicensePlate(*s)
	}
	return oc
}

// SetCustomerName sets the "customer_name" field.
func (oc *OrderCreate) SetCustomerName(s string) *OrderCreate {
	oc.mutation.SetCustomerName(s)
	return oc
}

// SetNillableCustomerName sets the "customer_name" field if the given value is not nil.
func (oc *OrderCreate) SetNillableCustomerName(s *string) *OrderCreate {
	if s != nil {
		oc.SetCustomerName(*s)
	}
	return oc
}

// SetStationID sets the "station_id" field.
func (oc *OrderCreate) SetStationID(i int64) *OrderCreate {
	oc.mutation.SetStationID(i)
	return oc
}

// SetStatus sets the "status" field.
func (oc *OrderCreate) SetStatus(s string) *OrderCreate {
	oc.mutation.SetStatus(s)
	return oc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (oc *OrderCreate) SetNillableStatus(s *string) *OrderCreate {
	if s != nil {
		oc.SetStatus(*s)
	}
	return oc
}

// SetTotalAmount sets the "total_amount" field.
func (oc *OrderCreate) SetTotalAmount(f float64) *OrderCreate {
	oc.mutation.SetTotalAmount(f)
	return oc
}

// SetNillableTotalAmount sets the "total_amount" field if the given value is not nil.
func (oc *OrderCreate) SetNillableTotalAmount(f *float64) *OrderCreate {
	if f != nil {
		oc.SetTotalAmount(*f)
	}
	return oc
}

// SetDiscountAmount sets the "discount_amount" field.
func (oc *OrderCreate) SetDiscountAmount(f float64) *OrderCreate {
	oc.mutation.SetDiscountAmount(f)
	return oc
}

// SetNillableDiscountAmount sets the "discount_amount" field if the given value is not nil.
func (oc *OrderCreate) SetNillableDiscountAmount(f *float64) *OrderCreate {
	if f != nil {
		oc.SetDiscountAmount(*f)
	}
	return oc
}

// SetFinalAmount sets the "final_amount" field.
func (oc *OrderCreate) SetFinalAmount(f float64) *OrderCreate {
	oc.mutation.SetFinalAmount(f)
	return oc
}

// SetNillableFinalAmount sets the "final_amount" field if the given value is not nil.
func (oc *OrderCreate) SetNillableFinalAmount(f *float64) *OrderCreate {
	if f != nil {
		oc.SetFinalAmount(*f)
	}
	return oc
}

// SetTaxAmount sets the "tax_amount" field.
func (oc *OrderCreate) SetTaxAmount(f float64) *OrderCreate {
	oc.mutation.SetTaxAmount(f)
	return oc
}

// SetNillableTaxAmount sets the "tax_amount" field if the given value is not nil.
func (oc *OrderCreate) SetNillableTaxAmount(f *float64) *OrderCreate {
	if f != nil {
		oc.SetTaxAmount(*f)
	}
	return oc
}

// SetPaidAmount sets the "paid_amount" field.
func (oc *OrderCreate) SetPaidAmount(f float64) *OrderCreate {
	oc.mutation.SetPaidAmount(f)
	return oc
}

// SetNillablePaidAmount sets the "paid_amount" field if the given value is not nil.
func (oc *OrderCreate) SetNillablePaidAmount(f *float64) *OrderCreate {
	if f != nil {
		oc.SetPaidAmount(*f)
	}
	return oc
}

// SetMetadata sets the "metadata" field.
func (oc *OrderCreate) SetMetadata(m map[string]interface{}) *OrderCreate {
	oc.mutation.SetMetadata(m)
	return oc
}

// SetCreatedAt sets the "created_at" field.
func (oc *OrderCreate) SetCreatedAt(t time.Time) *OrderCreate {
	oc.mutation.SetCreatedAt(t)
	return oc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (oc *OrderCreate) SetNillableCreatedAt(t *time.Time) *OrderCreate {
	if t != nil {
		oc.SetCreatedAt(*t)
	}
	return oc
}

// SetUpdatedAt sets the "updated_at" field.
func (oc *OrderCreate) SetUpdatedAt(t time.Time) *OrderCreate {
	oc.mutation.SetUpdatedAt(t)
	return oc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (oc *OrderCreate) SetNillableUpdatedAt(t *time.Time) *OrderCreate {
	if t != nil {
		oc.SetUpdatedAt(*t)
	}
	return oc
}

// SetCompletedAt sets the "completed_at" field.
func (oc *OrderCreate) SetCompletedAt(t time.Time) *OrderCreate {
	oc.mutation.SetCompletedAt(t)
	return oc
}

// SetNillableCompletedAt sets the "completed_at" field if the given value is not nil.
func (oc *OrderCreate) SetNillableCompletedAt(t *time.Time) *OrderCreate {
	if t != nil {
		oc.SetCompletedAt(*t)
	}
	return oc
}

// SetCancelledAt sets the "cancelled_at" field.
func (oc *OrderCreate) SetCancelledAt(t time.Time) *OrderCreate {
	oc.mutation.SetCancelledAt(t)
	return oc
}

// SetNillableCancelledAt sets the "cancelled_at" field if the given value is not nil.
func (oc *OrderCreate) SetNillableCancelledAt(t *time.Time) *OrderCreate {
	if t != nil {
		oc.SetCancelledAt(*t)
	}
	return oc
}

// SetEmployeeNo sets the "employee_no" field.
func (oc *OrderCreate) SetEmployeeNo(s string) *OrderCreate {
	oc.mutation.SetEmployeeNo(s)
	return oc
}

// SetNillableEmployeeNo sets the "employee_no" field if the given value is not nil.
func (oc *OrderCreate) SetNillableEmployeeNo(s *string) *OrderCreate {
	if s != nil {
		oc.SetEmployeeNo(*s)
	}
	return oc
}

// SetStaffCardID sets the "staff_card_id" field.
func (oc *OrderCreate) SetStaffCardID(i int64) *OrderCreate {
	oc.mutation.SetStaffCardID(i)
	return oc
}

// SetNillableStaffCardID sets the "staff_card_id" field if the given value is not nil.
func (oc *OrderCreate) SetNillableStaffCardID(i *int64) *OrderCreate {
	if i != nil {
		oc.SetStaffCardID(*i)
	}
	return oc
}

// SetID sets the "id" field.
func (oc *OrderCreate) SetID(s string) *OrderCreate {
	oc.mutation.SetID(s)
	return oc
}

// SetFuelTransactionLinkID sets the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity by ID.
func (oc *OrderCreate) SetFuelTransactionLinkID(id string) *OrderCreate {
	oc.mutation.SetFuelTransactionLinkID(id)
	return oc
}

// SetNillableFuelTransactionLinkID sets the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity by ID if the given value is not nil.
func (oc *OrderCreate) SetNillableFuelTransactionLinkID(id *string) *OrderCreate {
	if id != nil {
		oc = oc.SetFuelTransactionLinkID(*id)
	}
	return oc
}

// SetFuelTransactionLink sets the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity.
func (oc *OrderCreate) SetFuelTransactionLink(f *FuelTransactionOrderLink) *OrderCreate {
	return oc.SetFuelTransactionLinkID(f.ID)
}

// Mutation returns the OrderMutation object of the builder.
func (oc *OrderCreate) Mutation() *OrderMutation {
	return oc.mutation
}

// Save creates the Order in the database.
func (oc *OrderCreate) Save(ctx context.Context) (*Order, error) {
	oc.defaults()
	return withHooks(ctx, oc.sqlSave, oc.mutation, oc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (oc *OrderCreate) SaveX(ctx context.Context) *Order {
	v, err := oc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (oc *OrderCreate) Exec(ctx context.Context) error {
	_, err := oc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (oc *OrderCreate) ExecX(ctx context.Context) {
	if err := oc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (oc *OrderCreate) defaults() {
	if _, ok := oc.mutation.Status(); !ok {
		v := order.DefaultStatus
		oc.mutation.SetStatus(v)
	}
	if _, ok := oc.mutation.TotalAmount(); !ok {
		v := order.DefaultTotalAmount
		oc.mutation.SetTotalAmount(v)
	}
	if _, ok := oc.mutation.DiscountAmount(); !ok {
		v := order.DefaultDiscountAmount
		oc.mutation.SetDiscountAmount(v)
	}
	if _, ok := oc.mutation.FinalAmount(); !ok {
		v := order.DefaultFinalAmount
		oc.mutation.SetFinalAmount(v)
	}
	if _, ok := oc.mutation.TaxAmount(); !ok {
		v := order.DefaultTaxAmount
		oc.mutation.SetTaxAmount(v)
	}
	if _, ok := oc.mutation.PaidAmount(); !ok {
		v := order.DefaultPaidAmount
		oc.mutation.SetPaidAmount(v)
	}
	if _, ok := oc.mutation.CreatedAt(); !ok {
		v := order.DefaultCreatedAt()
		oc.mutation.SetCreatedAt(v)
	}
	if _, ok := oc.mutation.UpdatedAt(); !ok {
		v := order.DefaultUpdatedAt()
		oc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (oc *OrderCreate) check() error {
	if _, ok := oc.mutation.OrderNumber(); !ok {
		return &ValidationError{Name: "order_number", err: errors.New(`order_service: missing required field "Order.order_number"`)}
	}
	if v, ok := oc.mutation.OrderNumber(); ok {
		if err := order.OrderNumberValidator(v); err != nil {
			return &ValidationError{Name: "order_number", err: fmt.Errorf(`order_service: validator failed for field "Order.order_number": %w`, err)}
		}
	}
	if v, ok := oc.mutation.CustomerPhone(); ok {
		if err := order.CustomerPhoneValidator(v); err != nil {
			return &ValidationError{Name: "customer_phone", err: fmt.Errorf(`order_service: validator failed for field "Order.customer_phone": %w`, err)}
		}
	}
	if v, ok := oc.mutation.LicensePlate(); ok {
		if err := order.LicensePlateValidator(v); err != nil {
			return &ValidationError{Name: "license_plate", err: fmt.Errorf(`order_service: validator failed for field "Order.license_plate": %w`, err)}
		}
	}
	if v, ok := oc.mutation.CustomerName(); ok {
		if err := order.CustomerNameValidator(v); err != nil {
			return &ValidationError{Name: "customer_name", err: fmt.Errorf(`order_service: validator failed for field "Order.customer_name": %w`, err)}
		}
	}
	if _, ok := oc.mutation.StationID(); !ok {
		return &ValidationError{Name: "station_id", err: errors.New(`order_service: missing required field "Order.station_id"`)}
	}
	if _, ok := oc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`order_service: missing required field "Order.status"`)}
	}
	if v, ok := oc.mutation.Status(); ok {
		if err := order.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`order_service: validator failed for field "Order.status": %w`, err)}
		}
	}
	if _, ok := oc.mutation.TotalAmount(); !ok {
		return &ValidationError{Name: "total_amount", err: errors.New(`order_service: missing required field "Order.total_amount"`)}
	}
	if _, ok := oc.mutation.DiscountAmount(); !ok {
		return &ValidationError{Name: "discount_amount", err: errors.New(`order_service: missing required field "Order.discount_amount"`)}
	}
	if _, ok := oc.mutation.FinalAmount(); !ok {
		return &ValidationError{Name: "final_amount", err: errors.New(`order_service: missing required field "Order.final_amount"`)}
	}
	if _, ok := oc.mutation.TaxAmount(); !ok {
		return &ValidationError{Name: "tax_amount", err: errors.New(`order_service: missing required field "Order.tax_amount"`)}
	}
	if _, ok := oc.mutation.PaidAmount(); !ok {
		return &ValidationError{Name: "paid_amount", err: errors.New(`order_service: missing required field "Order.paid_amount"`)}
	}
	if _, ok := oc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`order_service: missing required field "Order.created_at"`)}
	}
	if _, ok := oc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`order_service: missing required field "Order.updated_at"`)}
	}
	if v, ok := oc.mutation.EmployeeNo(); ok {
		if err := order.EmployeeNoValidator(v); err != nil {
			return &ValidationError{Name: "employee_no", err: fmt.Errorf(`order_service: validator failed for field "Order.employee_no": %w`, err)}
		}
	}
	if v, ok := oc.mutation.ID(); ok {
		if err := order.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`order_service: validator failed for field "Order.id": %w`, err)}
		}
	}
	return nil
}

func (oc *OrderCreate) sqlSave(ctx context.Context) (*Order, error) {
	if err := oc.check(); err != nil {
		return nil, err
	}
	_node, _spec := oc.createSpec()
	if err := sqlgraph.CreateNode(ctx, oc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Order.ID type: %T", _spec.ID.Value)
		}
	}
	oc.mutation.id = &_node.ID
	oc.mutation.done = true
	return _node, nil
}

func (oc *OrderCreate) createSpec() (*Order, *sqlgraph.CreateSpec) {
	var (
		_node = &Order{config: oc.config}
		_spec = sqlgraph.NewCreateSpec(order.Table, sqlgraph.NewFieldSpec(order.FieldID, field.TypeString))
	)
	if id, ok := oc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := oc.mutation.OrderNumber(); ok {
		_spec.SetField(order.FieldOrderNumber, field.TypeString, value)
		_node.OrderNumber = value
	}
	if value, ok := oc.mutation.CustomerID(); ok {
		_spec.SetField(order.FieldCustomerID, field.TypeInt64, value)
		_node.CustomerID = &value
	}
	if value, ok := oc.mutation.CustomerPhone(); ok {
		_spec.SetField(order.FieldCustomerPhone, field.TypeString, value)
		_node.CustomerPhone = &value
	}
	if value, ok := oc.mutation.LicensePlate(); ok {
		_spec.SetField(order.FieldLicensePlate, field.TypeString, value)
		_node.LicensePlate = &value
	}
	if value, ok := oc.mutation.CustomerName(); ok {
		_spec.SetField(order.FieldCustomerName, field.TypeString, value)
		_node.CustomerName = &value
	}
	if value, ok := oc.mutation.StationID(); ok {
		_spec.SetField(order.FieldStationID, field.TypeInt64, value)
		_node.StationID = value
	}
	if value, ok := oc.mutation.Status(); ok {
		_spec.SetField(order.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := oc.mutation.TotalAmount(); ok {
		_spec.SetField(order.FieldTotalAmount, field.TypeFloat64, value)
		_node.TotalAmount = value
	}
	if value, ok := oc.mutation.DiscountAmount(); ok {
		_spec.SetField(order.FieldDiscountAmount, field.TypeFloat64, value)
		_node.DiscountAmount = value
	}
	if value, ok := oc.mutation.FinalAmount(); ok {
		_spec.SetField(order.FieldFinalAmount, field.TypeFloat64, value)
		_node.FinalAmount = value
	}
	if value, ok := oc.mutation.TaxAmount(); ok {
		_spec.SetField(order.FieldTaxAmount, field.TypeFloat64, value)
		_node.TaxAmount = value
	}
	if value, ok := oc.mutation.PaidAmount(); ok {
		_spec.SetField(order.FieldPaidAmount, field.TypeFloat64, value)
		_node.PaidAmount = value
	}
	if value, ok := oc.mutation.Metadata(); ok {
		_spec.SetField(order.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := oc.mutation.CreatedAt(); ok {
		_spec.SetField(order.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := oc.mutation.UpdatedAt(); ok {
		_spec.SetField(order.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := oc.mutation.CompletedAt(); ok {
		_spec.SetField(order.FieldCompletedAt, field.TypeTime, value)
		_node.CompletedAt = &value
	}
	if value, ok := oc.mutation.CancelledAt(); ok {
		_spec.SetField(order.FieldCancelledAt, field.TypeTime, value)
		_node.CancelledAt = &value
	}
	if value, ok := oc.mutation.EmployeeNo(); ok {
		_spec.SetField(order.FieldEmployeeNo, field.TypeString, value)
		_node.EmployeeNo = &value
	}
	if value, ok := oc.mutation.StaffCardID(); ok {
		_spec.SetField(order.FieldStaffCardID, field.TypeInt64, value)
		_node.StaffCardID = &value
	}
	if nodes := oc.mutation.FuelTransactionLinkIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   order.FuelTransactionLinkTable,
			Columns: []string{order.FuelTransactionLinkColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OrderCreateBulk is the builder for creating many Order entities in bulk.
type OrderCreateBulk struct {
	config
	err      error
	builders []*OrderCreate
}

// Save creates the Order entities in the database.
func (ocb *OrderCreateBulk) Save(ctx context.Context) ([]*Order, error) {
	if ocb.err != nil {
		return nil, ocb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ocb.builders))
	nodes := make([]*Order, len(ocb.builders))
	mutators := make([]Mutator, len(ocb.builders))
	for i := range ocb.builders {
		func(i int, root context.Context) {
			builder := ocb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*OrderMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ocb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ocb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ocb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ocb *OrderCreateBulk) SaveX(ctx context.Context) []*Order {
	v, err := ocb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ocb *OrderCreateBulk) Exec(ctx context.Context) error {
	_, err := ocb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ocb *OrderCreateBulk) ExecX(ctx context.Context) {
	if err := ocb.Exec(ctx); err != nil {
		panic(err)
	}
}
