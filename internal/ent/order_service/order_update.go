// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

// OrderUpdate is the builder for updating Order entities.
type OrderUpdate struct {
	config
	hooks    []Hook
	mutation *OrderMutation
}

// Where appends a list predicates to the OrderUpdate builder.
func (ou *OrderUpdate) Where(ps ...predicate.Order) *OrderUpdate {
	ou.mutation.Where(ps...)
	return ou
}

// SetOrderNumber sets the "order_number" field.
func (ou *OrderUpdate) SetOrderNumber(s string) *OrderUpdate {
	ou.mutation.SetOrderNumber(s)
	return ou
}

// SetNillableOrderNumber sets the "order_number" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableOrderNumber(s *string) *OrderUpdate {
	if s != nil {
		ou.SetOrderNumber(*s)
	}
	return ou
}

// SetCustomerID sets the "customer_id" field.
func (ou *OrderUpdate) SetCustomerID(i int64) *OrderUpdate {
	ou.mutation.ResetCustomerID()
	ou.mutation.SetCustomerID(i)
	return ou
}

// SetNillableCustomerID sets the "customer_id" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableCustomerID(i *int64) *OrderUpdate {
	if i != nil {
		ou.SetCustomerID(*i)
	}
	return ou
}

// AddCustomerID adds i to the "customer_id" field.
func (ou *OrderUpdate) AddCustomerID(i int64) *OrderUpdate {
	ou.mutation.AddCustomerID(i)
	return ou
}

// ClearCustomerID clears the value of the "customer_id" field.
func (ou *OrderUpdate) ClearCustomerID() *OrderUpdate {
	ou.mutation.ClearCustomerID()
	return ou
}

// SetCustomerPhone sets the "customer_phone" field.
func (ou *OrderUpdate) SetCustomerPhone(s string) *OrderUpdate {
	ou.mutation.SetCustomerPhone(s)
	return ou
}

// SetNillableCustomerPhone sets the "customer_phone" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableCustomerPhone(s *string) *OrderUpdate {
	if s != nil {
		ou.SetCustomerPhone(*s)
	}
	return ou
}

// ClearCustomerPhone clears the value of the "customer_phone" field.
func (ou *OrderUpdate) ClearCustomerPhone() *OrderUpdate {
	ou.mutation.ClearCustomerPhone()
	return ou
}

// SetLicensePlate sets the "license_plate" field.
func (ou *OrderUpdate) SetLicensePlate(s string) *OrderUpdate {
	ou.mutation.SetLicensePlate(s)
	return ou
}

// SetNillableLicensePlate sets the "license_plate" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableLicensePlate(s *string) *OrderUpdate {
	if s != nil {
		ou.SetLicensePlate(*s)
	}
	return ou
}

// ClearLicensePlate clears the value of the "license_plate" field.
func (ou *OrderUpdate) ClearLicensePlate() *OrderUpdate {
	ou.mutation.ClearLicensePlate()
	return ou
}

// SetCustomerName sets the "customer_name" field.
func (ou *OrderUpdate) SetCustomerName(s string) *OrderUpdate {
	ou.mutation.SetCustomerName(s)
	return ou
}

// SetNillableCustomerName sets the "customer_name" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableCustomerName(s *string) *OrderUpdate {
	if s != nil {
		ou.SetCustomerName(*s)
	}
	return ou
}

// ClearCustomerName clears the value of the "customer_name" field.
func (ou *OrderUpdate) ClearCustomerName() *OrderUpdate {
	ou.mutation.ClearCustomerName()
	return ou
}

// SetStationID sets the "station_id" field.
func (ou *OrderUpdate) SetStationID(i int64) *OrderUpdate {
	ou.mutation.ResetStationID()
	ou.mutation.SetStationID(i)
	return ou
}

// SetNillableStationID sets the "station_id" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableStationID(i *int64) *OrderUpdate {
	if i != nil {
		ou.SetStationID(*i)
	}
	return ou
}

// AddStationID adds i to the "station_id" field.
func (ou *OrderUpdate) AddStationID(i int64) *OrderUpdate {
	ou.mutation.AddStationID(i)
	return ou
}

// SetStatus sets the "status" field.
func (ou *OrderUpdate) SetStatus(s string) *OrderUpdate {
	ou.mutation.SetStatus(s)
	return ou
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableStatus(s *string) *OrderUpdate {
	if s != nil {
		ou.SetStatus(*s)
	}
	return ou
}

// SetTotalAmount sets the "total_amount" field.
func (ou *OrderUpdate) SetTotalAmount(f float64) *OrderUpdate {
	ou.mutation.ResetTotalAmount()
	ou.mutation.SetTotalAmount(f)
	return ou
}

// SetNillableTotalAmount sets the "total_amount" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableTotalAmount(f *float64) *OrderUpdate {
	if f != nil {
		ou.SetTotalAmount(*f)
	}
	return ou
}

// AddTotalAmount adds f to the "total_amount" field.
func (ou *OrderUpdate) AddTotalAmount(f float64) *OrderUpdate {
	ou.mutation.AddTotalAmount(f)
	return ou
}

// SetDiscountAmount sets the "discount_amount" field.
func (ou *OrderUpdate) SetDiscountAmount(f float64) *OrderUpdate {
	ou.mutation.ResetDiscountAmount()
	ou.mutation.SetDiscountAmount(f)
	return ou
}

// SetNillableDiscountAmount sets the "discount_amount" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableDiscountAmount(f *float64) *OrderUpdate {
	if f != nil {
		ou.SetDiscountAmount(*f)
	}
	return ou
}

// AddDiscountAmount adds f to the "discount_amount" field.
func (ou *OrderUpdate) AddDiscountAmount(f float64) *OrderUpdate {
	ou.mutation.AddDiscountAmount(f)
	return ou
}

// SetFinalAmount sets the "final_amount" field.
func (ou *OrderUpdate) SetFinalAmount(f float64) *OrderUpdate {
	ou.mutation.ResetFinalAmount()
	ou.mutation.SetFinalAmount(f)
	return ou
}

// SetNillableFinalAmount sets the "final_amount" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableFinalAmount(f *float64) *OrderUpdate {
	if f != nil {
		ou.SetFinalAmount(*f)
	}
	return ou
}

// AddFinalAmount adds f to the "final_amount" field.
func (ou *OrderUpdate) AddFinalAmount(f float64) *OrderUpdate {
	ou.mutation.AddFinalAmount(f)
	return ou
}

// SetTaxAmount sets the "tax_amount" field.
func (ou *OrderUpdate) SetTaxAmount(f float64) *OrderUpdate {
	ou.mutation.ResetTaxAmount()
	ou.mutation.SetTaxAmount(f)
	return ou
}

// SetNillableTaxAmount sets the "tax_amount" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableTaxAmount(f *float64) *OrderUpdate {
	if f != nil {
		ou.SetTaxAmount(*f)
	}
	return ou
}

// AddTaxAmount adds f to the "tax_amount" field.
func (ou *OrderUpdate) AddTaxAmount(f float64) *OrderUpdate {
	ou.mutation.AddTaxAmount(f)
	return ou
}

// SetPaidAmount sets the "paid_amount" field.
func (ou *OrderUpdate) SetPaidAmount(f float64) *OrderUpdate {
	ou.mutation.ResetPaidAmount()
	ou.mutation.SetPaidAmount(f)
	return ou
}

// SetNillablePaidAmount sets the "paid_amount" field if the given value is not nil.
func (ou *OrderUpdate) SetNillablePaidAmount(f *float64) *OrderUpdate {
	if f != nil {
		ou.SetPaidAmount(*f)
	}
	return ou
}

// AddPaidAmount adds f to the "paid_amount" field.
func (ou *OrderUpdate) AddPaidAmount(f float64) *OrderUpdate {
	ou.mutation.AddPaidAmount(f)
	return ou
}

// SetMetadata sets the "metadata" field.
func (ou *OrderUpdate) SetMetadata(m map[string]interface{}) *OrderUpdate {
	ou.mutation.SetMetadata(m)
	return ou
}

// ClearMetadata clears the value of the "metadata" field.
func (ou *OrderUpdate) ClearMetadata() *OrderUpdate {
	ou.mutation.ClearMetadata()
	return ou
}

// SetUpdatedAt sets the "updated_at" field.
func (ou *OrderUpdate) SetUpdatedAt(t time.Time) *OrderUpdate {
	ou.mutation.SetUpdatedAt(t)
	return ou
}

// SetCompletedAt sets the "completed_at" field.
func (ou *OrderUpdate) SetCompletedAt(t time.Time) *OrderUpdate {
	ou.mutation.SetCompletedAt(t)
	return ou
}

// SetNillableCompletedAt sets the "completed_at" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableCompletedAt(t *time.Time) *OrderUpdate {
	if t != nil {
		ou.SetCompletedAt(*t)
	}
	return ou
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (ou *OrderUpdate) ClearCompletedAt() *OrderUpdate {
	ou.mutation.ClearCompletedAt()
	return ou
}

// SetCancelledAt sets the "cancelled_at" field.
func (ou *OrderUpdate) SetCancelledAt(t time.Time) *OrderUpdate {
	ou.mutation.SetCancelledAt(t)
	return ou
}

// SetNillableCancelledAt sets the "cancelled_at" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableCancelledAt(t *time.Time) *OrderUpdate {
	if t != nil {
		ou.SetCancelledAt(*t)
	}
	return ou
}

// ClearCancelledAt clears the value of the "cancelled_at" field.
func (ou *OrderUpdate) ClearCancelledAt() *OrderUpdate {
	ou.mutation.ClearCancelledAt()
	return ou
}

// SetEmployeeNo sets the "employee_no" field.
func (ou *OrderUpdate) SetEmployeeNo(s string) *OrderUpdate {
	ou.mutation.SetEmployeeNo(s)
	return ou
}

// SetNillableEmployeeNo sets the "employee_no" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableEmployeeNo(s *string) *OrderUpdate {
	if s != nil {
		ou.SetEmployeeNo(*s)
	}
	return ou
}

// ClearEmployeeNo clears the value of the "employee_no" field.
func (ou *OrderUpdate) ClearEmployeeNo() *OrderUpdate {
	ou.mutation.ClearEmployeeNo()
	return ou
}

// SetStaffCardID sets the "staff_card_id" field.
func (ou *OrderUpdate) SetStaffCardID(i int64) *OrderUpdate {
	ou.mutation.ResetStaffCardID()
	ou.mutation.SetStaffCardID(i)
	return ou
}

// SetNillableStaffCardID sets the "staff_card_id" field if the given value is not nil.
func (ou *OrderUpdate) SetNillableStaffCardID(i *int64) *OrderUpdate {
	if i != nil {
		ou.SetStaffCardID(*i)
	}
	return ou
}

// AddStaffCardID adds i to the "staff_card_id" field.
func (ou *OrderUpdate) AddStaffCardID(i int64) *OrderUpdate {
	ou.mutation.AddStaffCardID(i)
	return ou
}

// ClearStaffCardID clears the value of the "staff_card_id" field.
func (ou *OrderUpdate) ClearStaffCardID() *OrderUpdate {
	ou.mutation.ClearStaffCardID()
	return ou
}

// SetFuelTransactionLinkID sets the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity by ID.
func (ou *OrderUpdate) SetFuelTransactionLinkID(id string) *OrderUpdate {
	ou.mutation.SetFuelTransactionLinkID(id)
	return ou
}

// SetNillableFuelTransactionLinkID sets the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity by ID if the given value is not nil.
func (ou *OrderUpdate) SetNillableFuelTransactionLinkID(id *string) *OrderUpdate {
	if id != nil {
		ou = ou.SetFuelTransactionLinkID(*id)
	}
	return ou
}

// SetFuelTransactionLink sets the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity.
func (ou *OrderUpdate) SetFuelTransactionLink(f *FuelTransactionOrderLink) *OrderUpdate {
	return ou.SetFuelTransactionLinkID(f.ID)
}

// Mutation returns the OrderMutation object of the builder.
func (ou *OrderUpdate) Mutation() *OrderMutation {
	return ou.mutation
}

// ClearFuelTransactionLink clears the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity.
func (ou *OrderUpdate) ClearFuelTransactionLink() *OrderUpdate {
	ou.mutation.ClearFuelTransactionLink()
	return ou
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ou *OrderUpdate) Save(ctx context.Context) (int, error) {
	ou.defaults()
	return withHooks(ctx, ou.sqlSave, ou.mutation, ou.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ou *OrderUpdate) SaveX(ctx context.Context) int {
	affected, err := ou.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ou *OrderUpdate) Exec(ctx context.Context) error {
	_, err := ou.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ou *OrderUpdate) ExecX(ctx context.Context) {
	if err := ou.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ou *OrderUpdate) defaults() {
	if _, ok := ou.mutation.UpdatedAt(); !ok {
		v := order.UpdateDefaultUpdatedAt()
		ou.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ou *OrderUpdate) check() error {
	if v, ok := ou.mutation.OrderNumber(); ok {
		if err := order.OrderNumberValidator(v); err != nil {
			return &ValidationError{Name: "order_number", err: fmt.Errorf(`order_service: validator failed for field "Order.order_number": %w`, err)}
		}
	}
	if v, ok := ou.mutation.CustomerPhone(); ok {
		if err := order.CustomerPhoneValidator(v); err != nil {
			return &ValidationError{Name: "customer_phone", err: fmt.Errorf(`order_service: validator failed for field "Order.customer_phone": %w`, err)}
		}
	}
	if v, ok := ou.mutation.LicensePlate(); ok {
		if err := order.LicensePlateValidator(v); err != nil {
			return &ValidationError{Name: "license_plate", err: fmt.Errorf(`order_service: validator failed for field "Order.license_plate": %w`, err)}
		}
	}
	if v, ok := ou.mutation.CustomerName(); ok {
		if err := order.CustomerNameValidator(v); err != nil {
			return &ValidationError{Name: "customer_name", err: fmt.Errorf(`order_service: validator failed for field "Order.customer_name": %w`, err)}
		}
	}
	if v, ok := ou.mutation.Status(); ok {
		if err := order.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`order_service: validator failed for field "Order.status": %w`, err)}
		}
	}
	if v, ok := ou.mutation.EmployeeNo(); ok {
		if err := order.EmployeeNoValidator(v); err != nil {
			return &ValidationError{Name: "employee_no", err: fmt.Errorf(`order_service: validator failed for field "Order.employee_no": %w`, err)}
		}
	}
	return nil
}

func (ou *OrderUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ou.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(order.Table, order.Columns, sqlgraph.NewFieldSpec(order.FieldID, field.TypeString))
	if ps := ou.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ou.mutation.OrderNumber(); ok {
		_spec.SetField(order.FieldOrderNumber, field.TypeString, value)
	}
	if value, ok := ou.mutation.CustomerID(); ok {
		_spec.SetField(order.FieldCustomerID, field.TypeInt64, value)
	}
	if value, ok := ou.mutation.AddedCustomerID(); ok {
		_spec.AddField(order.FieldCustomerID, field.TypeInt64, value)
	}
	if ou.mutation.CustomerIDCleared() {
		_spec.ClearField(order.FieldCustomerID, field.TypeInt64)
	}
	if value, ok := ou.mutation.CustomerPhone(); ok {
		_spec.SetField(order.FieldCustomerPhone, field.TypeString, value)
	}
	if ou.mutation.CustomerPhoneCleared() {
		_spec.ClearField(order.FieldCustomerPhone, field.TypeString)
	}
	if value, ok := ou.mutation.LicensePlate(); ok {
		_spec.SetField(order.FieldLicensePlate, field.TypeString, value)
	}
	if ou.mutation.LicensePlateCleared() {
		_spec.ClearField(order.FieldLicensePlate, field.TypeString)
	}
	if value, ok := ou.mutation.CustomerName(); ok {
		_spec.SetField(order.FieldCustomerName, field.TypeString, value)
	}
	if ou.mutation.CustomerNameCleared() {
		_spec.ClearField(order.FieldCustomerName, field.TypeString)
	}
	if value, ok := ou.mutation.StationID(); ok {
		_spec.SetField(order.FieldStationID, field.TypeInt64, value)
	}
	if value, ok := ou.mutation.AddedStationID(); ok {
		_spec.AddField(order.FieldStationID, field.TypeInt64, value)
	}
	if value, ok := ou.mutation.Status(); ok {
		_spec.SetField(order.FieldStatus, field.TypeString, value)
	}
	if value, ok := ou.mutation.TotalAmount(); ok {
		_spec.SetField(order.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := ou.mutation.AddedTotalAmount(); ok {
		_spec.AddField(order.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := ou.mutation.DiscountAmount(); ok {
		_spec.SetField(order.FieldDiscountAmount, field.TypeFloat64, value)
	}
	if value, ok := ou.mutation.AddedDiscountAmount(); ok {
		_spec.AddField(order.FieldDiscountAmount, field.TypeFloat64, value)
	}
	if value, ok := ou.mutation.FinalAmount(); ok {
		_spec.SetField(order.FieldFinalAmount, field.TypeFloat64, value)
	}
	if value, ok := ou.mutation.AddedFinalAmount(); ok {
		_spec.AddField(order.FieldFinalAmount, field.TypeFloat64, value)
	}
	if value, ok := ou.mutation.TaxAmount(); ok {
		_spec.SetField(order.FieldTaxAmount, field.TypeFloat64, value)
	}
	if value, ok := ou.mutation.AddedTaxAmount(); ok {
		_spec.AddField(order.FieldTaxAmount, field.TypeFloat64, value)
	}
	if value, ok := ou.mutation.PaidAmount(); ok {
		_spec.SetField(order.FieldPaidAmount, field.TypeFloat64, value)
	}
	if value, ok := ou.mutation.AddedPaidAmount(); ok {
		_spec.AddField(order.FieldPaidAmount, field.TypeFloat64, value)
	}
	if value, ok := ou.mutation.Metadata(); ok {
		_spec.SetField(order.FieldMetadata, field.TypeJSON, value)
	}
	if ou.mutation.MetadataCleared() {
		_spec.ClearField(order.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ou.mutation.UpdatedAt(); ok {
		_spec.SetField(order.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ou.mutation.CompletedAt(); ok {
		_spec.SetField(order.FieldCompletedAt, field.TypeTime, value)
	}
	if ou.mutation.CompletedAtCleared() {
		_spec.ClearField(order.FieldCompletedAt, field.TypeTime)
	}
	if value, ok := ou.mutation.CancelledAt(); ok {
		_spec.SetField(order.FieldCancelledAt, field.TypeTime, value)
	}
	if ou.mutation.CancelledAtCleared() {
		_spec.ClearField(order.FieldCancelledAt, field.TypeTime)
	}
	if value, ok := ou.mutation.EmployeeNo(); ok {
		_spec.SetField(order.FieldEmployeeNo, field.TypeString, value)
	}
	if ou.mutation.EmployeeNoCleared() {
		_spec.ClearField(order.FieldEmployeeNo, field.TypeString)
	}
	if value, ok := ou.mutation.StaffCardID(); ok {
		_spec.SetField(order.FieldStaffCardID, field.TypeInt64, value)
	}
	if value, ok := ou.mutation.AddedStaffCardID(); ok {
		_spec.AddField(order.FieldStaffCardID, field.TypeInt64, value)
	}
	if ou.mutation.StaffCardIDCleared() {
		_spec.ClearField(order.FieldStaffCardID, field.TypeInt64)
	}
	if ou.mutation.FuelTransactionLinkCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   order.FuelTransactionLinkTable,
			Columns: []string{order.FuelTransactionLinkColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.FuelTransactionLinkIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   order.FuelTransactionLinkTable,
			Columns: []string{order.FuelTransactionLinkColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ou.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{order.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ou.mutation.done = true
	return n, nil
}

// OrderUpdateOne is the builder for updating a single Order entity.
type OrderUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *OrderMutation
}

// SetOrderNumber sets the "order_number" field.
func (ouo *OrderUpdateOne) SetOrderNumber(s string) *OrderUpdateOne {
	ouo.mutation.SetOrderNumber(s)
	return ouo
}

// SetNillableOrderNumber sets the "order_number" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableOrderNumber(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetOrderNumber(*s)
	}
	return ouo
}

// SetCustomerID sets the "customer_id" field.
func (ouo *OrderUpdateOne) SetCustomerID(i int64) *OrderUpdateOne {
	ouo.mutation.ResetCustomerID()
	ouo.mutation.SetCustomerID(i)
	return ouo
}

// SetNillableCustomerID sets the "customer_id" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableCustomerID(i *int64) *OrderUpdateOne {
	if i != nil {
		ouo.SetCustomerID(*i)
	}
	return ouo
}

// AddCustomerID adds i to the "customer_id" field.
func (ouo *OrderUpdateOne) AddCustomerID(i int64) *OrderUpdateOne {
	ouo.mutation.AddCustomerID(i)
	return ouo
}

// ClearCustomerID clears the value of the "customer_id" field.
func (ouo *OrderUpdateOne) ClearCustomerID() *OrderUpdateOne {
	ouo.mutation.ClearCustomerID()
	return ouo
}

// SetCustomerPhone sets the "customer_phone" field.
func (ouo *OrderUpdateOne) SetCustomerPhone(s string) *OrderUpdateOne {
	ouo.mutation.SetCustomerPhone(s)
	return ouo
}

// SetNillableCustomerPhone sets the "customer_phone" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableCustomerPhone(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetCustomerPhone(*s)
	}
	return ouo
}

// ClearCustomerPhone clears the value of the "customer_phone" field.
func (ouo *OrderUpdateOne) ClearCustomerPhone() *OrderUpdateOne {
	ouo.mutation.ClearCustomerPhone()
	return ouo
}

// SetLicensePlate sets the "license_plate" field.
func (ouo *OrderUpdateOne) SetLicensePlate(s string) *OrderUpdateOne {
	ouo.mutation.SetLicensePlate(s)
	return ouo
}

// SetNillableLicensePlate sets the "license_plate" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableLicensePlate(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetLicensePlate(*s)
	}
	return ouo
}

// ClearLicensePlate clears the value of the "license_plate" field.
func (ouo *OrderUpdateOne) ClearLicensePlate() *OrderUpdateOne {
	ouo.mutation.ClearLicensePlate()
	return ouo
}

// SetCustomerName sets the "customer_name" field.
func (ouo *OrderUpdateOne) SetCustomerName(s string) *OrderUpdateOne {
	ouo.mutation.SetCustomerName(s)
	return ouo
}

// SetNillableCustomerName sets the "customer_name" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableCustomerName(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetCustomerName(*s)
	}
	return ouo
}

// ClearCustomerName clears the value of the "customer_name" field.
func (ouo *OrderUpdateOne) ClearCustomerName() *OrderUpdateOne {
	ouo.mutation.ClearCustomerName()
	return ouo
}

// SetStationID sets the "station_id" field.
func (ouo *OrderUpdateOne) SetStationID(i int64) *OrderUpdateOne {
	ouo.mutation.ResetStationID()
	ouo.mutation.SetStationID(i)
	return ouo
}

// SetNillableStationID sets the "station_id" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableStationID(i *int64) *OrderUpdateOne {
	if i != nil {
		ouo.SetStationID(*i)
	}
	return ouo
}

// AddStationID adds i to the "station_id" field.
func (ouo *OrderUpdateOne) AddStationID(i int64) *OrderUpdateOne {
	ouo.mutation.AddStationID(i)
	return ouo
}

// SetStatus sets the "status" field.
func (ouo *OrderUpdateOne) SetStatus(s string) *OrderUpdateOne {
	ouo.mutation.SetStatus(s)
	return ouo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableStatus(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetStatus(*s)
	}
	return ouo
}

// SetTotalAmount sets the "total_amount" field.
func (ouo *OrderUpdateOne) SetTotalAmount(f float64) *OrderUpdateOne {
	ouo.mutation.ResetTotalAmount()
	ouo.mutation.SetTotalAmount(f)
	return ouo
}

// SetNillableTotalAmount sets the "total_amount" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableTotalAmount(f *float64) *OrderUpdateOne {
	if f != nil {
		ouo.SetTotalAmount(*f)
	}
	return ouo
}

// AddTotalAmount adds f to the "total_amount" field.
func (ouo *OrderUpdateOne) AddTotalAmount(f float64) *OrderUpdateOne {
	ouo.mutation.AddTotalAmount(f)
	return ouo
}

// SetDiscountAmount sets the "discount_amount" field.
func (ouo *OrderUpdateOne) SetDiscountAmount(f float64) *OrderUpdateOne {
	ouo.mutation.ResetDiscountAmount()
	ouo.mutation.SetDiscountAmount(f)
	return ouo
}

// SetNillableDiscountAmount sets the "discount_amount" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableDiscountAmount(f *float64) *OrderUpdateOne {
	if f != nil {
		ouo.SetDiscountAmount(*f)
	}
	return ouo
}

// AddDiscountAmount adds f to the "discount_amount" field.
func (ouo *OrderUpdateOne) AddDiscountAmount(f float64) *OrderUpdateOne {
	ouo.mutation.AddDiscountAmount(f)
	return ouo
}

// SetFinalAmount sets the "final_amount" field.
func (ouo *OrderUpdateOne) SetFinalAmount(f float64) *OrderUpdateOne {
	ouo.mutation.ResetFinalAmount()
	ouo.mutation.SetFinalAmount(f)
	return ouo
}

// SetNillableFinalAmount sets the "final_amount" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableFinalAmount(f *float64) *OrderUpdateOne {
	if f != nil {
		ouo.SetFinalAmount(*f)
	}
	return ouo
}

// AddFinalAmount adds f to the "final_amount" field.
func (ouo *OrderUpdateOne) AddFinalAmount(f float64) *OrderUpdateOne {
	ouo.mutation.AddFinalAmount(f)
	return ouo
}

// SetTaxAmount sets the "tax_amount" field.
func (ouo *OrderUpdateOne) SetTaxAmount(f float64) *OrderUpdateOne {
	ouo.mutation.ResetTaxAmount()
	ouo.mutation.SetTaxAmount(f)
	return ouo
}

// SetNillableTaxAmount sets the "tax_amount" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableTaxAmount(f *float64) *OrderUpdateOne {
	if f != nil {
		ouo.SetTaxAmount(*f)
	}
	return ouo
}

// AddTaxAmount adds f to the "tax_amount" field.
func (ouo *OrderUpdateOne) AddTaxAmount(f float64) *OrderUpdateOne {
	ouo.mutation.AddTaxAmount(f)
	return ouo
}

// SetPaidAmount sets the "paid_amount" field.
func (ouo *OrderUpdateOne) SetPaidAmount(f float64) *OrderUpdateOne {
	ouo.mutation.ResetPaidAmount()
	ouo.mutation.SetPaidAmount(f)
	return ouo
}

// SetNillablePaidAmount sets the "paid_amount" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillablePaidAmount(f *float64) *OrderUpdateOne {
	if f != nil {
		ouo.SetPaidAmount(*f)
	}
	return ouo
}

// AddPaidAmount adds f to the "paid_amount" field.
func (ouo *OrderUpdateOne) AddPaidAmount(f float64) *OrderUpdateOne {
	ouo.mutation.AddPaidAmount(f)
	return ouo
}

// SetMetadata sets the "metadata" field.
func (ouo *OrderUpdateOne) SetMetadata(m map[string]interface{}) *OrderUpdateOne {
	ouo.mutation.SetMetadata(m)
	return ouo
}

// ClearMetadata clears the value of the "metadata" field.
func (ouo *OrderUpdateOne) ClearMetadata() *OrderUpdateOne {
	ouo.mutation.ClearMetadata()
	return ouo
}

// SetUpdatedAt sets the "updated_at" field.
func (ouo *OrderUpdateOne) SetUpdatedAt(t time.Time) *OrderUpdateOne {
	ouo.mutation.SetUpdatedAt(t)
	return ouo
}

// SetCompletedAt sets the "completed_at" field.
func (ouo *OrderUpdateOne) SetCompletedAt(t time.Time) *OrderUpdateOne {
	ouo.mutation.SetCompletedAt(t)
	return ouo
}

// SetNillableCompletedAt sets the "completed_at" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableCompletedAt(t *time.Time) *OrderUpdateOne {
	if t != nil {
		ouo.SetCompletedAt(*t)
	}
	return ouo
}

// ClearCompletedAt clears the value of the "completed_at" field.
func (ouo *OrderUpdateOne) ClearCompletedAt() *OrderUpdateOne {
	ouo.mutation.ClearCompletedAt()
	return ouo
}

// SetCancelledAt sets the "cancelled_at" field.
func (ouo *OrderUpdateOne) SetCancelledAt(t time.Time) *OrderUpdateOne {
	ouo.mutation.SetCancelledAt(t)
	return ouo
}

// SetNillableCancelledAt sets the "cancelled_at" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableCancelledAt(t *time.Time) *OrderUpdateOne {
	if t != nil {
		ouo.SetCancelledAt(*t)
	}
	return ouo
}

// ClearCancelledAt clears the value of the "cancelled_at" field.
func (ouo *OrderUpdateOne) ClearCancelledAt() *OrderUpdateOne {
	ouo.mutation.ClearCancelledAt()
	return ouo
}

// SetEmployeeNo sets the "employee_no" field.
func (ouo *OrderUpdateOne) SetEmployeeNo(s string) *OrderUpdateOne {
	ouo.mutation.SetEmployeeNo(s)
	return ouo
}

// SetNillableEmployeeNo sets the "employee_no" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableEmployeeNo(s *string) *OrderUpdateOne {
	if s != nil {
		ouo.SetEmployeeNo(*s)
	}
	return ouo
}

// ClearEmployeeNo clears the value of the "employee_no" field.
func (ouo *OrderUpdateOne) ClearEmployeeNo() *OrderUpdateOne {
	ouo.mutation.ClearEmployeeNo()
	return ouo
}

// SetStaffCardID sets the "staff_card_id" field.
func (ouo *OrderUpdateOne) SetStaffCardID(i int64) *OrderUpdateOne {
	ouo.mutation.ResetStaffCardID()
	ouo.mutation.SetStaffCardID(i)
	return ouo
}

// SetNillableStaffCardID sets the "staff_card_id" field if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableStaffCardID(i *int64) *OrderUpdateOne {
	if i != nil {
		ouo.SetStaffCardID(*i)
	}
	return ouo
}

// AddStaffCardID adds i to the "staff_card_id" field.
func (ouo *OrderUpdateOne) AddStaffCardID(i int64) *OrderUpdateOne {
	ouo.mutation.AddStaffCardID(i)
	return ouo
}

// ClearStaffCardID clears the value of the "staff_card_id" field.
func (ouo *OrderUpdateOne) ClearStaffCardID() *OrderUpdateOne {
	ouo.mutation.ClearStaffCardID()
	return ouo
}

// SetFuelTransactionLinkID sets the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity by ID.
func (ouo *OrderUpdateOne) SetFuelTransactionLinkID(id string) *OrderUpdateOne {
	ouo.mutation.SetFuelTransactionLinkID(id)
	return ouo
}

// SetNillableFuelTransactionLinkID sets the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity by ID if the given value is not nil.
func (ouo *OrderUpdateOne) SetNillableFuelTransactionLinkID(id *string) *OrderUpdateOne {
	if id != nil {
		ouo = ouo.SetFuelTransactionLinkID(*id)
	}
	return ouo
}

// SetFuelTransactionLink sets the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity.
func (ouo *OrderUpdateOne) SetFuelTransactionLink(f *FuelTransactionOrderLink) *OrderUpdateOne {
	return ouo.SetFuelTransactionLinkID(f.ID)
}

// Mutation returns the OrderMutation object of the builder.
func (ouo *OrderUpdateOne) Mutation() *OrderMutation {
	return ouo.mutation
}

// ClearFuelTransactionLink clears the "fuel_transaction_link" edge to the FuelTransactionOrderLink entity.
func (ouo *OrderUpdateOne) ClearFuelTransactionLink() *OrderUpdateOne {
	ouo.mutation.ClearFuelTransactionLink()
	return ouo
}

// Where appends a list predicates to the OrderUpdate builder.
func (ouo *OrderUpdateOne) Where(ps ...predicate.Order) *OrderUpdateOne {
	ouo.mutation.Where(ps...)
	return ouo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ouo *OrderUpdateOne) Select(field string, fields ...string) *OrderUpdateOne {
	ouo.fields = append([]string{field}, fields...)
	return ouo
}

// Save executes the query and returns the updated Order entity.
func (ouo *OrderUpdateOne) Save(ctx context.Context) (*Order, error) {
	ouo.defaults()
	return withHooks(ctx, ouo.sqlSave, ouo.mutation, ouo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ouo *OrderUpdateOne) SaveX(ctx context.Context) *Order {
	node, err := ouo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ouo *OrderUpdateOne) Exec(ctx context.Context) error {
	_, err := ouo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ouo *OrderUpdateOne) ExecX(ctx context.Context) {
	if err := ouo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ouo *OrderUpdateOne) defaults() {
	if _, ok := ouo.mutation.UpdatedAt(); !ok {
		v := order.UpdateDefaultUpdatedAt()
		ouo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ouo *OrderUpdateOne) check() error {
	if v, ok := ouo.mutation.OrderNumber(); ok {
		if err := order.OrderNumberValidator(v); err != nil {
			return &ValidationError{Name: "order_number", err: fmt.Errorf(`order_service: validator failed for field "Order.order_number": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.CustomerPhone(); ok {
		if err := order.CustomerPhoneValidator(v); err != nil {
			return &ValidationError{Name: "customer_phone", err: fmt.Errorf(`order_service: validator failed for field "Order.customer_phone": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.LicensePlate(); ok {
		if err := order.LicensePlateValidator(v); err != nil {
			return &ValidationError{Name: "license_plate", err: fmt.Errorf(`order_service: validator failed for field "Order.license_plate": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.CustomerName(); ok {
		if err := order.CustomerNameValidator(v); err != nil {
			return &ValidationError{Name: "customer_name", err: fmt.Errorf(`order_service: validator failed for field "Order.customer_name": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.Status(); ok {
		if err := order.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`order_service: validator failed for field "Order.status": %w`, err)}
		}
	}
	if v, ok := ouo.mutation.EmployeeNo(); ok {
		if err := order.EmployeeNoValidator(v); err != nil {
			return &ValidationError{Name: "employee_no", err: fmt.Errorf(`order_service: validator failed for field "Order.employee_no": %w`, err)}
		}
	}
	return nil
}

func (ouo *OrderUpdateOne) sqlSave(ctx context.Context) (_node *Order, err error) {
	if err := ouo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(order.Table, order.Columns, sqlgraph.NewFieldSpec(order.FieldID, field.TypeString))
	id, ok := ouo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`order_service: missing "Order.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ouo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, order.FieldID)
		for _, f := range fields {
			if !order.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("order_service: invalid field %q for query", f)}
			}
			if f != order.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ouo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ouo.mutation.OrderNumber(); ok {
		_spec.SetField(order.FieldOrderNumber, field.TypeString, value)
	}
	if value, ok := ouo.mutation.CustomerID(); ok {
		_spec.SetField(order.FieldCustomerID, field.TypeInt64, value)
	}
	if value, ok := ouo.mutation.AddedCustomerID(); ok {
		_spec.AddField(order.FieldCustomerID, field.TypeInt64, value)
	}
	if ouo.mutation.CustomerIDCleared() {
		_spec.ClearField(order.FieldCustomerID, field.TypeInt64)
	}
	if value, ok := ouo.mutation.CustomerPhone(); ok {
		_spec.SetField(order.FieldCustomerPhone, field.TypeString, value)
	}
	if ouo.mutation.CustomerPhoneCleared() {
		_spec.ClearField(order.FieldCustomerPhone, field.TypeString)
	}
	if value, ok := ouo.mutation.LicensePlate(); ok {
		_spec.SetField(order.FieldLicensePlate, field.TypeString, value)
	}
	if ouo.mutation.LicensePlateCleared() {
		_spec.ClearField(order.FieldLicensePlate, field.TypeString)
	}
	if value, ok := ouo.mutation.CustomerName(); ok {
		_spec.SetField(order.FieldCustomerName, field.TypeString, value)
	}
	if ouo.mutation.CustomerNameCleared() {
		_spec.ClearField(order.FieldCustomerName, field.TypeString)
	}
	if value, ok := ouo.mutation.StationID(); ok {
		_spec.SetField(order.FieldStationID, field.TypeInt64, value)
	}
	if value, ok := ouo.mutation.AddedStationID(); ok {
		_spec.AddField(order.FieldStationID, field.TypeInt64, value)
	}
	if value, ok := ouo.mutation.Status(); ok {
		_spec.SetField(order.FieldStatus, field.TypeString, value)
	}
	if value, ok := ouo.mutation.TotalAmount(); ok {
		_spec.SetField(order.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := ouo.mutation.AddedTotalAmount(); ok {
		_spec.AddField(order.FieldTotalAmount, field.TypeFloat64, value)
	}
	if value, ok := ouo.mutation.DiscountAmount(); ok {
		_spec.SetField(order.FieldDiscountAmount, field.TypeFloat64, value)
	}
	if value, ok := ouo.mutation.AddedDiscountAmount(); ok {
		_spec.AddField(order.FieldDiscountAmount, field.TypeFloat64, value)
	}
	if value, ok := ouo.mutation.FinalAmount(); ok {
		_spec.SetField(order.FieldFinalAmount, field.TypeFloat64, value)
	}
	if value, ok := ouo.mutation.AddedFinalAmount(); ok {
		_spec.AddField(order.FieldFinalAmount, field.TypeFloat64, value)
	}
	if value, ok := ouo.mutation.TaxAmount(); ok {
		_spec.SetField(order.FieldTaxAmount, field.TypeFloat64, value)
	}
	if value, ok := ouo.mutation.AddedTaxAmount(); ok {
		_spec.AddField(order.FieldTaxAmount, field.TypeFloat64, value)
	}
	if value, ok := ouo.mutation.PaidAmount(); ok {
		_spec.SetField(order.FieldPaidAmount, field.TypeFloat64, value)
	}
	if value, ok := ouo.mutation.AddedPaidAmount(); ok {
		_spec.AddField(order.FieldPaidAmount, field.TypeFloat64, value)
	}
	if value, ok := ouo.mutation.Metadata(); ok {
		_spec.SetField(order.FieldMetadata, field.TypeJSON, value)
	}
	if ouo.mutation.MetadataCleared() {
		_spec.ClearField(order.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ouo.mutation.UpdatedAt(); ok {
		_spec.SetField(order.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ouo.mutation.CompletedAt(); ok {
		_spec.SetField(order.FieldCompletedAt, field.TypeTime, value)
	}
	if ouo.mutation.CompletedAtCleared() {
		_spec.ClearField(order.FieldCompletedAt, field.TypeTime)
	}
	if value, ok := ouo.mutation.CancelledAt(); ok {
		_spec.SetField(order.FieldCancelledAt, field.TypeTime, value)
	}
	if ouo.mutation.CancelledAtCleared() {
		_spec.ClearField(order.FieldCancelledAt, field.TypeTime)
	}
	if value, ok := ouo.mutation.EmployeeNo(); ok {
		_spec.SetField(order.FieldEmployeeNo, field.TypeString, value)
	}
	if ouo.mutation.EmployeeNoCleared() {
		_spec.ClearField(order.FieldEmployeeNo, field.TypeString)
	}
	if value, ok := ouo.mutation.StaffCardID(); ok {
		_spec.SetField(order.FieldStaffCardID, field.TypeInt64, value)
	}
	if value, ok := ouo.mutation.AddedStaffCardID(); ok {
		_spec.AddField(order.FieldStaffCardID, field.TypeInt64, value)
	}
	if ouo.mutation.StaffCardIDCleared() {
		_spec.ClearField(order.FieldStaffCardID, field.TypeInt64)
	}
	if ouo.mutation.FuelTransactionLinkCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   order.FuelTransactionLinkTable,
			Columns: []string{order.FuelTransactionLinkColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.FuelTransactionLinkIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   order.FuelTransactionLinkTable,
			Columns: []string{order.FuelTransactionLinkColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Order{config: ouo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ouo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{order.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ouo.mutation.done = true
	return _node, nil
}
