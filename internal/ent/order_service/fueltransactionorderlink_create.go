// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
)

// FuelTransactionOrderLinkCreate is the builder for creating a FuelTransactionOrderLink entity.
type FuelTransactionOrderLinkCreate struct {
	config
	mutation *FuelTransactionOrderLinkMutation
	hooks    []Hook
}

// SetFuelTransactionID sets the "fuel_transaction_id" field.
func (ftolc *FuelTransactionOrderLinkCreate) SetFuelTransactionID(s string) *FuelTransactionOrderLinkCreate {
	ftolc.mutation.SetFuelTransactionID(s)
	return ftolc
}

// SetOrderID sets the "order_id" field.
func (ftolc *FuelTransactionOrderLinkCreate) SetOrderID(s string) *FuelTransactionOrderLinkCreate {
	ftolc.mutation.SetOrderID(s)
	return ftolc
}

// SetAllocatedAmount sets the "allocated_amount" field.
func (ftolc *FuelTransactionOrderLinkCreate) SetAllocatedAmount(f float64) *FuelTransactionOrderLinkCreate {
	ftolc.mutation.SetAllocatedAmount(f)
	return ftolc
}

// SetStatus sets the "status" field.
func (ftolc *FuelTransactionOrderLinkCreate) SetStatus(s string) *FuelTransactionOrderLinkCreate {
	ftolc.mutation.SetStatus(s)
	return ftolc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ftolc *FuelTransactionOrderLinkCreate) SetNillableStatus(s *string) *FuelTransactionOrderLinkCreate {
	if s != nil {
		ftolc.SetStatus(*s)
	}
	return ftolc
}

// SetMetadata sets the "metadata" field.
func (ftolc *FuelTransactionOrderLinkCreate) SetMetadata(m map[string]interface{}) *FuelTransactionOrderLinkCreate {
	ftolc.mutation.SetMetadata(m)
	return ftolc
}

// SetCreatedAt sets the "created_at" field.
func (ftolc *FuelTransactionOrderLinkCreate) SetCreatedAt(t time.Time) *FuelTransactionOrderLinkCreate {
	ftolc.mutation.SetCreatedAt(t)
	return ftolc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ftolc *FuelTransactionOrderLinkCreate) SetNillableCreatedAt(t *time.Time) *FuelTransactionOrderLinkCreate {
	if t != nil {
		ftolc.SetCreatedAt(*t)
	}
	return ftolc
}

// SetUpdatedAt sets the "updated_at" field.
func (ftolc *FuelTransactionOrderLinkCreate) SetUpdatedAt(t time.Time) *FuelTransactionOrderLinkCreate {
	ftolc.mutation.SetUpdatedAt(t)
	return ftolc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ftolc *FuelTransactionOrderLinkCreate) SetNillableUpdatedAt(t *time.Time) *FuelTransactionOrderLinkCreate {
	if t != nil {
		ftolc.SetUpdatedAt(*t)
	}
	return ftolc
}

// SetDeactivatedAt sets the "deactivated_at" field.
func (ftolc *FuelTransactionOrderLinkCreate) SetDeactivatedAt(t time.Time) *FuelTransactionOrderLinkCreate {
	ftolc.mutation.SetDeactivatedAt(t)
	return ftolc
}

// SetNillableDeactivatedAt sets the "deactivated_at" field if the given value is not nil.
func (ftolc *FuelTransactionOrderLinkCreate) SetNillableDeactivatedAt(t *time.Time) *FuelTransactionOrderLinkCreate {
	if t != nil {
		ftolc.SetDeactivatedAt(*t)
	}
	return ftolc
}

// SetID sets the "id" field.
func (ftolc *FuelTransactionOrderLinkCreate) SetID(s string) *FuelTransactionOrderLinkCreate {
	ftolc.mutation.SetID(s)
	return ftolc
}

// SetFuelTransaction sets the "fuel_transaction" edge to the FuelTransaction entity.
func (ftolc *FuelTransactionOrderLinkCreate) SetFuelTransaction(f *FuelTransaction) *FuelTransactionOrderLinkCreate {
	return ftolc.SetFuelTransactionID(f.ID)
}

// SetOrder sets the "order" edge to the Order entity.
func (ftolc *FuelTransactionOrderLinkCreate) SetOrder(o *Order) *FuelTransactionOrderLinkCreate {
	return ftolc.SetOrderID(o.ID)
}

// Mutation returns the FuelTransactionOrderLinkMutation object of the builder.
func (ftolc *FuelTransactionOrderLinkCreate) Mutation() *FuelTransactionOrderLinkMutation {
	return ftolc.mutation
}

// Save creates the FuelTransactionOrderLink in the database.
func (ftolc *FuelTransactionOrderLinkCreate) Save(ctx context.Context) (*FuelTransactionOrderLink, error) {
	ftolc.defaults()
	return withHooks(ctx, ftolc.sqlSave, ftolc.mutation, ftolc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ftolc *FuelTransactionOrderLinkCreate) SaveX(ctx context.Context) *FuelTransactionOrderLink {
	v, err := ftolc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ftolc *FuelTransactionOrderLinkCreate) Exec(ctx context.Context) error {
	_, err := ftolc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ftolc *FuelTransactionOrderLinkCreate) ExecX(ctx context.Context) {
	if err := ftolc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ftolc *FuelTransactionOrderLinkCreate) defaults() {
	if _, ok := ftolc.mutation.Status(); !ok {
		v := fueltransactionorderlink.DefaultStatus
		ftolc.mutation.SetStatus(v)
	}
	if _, ok := ftolc.mutation.CreatedAt(); !ok {
		v := fueltransactionorderlink.DefaultCreatedAt()
		ftolc.mutation.SetCreatedAt(v)
	}
	if _, ok := ftolc.mutation.UpdatedAt(); !ok {
		v := fueltransactionorderlink.DefaultUpdatedAt()
		ftolc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ftolc *FuelTransactionOrderLinkCreate) check() error {
	if _, ok := ftolc.mutation.FuelTransactionID(); !ok {
		return &ValidationError{Name: "fuel_transaction_id", err: errors.New(`order_service: missing required field "FuelTransactionOrderLink.fuel_transaction_id"`)}
	}
	if _, ok := ftolc.mutation.OrderID(); !ok {
		return &ValidationError{Name: "order_id", err: errors.New(`order_service: missing required field "FuelTransactionOrderLink.order_id"`)}
	}
	if _, ok := ftolc.mutation.AllocatedAmount(); !ok {
		return &ValidationError{Name: "allocated_amount", err: errors.New(`order_service: missing required field "FuelTransactionOrderLink.allocated_amount"`)}
	}
	if _, ok := ftolc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`order_service: missing required field "FuelTransactionOrderLink.status"`)}
	}
	if v, ok := ftolc.mutation.Status(); ok {
		if err := fueltransactionorderlink.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`order_service: validator failed for field "FuelTransactionOrderLink.status": %w`, err)}
		}
	}
	if _, ok := ftolc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`order_service: missing required field "FuelTransactionOrderLink.created_at"`)}
	}
	if _, ok := ftolc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`order_service: missing required field "FuelTransactionOrderLink.updated_at"`)}
	}
	if v, ok := ftolc.mutation.ID(); ok {
		if err := fueltransactionorderlink.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransactionOrderLink.id": %w`, err)}
		}
	}
	if _, ok := ftolc.mutation.FuelTransactionID(); !ok {
		return &ValidationError{Name: "fuel_transaction", err: errors.New(`order_service: missing required edge "FuelTransactionOrderLink.fuel_transaction"`)}
	}
	if _, ok := ftolc.mutation.OrderID(); !ok {
		return &ValidationError{Name: "order", err: errors.New(`order_service: missing required edge "FuelTransactionOrderLink.order"`)}
	}
	return nil
}

func (ftolc *FuelTransactionOrderLinkCreate) sqlSave(ctx context.Context) (*FuelTransactionOrderLink, error) {
	if err := ftolc.check(); err != nil {
		return nil, err
	}
	_node, _spec := ftolc.createSpec()
	if err := sqlgraph.CreateNode(ctx, ftolc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected FuelTransactionOrderLink.ID type: %T", _spec.ID.Value)
		}
	}
	ftolc.mutation.id = &_node.ID
	ftolc.mutation.done = true
	return _node, nil
}

func (ftolc *FuelTransactionOrderLinkCreate) createSpec() (*FuelTransactionOrderLink, *sqlgraph.CreateSpec) {
	var (
		_node = &FuelTransactionOrderLink{config: ftolc.config}
		_spec = sqlgraph.NewCreateSpec(fueltransactionorderlink.Table, sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString))
	)
	if id, ok := ftolc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := ftolc.mutation.AllocatedAmount(); ok {
		_spec.SetField(fueltransactionorderlink.FieldAllocatedAmount, field.TypeFloat64, value)
		_node.AllocatedAmount = value
	}
	if value, ok := ftolc.mutation.Status(); ok {
		_spec.SetField(fueltransactionorderlink.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := ftolc.mutation.Metadata(); ok {
		_spec.SetField(fueltransactionorderlink.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := ftolc.mutation.CreatedAt(); ok {
		_spec.SetField(fueltransactionorderlink.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ftolc.mutation.UpdatedAt(); ok {
		_spec.SetField(fueltransactionorderlink.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ftolc.mutation.DeactivatedAt(); ok {
		_spec.SetField(fueltransactionorderlink.FieldDeactivatedAt, field.TypeTime, value)
		_node.DeactivatedAt = &value
	}
	if nodes := ftolc.mutation.FuelTransactionIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   fueltransactionorderlink.FuelTransactionTable,
			Columns: []string{fueltransactionorderlink.FuelTransactionColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransaction.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.FuelTransactionID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := ftolc.mutation.OrderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   fueltransactionorderlink.OrderTable,
			Columns: []string{fueltransactionorderlink.OrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(order.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.OrderID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// FuelTransactionOrderLinkCreateBulk is the builder for creating many FuelTransactionOrderLink entities in bulk.
type FuelTransactionOrderLinkCreateBulk struct {
	config
	err      error
	builders []*FuelTransactionOrderLinkCreate
}

// Save creates the FuelTransactionOrderLink entities in the database.
func (ftolcb *FuelTransactionOrderLinkCreateBulk) Save(ctx context.Context) ([]*FuelTransactionOrderLink, error) {
	if ftolcb.err != nil {
		return nil, ftolcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ftolcb.builders))
	nodes := make([]*FuelTransactionOrderLink, len(ftolcb.builders))
	mutators := make([]Mutator, len(ftolcb.builders))
	for i := range ftolcb.builders {
		func(i int, root context.Context) {
			builder := ftolcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*FuelTransactionOrderLinkMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ftolcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ftolcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ftolcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ftolcb *FuelTransactionOrderLinkCreateBulk) SaveX(ctx context.Context) []*FuelTransactionOrderLink {
	v, err := ftolcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ftolcb *FuelTransactionOrderLinkCreateBulk) Exec(ctx context.Context) error {
	_, err := ftolcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ftolcb *FuelTransactionOrderLinkCreateBulk) ExecX(ctx context.Context) {
	if err := ftolcb.Exec(ctx); err != nil {
		panic(err)
	}
}
