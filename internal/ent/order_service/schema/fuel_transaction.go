package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// FuelTransaction 燃油交易实体定义
type FuelTransaction struct {
	ent.Schema
}

// Fields 定义燃油交易的字段
func (FuelTransaction) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			MaxLen(255).
			Comment("主键ID"),
		field.String("transaction_number").
			MaxLen(50).
			NotEmpty().
			Comment("交易编号"),
		field.Int64("station_id").
			Comment("加油站ID"),
		field.String("pump_id").
			MaxLen(50).
			NotEmpty().
			Comment("油泵ID"),
		field.String("nozzle_id").
			MaxLen(50).
			NotEmpty().
			Comment("油枪ID"),
		field.String("fuel_type").
			MaxLen(50).
			NotEmpty().
			Comment("燃油类型"),
		field.String("fuel_grade").
			MaxLen(50).
			NotEmpty().
			Comment("燃油等级"),
		field.Int("tank").
			Comment("油罐编号"),
		field.Float("unit_price").
			SchemaType(map[string]string{
				"postgres": "numeric(10,2)",
			}).
			Comment("单价"),
		field.Float("volume").
			SchemaType(map[string]string{
				"postgres": "numeric(10,3)",
			}).
			Comment("加油量"),
		field.Float("amount").
			SchemaType(map[string]string{
				"postgres": "numeric(12,2)",
			}).
			Comment("金额"),
		field.Float("total_volume").
			SchemaType(map[string]string{
				"postgres": "numeric(10,3)",
			}).
			Default(0.0).
			Comment("总加油量"),
		field.Float("total_amount").
			SchemaType(map[string]string{
				"postgres": "numeric(12,2)",
			}).
			Default(0.0).
			Comment("总金额"),
		field.String("status").
			MaxLen(20).
			Default("pending").
			Comment("状态"),
		field.String("member_card_id").
			MaxLen(100).
			Optional().
			Nillable().
			Comment("会员卡ID"),
		field.String("member_id").
			MaxLen(255).
			Optional().
			Nillable().
			Comment("会员ID"),
		field.String("employee_id").
			MaxLen(255).
			Optional().
			Nillable().
			Comment("员工ID"),
		field.String("fcc_transaction_id").
			MaxLen(100).
			Optional().
			Nillable().
			Comment("FCC交易ID"),
		field.String("pos_terminal_id").
			MaxLen(50).
			Optional().
			Nillable().
			Comment("POS终端ID"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional().
			Comment("元数据"),
		field.Time("created_at").
			Default(time.Now).
			Immutable().
			Comment("创建时间"),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("更新时间"),
		field.Time("processed_at").
			Optional().
			Nillable().
			Comment("处理时间"),
		field.Time("cancelled_at").
			Optional().
			Nillable().
			Comment("取消时间"),
		field.Float("start_totalizer").
			SchemaType(map[string]string{
				"postgres": "numeric(15,3)",
			}).
			Optional().
			Nillable().
			Comment("起始计数器"),
		field.Float("end_totalizer").
			SchemaType(map[string]string{
				"postgres": "numeric(15,3)",
			}).
			Optional().
			Nillable().
			Comment("结束计数器"),
		field.Time("nozzle_start_time").
			Optional().
			Nillable().
			Comment("油枪开始时间"),
		field.Time("nozzle_end_time").
			Optional().
			Nillable().
			Comment("油枪结束时间"),
		field.String("staff_card_id").
			MaxLen(255).
			Optional().
			Nillable().
			Comment("员工卡ID"),
	}
}

// Edges 定义燃油交易的关联关系
func (FuelTransaction) Edges() []ent.Edge {
	return []ent.Edge{
		// 与订单的一对一关系，通过中间表连接
		edge.To("order_link", FuelTransactionOrderLink.Type).
			Unique(),
	}
}
