package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// FuelTransactionOrderLink 燃油交易订单关联实体定义
type FuelTransactionOrderLink struct {
	ent.Schema
}

// Fields 定义燃油交易订单关联的字段
func (FuelTransactionOrderLink) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			MaxLen(255).
			Comment("主键ID"),
		field.String("fuel_transaction_id").
			Comment("燃油交易ID"),
		field.String("order_id").
			Comment("订单ID"),
		field.Float("allocated_amount").
			SchemaType(map[string]string{
				"postgres": "numeric(12,2)",
			}).
			Comment("分配金额"),
		field.String("status").
			MaxLen(20).
			Default("active").
			Comment("关联状态"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional().
			Comment("元数据"),
		field.Time("created_at").
			Default(time.Now).
			Immutable().
			Comment("创建时间"),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("更新时间"),
		field.Time("deactivated_at").
			Optional().
			Nillable().
			Comment("停用时间"),
	}
}

// Edges 定义燃油交易订单关联的关联关系
func (FuelTransactionOrderLink) Edges() []ent.Edge {
	return []ent.Edge{
		// 关联到燃油交易
		edge.From("fuel_transaction", FuelTransaction.Type).
			Ref("order_link").
			Field("fuel_transaction_id").
			Required().
			Unique(),
		// 关联到订单
		edge.From("order", Order.Type).
			Ref("fuel_transaction_link").
			Field("order_id").
			Required().
			Unique(),
	}
}
