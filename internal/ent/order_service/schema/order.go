package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Order 订单实体定义
type Order struct {
	ent.Schema
}

// Fields 定义订单的字段
func (Order) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			MaxLen(255).
			Comment("主键ID"),
		field.String("order_number").
			MaxLen(100).
			NotEmpty().
			Comment("订单编号"),
		field.Int64("customer_id").
			Optional().
			Nillable().
			Comment("客户ID"),
		field.String("customer_phone").
			MaxLen(20).
			Optional().
			Nillable().
			Comment("客户手机号"),
		field.String("license_plate").
			MaxLen(100).
			Optional().
			Nillable().
			Comment("车牌号"),
		field.String("customer_name").
			MaxLen(100).
			Optional().
			Nillable().
			Comment("客户姓名"),
		field.Int64("station_id").
			Comment("加油站ID"),
		field.String("status").
			MaxLen(20).
			Default("new").
			Comment("订单状态"),
		field.Float("total_amount").
			SchemaType(map[string]string{
				"postgres": "numeric(12,2)",
			}).
			Default(0.0).
			Comment("总金额"),
		field.Float("discount_amount").
			SchemaType(map[string]string{
				"postgres": "numeric(12,2)",
			}).
			Default(0.0).
			Comment("折扣金额"),
		field.Float("final_amount").
			SchemaType(map[string]string{
				"postgres": "numeric(12,2)",
			}).
			Default(0.0).
			Comment("最终金额"),
		field.Float("tax_amount").
			SchemaType(map[string]string{
				"postgres": "numeric(12,2)",
			}).
			Default(0.0).
			Comment("税费金额"),
		field.Float("paid_amount").
			SchemaType(map[string]string{
				"postgres": "numeric(12,2)",
			}).
			Default(0.0).
			Comment("已支付金额"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional().
			Comment("元数据"),
		field.Time("created_at").
			Default(time.Now).
			Immutable().
			Comment("创建时间"),
		field.Time("updated_at").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("更新时间"),
		field.Time("completed_at").
			Optional().
			Nillable().
			Comment("完成时间"),
		field.Time("cancelled_at").
			Optional().
			Nillable().
			Comment("取消时间"),
		field.String("employee_no").
			MaxLen(50).
			Optional().
			Nillable().
			Comment("员工编号"),
		field.Int64("staff_card_id").
			Optional().
			Nillable().
			Comment("员工卡ID"),
	}
}

// Edges 定义订单的关联关系
func (Order) Edges() []ent.Edge {
	return []ent.Edge{
		// 与燃油交易的一对一关系，通过中间表连接
		edge.To("fuel_transaction_link", FuelTransactionOrderLink.Type).
			Unique(),
	}
}
