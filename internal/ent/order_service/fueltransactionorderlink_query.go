// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

// FuelTransactionOrderLinkQuery is the builder for querying FuelTransactionOrderLink entities.
type FuelTransactionOrderLinkQuery struct {
	config
	ctx                 *QueryContext
	order               []fueltransactionorderlink.OrderOption
	inters              []Interceptor
	predicates          []predicate.FuelTransactionOrderLink
	withFuelTransaction *FuelTransactionQuery
	withOrder           *OrderQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the FuelTransactionOrderLinkQuery builder.
func (ftolq *FuelTransactionOrderLinkQuery) Where(ps ...predicate.FuelTransactionOrderLink) *FuelTransactionOrderLinkQuery {
	ftolq.predicates = append(ftolq.predicates, ps...)
	return ftolq
}

// Limit the number of records to be returned by this query.
func (ftolq *FuelTransactionOrderLinkQuery) Limit(limit int) *FuelTransactionOrderLinkQuery {
	ftolq.ctx.Limit = &limit
	return ftolq
}

// Offset to start from.
func (ftolq *FuelTransactionOrderLinkQuery) Offset(offset int) *FuelTransactionOrderLinkQuery {
	ftolq.ctx.Offset = &offset
	return ftolq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ftolq *FuelTransactionOrderLinkQuery) Unique(unique bool) *FuelTransactionOrderLinkQuery {
	ftolq.ctx.Unique = &unique
	return ftolq
}

// Order specifies how the records should be ordered.
func (ftolq *FuelTransactionOrderLinkQuery) Order(o ...fueltransactionorderlink.OrderOption) *FuelTransactionOrderLinkQuery {
	ftolq.order = append(ftolq.order, o...)
	return ftolq
}

// QueryFuelTransaction chains the current query on the "fuel_transaction" edge.
func (ftolq *FuelTransactionOrderLinkQuery) QueryFuelTransaction() *FuelTransactionQuery {
	query := (&FuelTransactionClient{config: ftolq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := ftolq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := ftolq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(fueltransactionorderlink.Table, fueltransactionorderlink.FieldID, selector),
			sqlgraph.To(fueltransaction.Table, fueltransaction.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, fueltransactionorderlink.FuelTransactionTable, fueltransactionorderlink.FuelTransactionColumn),
		)
		fromU = sqlgraph.SetNeighbors(ftolq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryOrder chains the current query on the "order" edge.
func (ftolq *FuelTransactionOrderLinkQuery) QueryOrder() *OrderQuery {
	query := (&OrderClient{config: ftolq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := ftolq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := ftolq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(fueltransactionorderlink.Table, fueltransactionorderlink.FieldID, selector),
			sqlgraph.To(order.Table, order.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, fueltransactionorderlink.OrderTable, fueltransactionorderlink.OrderColumn),
		)
		fromU = sqlgraph.SetNeighbors(ftolq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first FuelTransactionOrderLink entity from the query.
// Returns a *NotFoundError when no FuelTransactionOrderLink was found.
func (ftolq *FuelTransactionOrderLinkQuery) First(ctx context.Context) (*FuelTransactionOrderLink, error) {
	nodes, err := ftolq.Limit(1).All(setContextOp(ctx, ftolq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{fueltransactionorderlink.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ftolq *FuelTransactionOrderLinkQuery) FirstX(ctx context.Context) *FuelTransactionOrderLink {
	node, err := ftolq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first FuelTransactionOrderLink ID from the query.
// Returns a *NotFoundError when no FuelTransactionOrderLink ID was found.
func (ftolq *FuelTransactionOrderLinkQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = ftolq.Limit(1).IDs(setContextOp(ctx, ftolq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{fueltransactionorderlink.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ftolq *FuelTransactionOrderLinkQuery) FirstIDX(ctx context.Context) string {
	id, err := ftolq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single FuelTransactionOrderLink entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one FuelTransactionOrderLink entity is found.
// Returns a *NotFoundError when no FuelTransactionOrderLink entities are found.
func (ftolq *FuelTransactionOrderLinkQuery) Only(ctx context.Context) (*FuelTransactionOrderLink, error) {
	nodes, err := ftolq.Limit(2).All(setContextOp(ctx, ftolq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{fueltransactionorderlink.Label}
	default:
		return nil, &NotSingularError{fueltransactionorderlink.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ftolq *FuelTransactionOrderLinkQuery) OnlyX(ctx context.Context) *FuelTransactionOrderLink {
	node, err := ftolq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only FuelTransactionOrderLink ID in the query.
// Returns a *NotSingularError when more than one FuelTransactionOrderLink ID is found.
// Returns a *NotFoundError when no entities are found.
func (ftolq *FuelTransactionOrderLinkQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = ftolq.Limit(2).IDs(setContextOp(ctx, ftolq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{fueltransactionorderlink.Label}
	default:
		err = &NotSingularError{fueltransactionorderlink.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ftolq *FuelTransactionOrderLinkQuery) OnlyIDX(ctx context.Context) string {
	id, err := ftolq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of FuelTransactionOrderLinks.
func (ftolq *FuelTransactionOrderLinkQuery) All(ctx context.Context) ([]*FuelTransactionOrderLink, error) {
	ctx = setContextOp(ctx, ftolq.ctx, "All")
	if err := ftolq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*FuelTransactionOrderLink, *FuelTransactionOrderLinkQuery]()
	return withInterceptors[[]*FuelTransactionOrderLink](ctx, ftolq, qr, ftolq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ftolq *FuelTransactionOrderLinkQuery) AllX(ctx context.Context) []*FuelTransactionOrderLink {
	nodes, err := ftolq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of FuelTransactionOrderLink IDs.
func (ftolq *FuelTransactionOrderLinkQuery) IDs(ctx context.Context) (ids []string, err error) {
	if ftolq.ctx.Unique == nil && ftolq.path != nil {
		ftolq.Unique(true)
	}
	ctx = setContextOp(ctx, ftolq.ctx, "IDs")
	if err = ftolq.Select(fueltransactionorderlink.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ftolq *FuelTransactionOrderLinkQuery) IDsX(ctx context.Context) []string {
	ids, err := ftolq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ftolq *FuelTransactionOrderLinkQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ftolq.ctx, "Count")
	if err := ftolq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ftolq, querierCount[*FuelTransactionOrderLinkQuery](), ftolq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ftolq *FuelTransactionOrderLinkQuery) CountX(ctx context.Context) int {
	count, err := ftolq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ftolq *FuelTransactionOrderLinkQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ftolq.ctx, "Exist")
	switch _, err := ftolq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("order_service: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ftolq *FuelTransactionOrderLinkQuery) ExistX(ctx context.Context) bool {
	exist, err := ftolq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the FuelTransactionOrderLinkQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ftolq *FuelTransactionOrderLinkQuery) Clone() *FuelTransactionOrderLinkQuery {
	if ftolq == nil {
		return nil
	}
	return &FuelTransactionOrderLinkQuery{
		config:              ftolq.config,
		ctx:                 ftolq.ctx.Clone(),
		order:               append([]fueltransactionorderlink.OrderOption{}, ftolq.order...),
		inters:              append([]Interceptor{}, ftolq.inters...),
		predicates:          append([]predicate.FuelTransactionOrderLink{}, ftolq.predicates...),
		withFuelTransaction: ftolq.withFuelTransaction.Clone(),
		withOrder:           ftolq.withOrder.Clone(),
		// clone intermediate query.
		sql:  ftolq.sql.Clone(),
		path: ftolq.path,
	}
}

// WithFuelTransaction tells the query-builder to eager-load the nodes that are connected to
// the "fuel_transaction" edge. The optional arguments are used to configure the query builder of the edge.
func (ftolq *FuelTransactionOrderLinkQuery) WithFuelTransaction(opts ...func(*FuelTransactionQuery)) *FuelTransactionOrderLinkQuery {
	query := (&FuelTransactionClient{config: ftolq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	ftolq.withFuelTransaction = query
	return ftolq
}

// WithOrder tells the query-builder to eager-load the nodes that are connected to
// the "order" edge. The optional arguments are used to configure the query builder of the edge.
func (ftolq *FuelTransactionOrderLinkQuery) WithOrder(opts ...func(*OrderQuery)) *FuelTransactionOrderLinkQuery {
	query := (&OrderClient{config: ftolq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	ftolq.withOrder = query
	return ftolq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		FuelTransactionID string `json:"fuel_transaction_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.FuelTransactionOrderLink.Query().
//		GroupBy(fueltransactionorderlink.FieldFuelTransactionID).
//		Aggregate(order_service.Count()).
//		Scan(ctx, &v)
func (ftolq *FuelTransactionOrderLinkQuery) GroupBy(field string, fields ...string) *FuelTransactionOrderLinkGroupBy {
	ftolq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &FuelTransactionOrderLinkGroupBy{build: ftolq}
	grbuild.flds = &ftolq.ctx.Fields
	grbuild.label = fueltransactionorderlink.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		FuelTransactionID string `json:"fuel_transaction_id,omitempty"`
//	}
//
//	client.FuelTransactionOrderLink.Query().
//		Select(fueltransactionorderlink.FieldFuelTransactionID).
//		Scan(ctx, &v)
func (ftolq *FuelTransactionOrderLinkQuery) Select(fields ...string) *FuelTransactionOrderLinkSelect {
	ftolq.ctx.Fields = append(ftolq.ctx.Fields, fields...)
	sbuild := &FuelTransactionOrderLinkSelect{FuelTransactionOrderLinkQuery: ftolq}
	sbuild.label = fueltransactionorderlink.Label
	sbuild.flds, sbuild.scan = &ftolq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a FuelTransactionOrderLinkSelect configured with the given aggregations.
func (ftolq *FuelTransactionOrderLinkQuery) Aggregate(fns ...AggregateFunc) *FuelTransactionOrderLinkSelect {
	return ftolq.Select().Aggregate(fns...)
}

func (ftolq *FuelTransactionOrderLinkQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ftolq.inters {
		if inter == nil {
			return fmt.Errorf("order_service: uninitialized interceptor (forgotten import order_service/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ftolq); err != nil {
				return err
			}
		}
	}
	for _, f := range ftolq.ctx.Fields {
		if !fueltransactionorderlink.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("order_service: invalid field %q for query", f)}
		}
	}
	if ftolq.path != nil {
		prev, err := ftolq.path(ctx)
		if err != nil {
			return err
		}
		ftolq.sql = prev
	}
	return nil
}

func (ftolq *FuelTransactionOrderLinkQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*FuelTransactionOrderLink, error) {
	var (
		nodes       = []*FuelTransactionOrderLink{}
		_spec       = ftolq.querySpec()
		loadedTypes = [2]bool{
			ftolq.withFuelTransaction != nil,
			ftolq.withOrder != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*FuelTransactionOrderLink).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &FuelTransactionOrderLink{config: ftolq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ftolq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := ftolq.withFuelTransaction; query != nil {
		if err := ftolq.loadFuelTransaction(ctx, query, nodes, nil,
			func(n *FuelTransactionOrderLink, e *FuelTransaction) { n.Edges.FuelTransaction = e }); err != nil {
			return nil, err
		}
	}
	if query := ftolq.withOrder; query != nil {
		if err := ftolq.loadOrder(ctx, query, nodes, nil,
			func(n *FuelTransactionOrderLink, e *Order) { n.Edges.Order = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (ftolq *FuelTransactionOrderLinkQuery) loadFuelTransaction(ctx context.Context, query *FuelTransactionQuery, nodes []*FuelTransactionOrderLink, init func(*FuelTransactionOrderLink), assign func(*FuelTransactionOrderLink, *FuelTransaction)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*FuelTransactionOrderLink)
	for i := range nodes {
		fk := nodes[i].FuelTransactionID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(fueltransaction.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "fuel_transaction_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (ftolq *FuelTransactionOrderLinkQuery) loadOrder(ctx context.Context, query *OrderQuery, nodes []*FuelTransactionOrderLink, init func(*FuelTransactionOrderLink), assign func(*FuelTransactionOrderLink, *Order)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*FuelTransactionOrderLink)
	for i := range nodes {
		fk := nodes[i].OrderID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(order.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "order_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (ftolq *FuelTransactionOrderLinkQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ftolq.querySpec()
	_spec.Node.Columns = ftolq.ctx.Fields
	if len(ftolq.ctx.Fields) > 0 {
		_spec.Unique = ftolq.ctx.Unique != nil && *ftolq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ftolq.driver, _spec)
}

func (ftolq *FuelTransactionOrderLinkQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(fueltransactionorderlink.Table, fueltransactionorderlink.Columns, sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString))
	_spec.From = ftolq.sql
	if unique := ftolq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ftolq.path != nil {
		_spec.Unique = true
	}
	if fields := ftolq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, fueltransactionorderlink.FieldID)
		for i := range fields {
			if fields[i] != fueltransactionorderlink.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if ftolq.withFuelTransaction != nil {
			_spec.Node.AddColumnOnce(fueltransactionorderlink.FieldFuelTransactionID)
		}
		if ftolq.withOrder != nil {
			_spec.Node.AddColumnOnce(fueltransactionorderlink.FieldOrderID)
		}
	}
	if ps := ftolq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ftolq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ftolq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ftolq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ftolq *FuelTransactionOrderLinkQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ftolq.driver.Dialect())
	t1 := builder.Table(fueltransactionorderlink.Table)
	columns := ftolq.ctx.Fields
	if len(columns) == 0 {
		columns = fueltransactionorderlink.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ftolq.sql != nil {
		selector = ftolq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ftolq.ctx.Unique != nil && *ftolq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ftolq.predicates {
		p(selector)
	}
	for _, p := range ftolq.order {
		p(selector)
	}
	if offset := ftolq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ftolq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// FuelTransactionOrderLinkGroupBy is the group-by builder for FuelTransactionOrderLink entities.
type FuelTransactionOrderLinkGroupBy struct {
	selector
	build *FuelTransactionOrderLinkQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ftolgb *FuelTransactionOrderLinkGroupBy) Aggregate(fns ...AggregateFunc) *FuelTransactionOrderLinkGroupBy {
	ftolgb.fns = append(ftolgb.fns, fns...)
	return ftolgb
}

// Scan applies the selector query and scans the result into the given value.
func (ftolgb *FuelTransactionOrderLinkGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ftolgb.build.ctx, "GroupBy")
	if err := ftolgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*FuelTransactionOrderLinkQuery, *FuelTransactionOrderLinkGroupBy](ctx, ftolgb.build, ftolgb, ftolgb.build.inters, v)
}

func (ftolgb *FuelTransactionOrderLinkGroupBy) sqlScan(ctx context.Context, root *FuelTransactionOrderLinkQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ftolgb.fns))
	for _, fn := range ftolgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ftolgb.flds)+len(ftolgb.fns))
		for _, f := range *ftolgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ftolgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ftolgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// FuelTransactionOrderLinkSelect is the builder for selecting fields of FuelTransactionOrderLink entities.
type FuelTransactionOrderLinkSelect struct {
	*FuelTransactionOrderLinkQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ftols *FuelTransactionOrderLinkSelect) Aggregate(fns ...AggregateFunc) *FuelTransactionOrderLinkSelect {
	ftols.fns = append(ftols.fns, fns...)
	return ftols
}

// Scan applies the selector query and scans the result into the given value.
func (ftols *FuelTransactionOrderLinkSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ftols.ctx, "Select")
	if err := ftols.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*FuelTransactionOrderLinkQuery, *FuelTransactionOrderLinkSelect](ctx, ftols.FuelTransactionOrderLinkQuery, ftols, ftols.inters, v)
}

func (ftols *FuelTransactionOrderLinkSelect) sqlScan(ctx context.Context, root *FuelTransactionOrderLinkQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ftols.fns))
	for _, fn := range ftols.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ftols.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ftols.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
