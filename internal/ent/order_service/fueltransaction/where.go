// Code generated by ent, DO NOT EDIT.

package fueltransaction

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldID, id))
}

// TransactionNumber applies equality check predicate on the "transaction_number" field. It's identical to TransactionNumberEQ.
func TransactionNumber(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldTransactionNumber, v))
}

// StationID applies equality check predicate on the "station_id" field. It's identical to StationIDEQ.
func StationID(v int64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldStationID, v))
}

// PumpID applies equality check predicate on the "pump_id" field. It's identical to PumpIDEQ.
func PumpID(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldPumpID, v))
}

// NozzleID applies equality check predicate on the "nozzle_id" field. It's identical to NozzleIDEQ.
func NozzleID(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldNozzleID, v))
}

// FuelType applies equality check predicate on the "fuel_type" field. It's identical to FuelTypeEQ.
func FuelType(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldFuelType, v))
}

// FuelGrade applies equality check predicate on the "fuel_grade" field. It's identical to FuelGradeEQ.
func FuelGrade(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldFuelGrade, v))
}

// Tank applies equality check predicate on the "tank" field. It's identical to TankEQ.
func Tank(v int) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldTank, v))
}

// UnitPrice applies equality check predicate on the "unit_price" field. It's identical to UnitPriceEQ.
func UnitPrice(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldUnitPrice, v))
}

// Volume applies equality check predicate on the "volume" field. It's identical to VolumeEQ.
func Volume(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldVolume, v))
}

// Amount applies equality check predicate on the "amount" field. It's identical to AmountEQ.
func Amount(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldAmount, v))
}

// TotalVolume applies equality check predicate on the "total_volume" field. It's identical to TotalVolumeEQ.
func TotalVolume(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldTotalVolume, v))
}

// TotalAmount applies equality check predicate on the "total_amount" field. It's identical to TotalAmountEQ.
func TotalAmount(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldTotalAmount, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldStatus, v))
}

// MemberCardID applies equality check predicate on the "member_card_id" field. It's identical to MemberCardIDEQ.
func MemberCardID(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldMemberCardID, v))
}

// MemberID applies equality check predicate on the "member_id" field. It's identical to MemberIDEQ.
func MemberID(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldMemberID, v))
}

// EmployeeID applies equality check predicate on the "employee_id" field. It's identical to EmployeeIDEQ.
func EmployeeID(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldEmployeeID, v))
}

// FccTransactionID applies equality check predicate on the "fcc_transaction_id" field. It's identical to FccTransactionIDEQ.
func FccTransactionID(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldFccTransactionID, v))
}

// PosTerminalID applies equality check predicate on the "pos_terminal_id" field. It's identical to PosTerminalIDEQ.
func PosTerminalID(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldPosTerminalID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldUpdatedAt, v))
}

// ProcessedAt applies equality check predicate on the "processed_at" field. It's identical to ProcessedAtEQ.
func ProcessedAt(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldProcessedAt, v))
}

// CancelledAt applies equality check predicate on the "cancelled_at" field. It's identical to CancelledAtEQ.
func CancelledAt(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldCancelledAt, v))
}

// StartTotalizer applies equality check predicate on the "start_totalizer" field. It's identical to StartTotalizerEQ.
func StartTotalizer(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldStartTotalizer, v))
}

// EndTotalizer applies equality check predicate on the "end_totalizer" field. It's identical to EndTotalizerEQ.
func EndTotalizer(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldEndTotalizer, v))
}

// NozzleStartTime applies equality check predicate on the "nozzle_start_time" field. It's identical to NozzleStartTimeEQ.
func NozzleStartTime(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldNozzleStartTime, v))
}

// NozzleEndTime applies equality check predicate on the "nozzle_end_time" field. It's identical to NozzleEndTimeEQ.
func NozzleEndTime(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldNozzleEndTime, v))
}

// StaffCardID applies equality check predicate on the "staff_card_id" field. It's identical to StaffCardIDEQ.
func StaffCardID(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldStaffCardID, v))
}

// TransactionNumberEQ applies the EQ predicate on the "transaction_number" field.
func TransactionNumberEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldTransactionNumber, v))
}

// TransactionNumberNEQ applies the NEQ predicate on the "transaction_number" field.
func TransactionNumberNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldTransactionNumber, v))
}

// TransactionNumberIn applies the In predicate on the "transaction_number" field.
func TransactionNumberIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldTransactionNumber, vs...))
}

// TransactionNumberNotIn applies the NotIn predicate on the "transaction_number" field.
func TransactionNumberNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldTransactionNumber, vs...))
}

// TransactionNumberGT applies the GT predicate on the "transaction_number" field.
func TransactionNumberGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldTransactionNumber, v))
}

// TransactionNumberGTE applies the GTE predicate on the "transaction_number" field.
func TransactionNumberGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldTransactionNumber, v))
}

// TransactionNumberLT applies the LT predicate on the "transaction_number" field.
func TransactionNumberLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldTransactionNumber, v))
}

// TransactionNumberLTE applies the LTE predicate on the "transaction_number" field.
func TransactionNumberLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldTransactionNumber, v))
}

// TransactionNumberContains applies the Contains predicate on the "transaction_number" field.
func TransactionNumberContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldTransactionNumber, v))
}

// TransactionNumberHasPrefix applies the HasPrefix predicate on the "transaction_number" field.
func TransactionNumberHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldTransactionNumber, v))
}

// TransactionNumberHasSuffix applies the HasSuffix predicate on the "transaction_number" field.
func TransactionNumberHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldTransactionNumber, v))
}

// TransactionNumberEqualFold applies the EqualFold predicate on the "transaction_number" field.
func TransactionNumberEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldTransactionNumber, v))
}

// TransactionNumberContainsFold applies the ContainsFold predicate on the "transaction_number" field.
func TransactionNumberContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldTransactionNumber, v))
}

// StationIDEQ applies the EQ predicate on the "station_id" field.
func StationIDEQ(v int64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldStationID, v))
}

// StationIDNEQ applies the NEQ predicate on the "station_id" field.
func StationIDNEQ(v int64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldStationID, v))
}

// StationIDIn applies the In predicate on the "station_id" field.
func StationIDIn(vs ...int64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldStationID, vs...))
}

// StationIDNotIn applies the NotIn predicate on the "station_id" field.
func StationIDNotIn(vs ...int64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldStationID, vs...))
}

// StationIDGT applies the GT predicate on the "station_id" field.
func StationIDGT(v int64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldStationID, v))
}

// StationIDGTE applies the GTE predicate on the "station_id" field.
func StationIDGTE(v int64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldStationID, v))
}

// StationIDLT applies the LT predicate on the "station_id" field.
func StationIDLT(v int64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldStationID, v))
}

// StationIDLTE applies the LTE predicate on the "station_id" field.
func StationIDLTE(v int64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldStationID, v))
}

// PumpIDEQ applies the EQ predicate on the "pump_id" field.
func PumpIDEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldPumpID, v))
}

// PumpIDNEQ applies the NEQ predicate on the "pump_id" field.
func PumpIDNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldPumpID, v))
}

// PumpIDIn applies the In predicate on the "pump_id" field.
func PumpIDIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldPumpID, vs...))
}

// PumpIDNotIn applies the NotIn predicate on the "pump_id" field.
func PumpIDNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldPumpID, vs...))
}

// PumpIDGT applies the GT predicate on the "pump_id" field.
func PumpIDGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldPumpID, v))
}

// PumpIDGTE applies the GTE predicate on the "pump_id" field.
func PumpIDGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldPumpID, v))
}

// PumpIDLT applies the LT predicate on the "pump_id" field.
func PumpIDLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldPumpID, v))
}

// PumpIDLTE applies the LTE predicate on the "pump_id" field.
func PumpIDLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldPumpID, v))
}

// PumpIDContains applies the Contains predicate on the "pump_id" field.
func PumpIDContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldPumpID, v))
}

// PumpIDHasPrefix applies the HasPrefix predicate on the "pump_id" field.
func PumpIDHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldPumpID, v))
}

// PumpIDHasSuffix applies the HasSuffix predicate on the "pump_id" field.
func PumpIDHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldPumpID, v))
}

// PumpIDEqualFold applies the EqualFold predicate on the "pump_id" field.
func PumpIDEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldPumpID, v))
}

// PumpIDContainsFold applies the ContainsFold predicate on the "pump_id" field.
func PumpIDContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldPumpID, v))
}

// NozzleIDEQ applies the EQ predicate on the "nozzle_id" field.
func NozzleIDEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldNozzleID, v))
}

// NozzleIDNEQ applies the NEQ predicate on the "nozzle_id" field.
func NozzleIDNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldNozzleID, v))
}

// NozzleIDIn applies the In predicate on the "nozzle_id" field.
func NozzleIDIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldNozzleID, vs...))
}

// NozzleIDNotIn applies the NotIn predicate on the "nozzle_id" field.
func NozzleIDNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldNozzleID, vs...))
}

// NozzleIDGT applies the GT predicate on the "nozzle_id" field.
func NozzleIDGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldNozzleID, v))
}

// NozzleIDGTE applies the GTE predicate on the "nozzle_id" field.
func NozzleIDGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldNozzleID, v))
}

// NozzleIDLT applies the LT predicate on the "nozzle_id" field.
func NozzleIDLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldNozzleID, v))
}

// NozzleIDLTE applies the LTE predicate on the "nozzle_id" field.
func NozzleIDLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldNozzleID, v))
}

// NozzleIDContains applies the Contains predicate on the "nozzle_id" field.
func NozzleIDContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldNozzleID, v))
}

// NozzleIDHasPrefix applies the HasPrefix predicate on the "nozzle_id" field.
func NozzleIDHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldNozzleID, v))
}

// NozzleIDHasSuffix applies the HasSuffix predicate on the "nozzle_id" field.
func NozzleIDHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldNozzleID, v))
}

// NozzleIDEqualFold applies the EqualFold predicate on the "nozzle_id" field.
func NozzleIDEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldNozzleID, v))
}

// NozzleIDContainsFold applies the ContainsFold predicate on the "nozzle_id" field.
func NozzleIDContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldNozzleID, v))
}

// FuelTypeEQ applies the EQ predicate on the "fuel_type" field.
func FuelTypeEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldFuelType, v))
}

// FuelTypeNEQ applies the NEQ predicate on the "fuel_type" field.
func FuelTypeNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldFuelType, v))
}

// FuelTypeIn applies the In predicate on the "fuel_type" field.
func FuelTypeIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldFuelType, vs...))
}

// FuelTypeNotIn applies the NotIn predicate on the "fuel_type" field.
func FuelTypeNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldFuelType, vs...))
}

// FuelTypeGT applies the GT predicate on the "fuel_type" field.
func FuelTypeGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldFuelType, v))
}

// FuelTypeGTE applies the GTE predicate on the "fuel_type" field.
func FuelTypeGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldFuelType, v))
}

// FuelTypeLT applies the LT predicate on the "fuel_type" field.
func FuelTypeLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldFuelType, v))
}

// FuelTypeLTE applies the LTE predicate on the "fuel_type" field.
func FuelTypeLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldFuelType, v))
}

// FuelTypeContains applies the Contains predicate on the "fuel_type" field.
func FuelTypeContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldFuelType, v))
}

// FuelTypeHasPrefix applies the HasPrefix predicate on the "fuel_type" field.
func FuelTypeHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldFuelType, v))
}

// FuelTypeHasSuffix applies the HasSuffix predicate on the "fuel_type" field.
func FuelTypeHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldFuelType, v))
}

// FuelTypeEqualFold applies the EqualFold predicate on the "fuel_type" field.
func FuelTypeEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldFuelType, v))
}

// FuelTypeContainsFold applies the ContainsFold predicate on the "fuel_type" field.
func FuelTypeContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldFuelType, v))
}

// FuelGradeEQ applies the EQ predicate on the "fuel_grade" field.
func FuelGradeEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldFuelGrade, v))
}

// FuelGradeNEQ applies the NEQ predicate on the "fuel_grade" field.
func FuelGradeNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldFuelGrade, v))
}

// FuelGradeIn applies the In predicate on the "fuel_grade" field.
func FuelGradeIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldFuelGrade, vs...))
}

// FuelGradeNotIn applies the NotIn predicate on the "fuel_grade" field.
func FuelGradeNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldFuelGrade, vs...))
}

// FuelGradeGT applies the GT predicate on the "fuel_grade" field.
func FuelGradeGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldFuelGrade, v))
}

// FuelGradeGTE applies the GTE predicate on the "fuel_grade" field.
func FuelGradeGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldFuelGrade, v))
}

// FuelGradeLT applies the LT predicate on the "fuel_grade" field.
func FuelGradeLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldFuelGrade, v))
}

// FuelGradeLTE applies the LTE predicate on the "fuel_grade" field.
func FuelGradeLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldFuelGrade, v))
}

// FuelGradeContains applies the Contains predicate on the "fuel_grade" field.
func FuelGradeContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldFuelGrade, v))
}

// FuelGradeHasPrefix applies the HasPrefix predicate on the "fuel_grade" field.
func FuelGradeHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldFuelGrade, v))
}

// FuelGradeHasSuffix applies the HasSuffix predicate on the "fuel_grade" field.
func FuelGradeHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldFuelGrade, v))
}

// FuelGradeEqualFold applies the EqualFold predicate on the "fuel_grade" field.
func FuelGradeEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldFuelGrade, v))
}

// FuelGradeContainsFold applies the ContainsFold predicate on the "fuel_grade" field.
func FuelGradeContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldFuelGrade, v))
}

// TankEQ applies the EQ predicate on the "tank" field.
func TankEQ(v int) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldTank, v))
}

// TankNEQ applies the NEQ predicate on the "tank" field.
func TankNEQ(v int) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldTank, v))
}

// TankIn applies the In predicate on the "tank" field.
func TankIn(vs ...int) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldTank, vs...))
}

// TankNotIn applies the NotIn predicate on the "tank" field.
func TankNotIn(vs ...int) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldTank, vs...))
}

// TankGT applies the GT predicate on the "tank" field.
func TankGT(v int) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldTank, v))
}

// TankGTE applies the GTE predicate on the "tank" field.
func TankGTE(v int) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldTank, v))
}

// TankLT applies the LT predicate on the "tank" field.
func TankLT(v int) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldTank, v))
}

// TankLTE applies the LTE predicate on the "tank" field.
func TankLTE(v int) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldTank, v))
}

// UnitPriceEQ applies the EQ predicate on the "unit_price" field.
func UnitPriceEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldUnitPrice, v))
}

// UnitPriceNEQ applies the NEQ predicate on the "unit_price" field.
func UnitPriceNEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldUnitPrice, v))
}

// UnitPriceIn applies the In predicate on the "unit_price" field.
func UnitPriceIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldUnitPrice, vs...))
}

// UnitPriceNotIn applies the NotIn predicate on the "unit_price" field.
func UnitPriceNotIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldUnitPrice, vs...))
}

// UnitPriceGT applies the GT predicate on the "unit_price" field.
func UnitPriceGT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldUnitPrice, v))
}

// UnitPriceGTE applies the GTE predicate on the "unit_price" field.
func UnitPriceGTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldUnitPrice, v))
}

// UnitPriceLT applies the LT predicate on the "unit_price" field.
func UnitPriceLT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldUnitPrice, v))
}

// UnitPriceLTE applies the LTE predicate on the "unit_price" field.
func UnitPriceLTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldUnitPrice, v))
}

// VolumeEQ applies the EQ predicate on the "volume" field.
func VolumeEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldVolume, v))
}

// VolumeNEQ applies the NEQ predicate on the "volume" field.
func VolumeNEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldVolume, v))
}

// VolumeIn applies the In predicate on the "volume" field.
func VolumeIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldVolume, vs...))
}

// VolumeNotIn applies the NotIn predicate on the "volume" field.
func VolumeNotIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldVolume, vs...))
}

// VolumeGT applies the GT predicate on the "volume" field.
func VolumeGT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldVolume, v))
}

// VolumeGTE applies the GTE predicate on the "volume" field.
func VolumeGTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldVolume, v))
}

// VolumeLT applies the LT predicate on the "volume" field.
func VolumeLT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldVolume, v))
}

// VolumeLTE applies the LTE predicate on the "volume" field.
func VolumeLTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldVolume, v))
}

// AmountEQ applies the EQ predicate on the "amount" field.
func AmountEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldAmount, v))
}

// AmountNEQ applies the NEQ predicate on the "amount" field.
func AmountNEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldAmount, v))
}

// AmountIn applies the In predicate on the "amount" field.
func AmountIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldAmount, vs...))
}

// AmountNotIn applies the NotIn predicate on the "amount" field.
func AmountNotIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldAmount, vs...))
}

// AmountGT applies the GT predicate on the "amount" field.
func AmountGT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldAmount, v))
}

// AmountGTE applies the GTE predicate on the "amount" field.
func AmountGTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldAmount, v))
}

// AmountLT applies the LT predicate on the "amount" field.
func AmountLT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldAmount, v))
}

// AmountLTE applies the LTE predicate on the "amount" field.
func AmountLTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldAmount, v))
}

// TotalVolumeEQ applies the EQ predicate on the "total_volume" field.
func TotalVolumeEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldTotalVolume, v))
}

// TotalVolumeNEQ applies the NEQ predicate on the "total_volume" field.
func TotalVolumeNEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldTotalVolume, v))
}

// TotalVolumeIn applies the In predicate on the "total_volume" field.
func TotalVolumeIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldTotalVolume, vs...))
}

// TotalVolumeNotIn applies the NotIn predicate on the "total_volume" field.
func TotalVolumeNotIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldTotalVolume, vs...))
}

// TotalVolumeGT applies the GT predicate on the "total_volume" field.
func TotalVolumeGT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldTotalVolume, v))
}

// TotalVolumeGTE applies the GTE predicate on the "total_volume" field.
func TotalVolumeGTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldTotalVolume, v))
}

// TotalVolumeLT applies the LT predicate on the "total_volume" field.
func TotalVolumeLT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldTotalVolume, v))
}

// TotalVolumeLTE applies the LTE predicate on the "total_volume" field.
func TotalVolumeLTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldTotalVolume, v))
}

// TotalAmountEQ applies the EQ predicate on the "total_amount" field.
func TotalAmountEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldTotalAmount, v))
}

// TotalAmountNEQ applies the NEQ predicate on the "total_amount" field.
func TotalAmountNEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldTotalAmount, v))
}

// TotalAmountIn applies the In predicate on the "total_amount" field.
func TotalAmountIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldTotalAmount, vs...))
}

// TotalAmountNotIn applies the NotIn predicate on the "total_amount" field.
func TotalAmountNotIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldTotalAmount, vs...))
}

// TotalAmountGT applies the GT predicate on the "total_amount" field.
func TotalAmountGT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldTotalAmount, v))
}

// TotalAmountGTE applies the GTE predicate on the "total_amount" field.
func TotalAmountGTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldTotalAmount, v))
}

// TotalAmountLT applies the LT predicate on the "total_amount" field.
func TotalAmountLT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldTotalAmount, v))
}

// TotalAmountLTE applies the LTE predicate on the "total_amount" field.
func TotalAmountLTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldTotalAmount, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldStatus, v))
}

// MemberCardIDEQ applies the EQ predicate on the "member_card_id" field.
func MemberCardIDEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldMemberCardID, v))
}

// MemberCardIDNEQ applies the NEQ predicate on the "member_card_id" field.
func MemberCardIDNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldMemberCardID, v))
}

// MemberCardIDIn applies the In predicate on the "member_card_id" field.
func MemberCardIDIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldMemberCardID, vs...))
}

// MemberCardIDNotIn applies the NotIn predicate on the "member_card_id" field.
func MemberCardIDNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldMemberCardID, vs...))
}

// MemberCardIDGT applies the GT predicate on the "member_card_id" field.
func MemberCardIDGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldMemberCardID, v))
}

// MemberCardIDGTE applies the GTE predicate on the "member_card_id" field.
func MemberCardIDGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldMemberCardID, v))
}

// MemberCardIDLT applies the LT predicate on the "member_card_id" field.
func MemberCardIDLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldMemberCardID, v))
}

// MemberCardIDLTE applies the LTE predicate on the "member_card_id" field.
func MemberCardIDLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldMemberCardID, v))
}

// MemberCardIDContains applies the Contains predicate on the "member_card_id" field.
func MemberCardIDContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldMemberCardID, v))
}

// MemberCardIDHasPrefix applies the HasPrefix predicate on the "member_card_id" field.
func MemberCardIDHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldMemberCardID, v))
}

// MemberCardIDHasSuffix applies the HasSuffix predicate on the "member_card_id" field.
func MemberCardIDHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldMemberCardID, v))
}

// MemberCardIDIsNil applies the IsNil predicate on the "member_card_id" field.
func MemberCardIDIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldMemberCardID))
}

// MemberCardIDNotNil applies the NotNil predicate on the "member_card_id" field.
func MemberCardIDNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldMemberCardID))
}

// MemberCardIDEqualFold applies the EqualFold predicate on the "member_card_id" field.
func MemberCardIDEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldMemberCardID, v))
}

// MemberCardIDContainsFold applies the ContainsFold predicate on the "member_card_id" field.
func MemberCardIDContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldMemberCardID, v))
}

// MemberIDEQ applies the EQ predicate on the "member_id" field.
func MemberIDEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldMemberID, v))
}

// MemberIDNEQ applies the NEQ predicate on the "member_id" field.
func MemberIDNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldMemberID, v))
}

// MemberIDIn applies the In predicate on the "member_id" field.
func MemberIDIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldMemberID, vs...))
}

// MemberIDNotIn applies the NotIn predicate on the "member_id" field.
func MemberIDNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldMemberID, vs...))
}

// MemberIDGT applies the GT predicate on the "member_id" field.
func MemberIDGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldMemberID, v))
}

// MemberIDGTE applies the GTE predicate on the "member_id" field.
func MemberIDGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldMemberID, v))
}

// MemberIDLT applies the LT predicate on the "member_id" field.
func MemberIDLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldMemberID, v))
}

// MemberIDLTE applies the LTE predicate on the "member_id" field.
func MemberIDLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldMemberID, v))
}

// MemberIDContains applies the Contains predicate on the "member_id" field.
func MemberIDContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldMemberID, v))
}

// MemberIDHasPrefix applies the HasPrefix predicate on the "member_id" field.
func MemberIDHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldMemberID, v))
}

// MemberIDHasSuffix applies the HasSuffix predicate on the "member_id" field.
func MemberIDHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldMemberID, v))
}

// MemberIDIsNil applies the IsNil predicate on the "member_id" field.
func MemberIDIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldMemberID))
}

// MemberIDNotNil applies the NotNil predicate on the "member_id" field.
func MemberIDNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldMemberID))
}

// MemberIDEqualFold applies the EqualFold predicate on the "member_id" field.
func MemberIDEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldMemberID, v))
}

// MemberIDContainsFold applies the ContainsFold predicate on the "member_id" field.
func MemberIDContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldMemberID, v))
}

// EmployeeIDEQ applies the EQ predicate on the "employee_id" field.
func EmployeeIDEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldEmployeeID, v))
}

// EmployeeIDNEQ applies the NEQ predicate on the "employee_id" field.
func EmployeeIDNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldEmployeeID, v))
}

// EmployeeIDIn applies the In predicate on the "employee_id" field.
func EmployeeIDIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldEmployeeID, vs...))
}

// EmployeeIDNotIn applies the NotIn predicate on the "employee_id" field.
func EmployeeIDNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldEmployeeID, vs...))
}

// EmployeeIDGT applies the GT predicate on the "employee_id" field.
func EmployeeIDGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldEmployeeID, v))
}

// EmployeeIDGTE applies the GTE predicate on the "employee_id" field.
func EmployeeIDGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldEmployeeID, v))
}

// EmployeeIDLT applies the LT predicate on the "employee_id" field.
func EmployeeIDLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldEmployeeID, v))
}

// EmployeeIDLTE applies the LTE predicate on the "employee_id" field.
func EmployeeIDLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldEmployeeID, v))
}

// EmployeeIDContains applies the Contains predicate on the "employee_id" field.
func EmployeeIDContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldEmployeeID, v))
}

// EmployeeIDHasPrefix applies the HasPrefix predicate on the "employee_id" field.
func EmployeeIDHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldEmployeeID, v))
}

// EmployeeIDHasSuffix applies the HasSuffix predicate on the "employee_id" field.
func EmployeeIDHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldEmployeeID, v))
}

// EmployeeIDIsNil applies the IsNil predicate on the "employee_id" field.
func EmployeeIDIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldEmployeeID))
}

// EmployeeIDNotNil applies the NotNil predicate on the "employee_id" field.
func EmployeeIDNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldEmployeeID))
}

// EmployeeIDEqualFold applies the EqualFold predicate on the "employee_id" field.
func EmployeeIDEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldEmployeeID, v))
}

// EmployeeIDContainsFold applies the ContainsFold predicate on the "employee_id" field.
func EmployeeIDContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldEmployeeID, v))
}

// FccTransactionIDEQ applies the EQ predicate on the "fcc_transaction_id" field.
func FccTransactionIDEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldFccTransactionID, v))
}

// FccTransactionIDNEQ applies the NEQ predicate on the "fcc_transaction_id" field.
func FccTransactionIDNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldFccTransactionID, v))
}

// FccTransactionIDIn applies the In predicate on the "fcc_transaction_id" field.
func FccTransactionIDIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldFccTransactionID, vs...))
}

// FccTransactionIDNotIn applies the NotIn predicate on the "fcc_transaction_id" field.
func FccTransactionIDNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldFccTransactionID, vs...))
}

// FccTransactionIDGT applies the GT predicate on the "fcc_transaction_id" field.
func FccTransactionIDGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldFccTransactionID, v))
}

// FccTransactionIDGTE applies the GTE predicate on the "fcc_transaction_id" field.
func FccTransactionIDGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldFccTransactionID, v))
}

// FccTransactionIDLT applies the LT predicate on the "fcc_transaction_id" field.
func FccTransactionIDLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldFccTransactionID, v))
}

// FccTransactionIDLTE applies the LTE predicate on the "fcc_transaction_id" field.
func FccTransactionIDLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldFccTransactionID, v))
}

// FccTransactionIDContains applies the Contains predicate on the "fcc_transaction_id" field.
func FccTransactionIDContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldFccTransactionID, v))
}

// FccTransactionIDHasPrefix applies the HasPrefix predicate on the "fcc_transaction_id" field.
func FccTransactionIDHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldFccTransactionID, v))
}

// FccTransactionIDHasSuffix applies the HasSuffix predicate on the "fcc_transaction_id" field.
func FccTransactionIDHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldFccTransactionID, v))
}

// FccTransactionIDIsNil applies the IsNil predicate on the "fcc_transaction_id" field.
func FccTransactionIDIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldFccTransactionID))
}

// FccTransactionIDNotNil applies the NotNil predicate on the "fcc_transaction_id" field.
func FccTransactionIDNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldFccTransactionID))
}

// FccTransactionIDEqualFold applies the EqualFold predicate on the "fcc_transaction_id" field.
func FccTransactionIDEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldFccTransactionID, v))
}

// FccTransactionIDContainsFold applies the ContainsFold predicate on the "fcc_transaction_id" field.
func FccTransactionIDContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldFccTransactionID, v))
}

// PosTerminalIDEQ applies the EQ predicate on the "pos_terminal_id" field.
func PosTerminalIDEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldPosTerminalID, v))
}

// PosTerminalIDNEQ applies the NEQ predicate on the "pos_terminal_id" field.
func PosTerminalIDNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldPosTerminalID, v))
}

// PosTerminalIDIn applies the In predicate on the "pos_terminal_id" field.
func PosTerminalIDIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldPosTerminalID, vs...))
}

// PosTerminalIDNotIn applies the NotIn predicate on the "pos_terminal_id" field.
func PosTerminalIDNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldPosTerminalID, vs...))
}

// PosTerminalIDGT applies the GT predicate on the "pos_terminal_id" field.
func PosTerminalIDGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldPosTerminalID, v))
}

// PosTerminalIDGTE applies the GTE predicate on the "pos_terminal_id" field.
func PosTerminalIDGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldPosTerminalID, v))
}

// PosTerminalIDLT applies the LT predicate on the "pos_terminal_id" field.
func PosTerminalIDLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldPosTerminalID, v))
}

// PosTerminalIDLTE applies the LTE predicate on the "pos_terminal_id" field.
func PosTerminalIDLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldPosTerminalID, v))
}

// PosTerminalIDContains applies the Contains predicate on the "pos_terminal_id" field.
func PosTerminalIDContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldPosTerminalID, v))
}

// PosTerminalIDHasPrefix applies the HasPrefix predicate on the "pos_terminal_id" field.
func PosTerminalIDHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldPosTerminalID, v))
}

// PosTerminalIDHasSuffix applies the HasSuffix predicate on the "pos_terminal_id" field.
func PosTerminalIDHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldPosTerminalID, v))
}

// PosTerminalIDIsNil applies the IsNil predicate on the "pos_terminal_id" field.
func PosTerminalIDIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldPosTerminalID))
}

// PosTerminalIDNotNil applies the NotNil predicate on the "pos_terminal_id" field.
func PosTerminalIDNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldPosTerminalID))
}

// PosTerminalIDEqualFold applies the EqualFold predicate on the "pos_terminal_id" field.
func PosTerminalIDEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldPosTerminalID, v))
}

// PosTerminalIDContainsFold applies the ContainsFold predicate on the "pos_terminal_id" field.
func PosTerminalIDContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldPosTerminalID, v))
}

// MetadataIsNil applies the IsNil predicate on the "metadata" field.
func MetadataIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldMetadata))
}

// MetadataNotNil applies the NotNil predicate on the "metadata" field.
func MetadataNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldMetadata))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldUpdatedAt, v))
}

// ProcessedAtEQ applies the EQ predicate on the "processed_at" field.
func ProcessedAtEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldProcessedAt, v))
}

// ProcessedAtNEQ applies the NEQ predicate on the "processed_at" field.
func ProcessedAtNEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldProcessedAt, v))
}

// ProcessedAtIn applies the In predicate on the "processed_at" field.
func ProcessedAtIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldProcessedAt, vs...))
}

// ProcessedAtNotIn applies the NotIn predicate on the "processed_at" field.
func ProcessedAtNotIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldProcessedAt, vs...))
}

// ProcessedAtGT applies the GT predicate on the "processed_at" field.
func ProcessedAtGT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldProcessedAt, v))
}

// ProcessedAtGTE applies the GTE predicate on the "processed_at" field.
func ProcessedAtGTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldProcessedAt, v))
}

// ProcessedAtLT applies the LT predicate on the "processed_at" field.
func ProcessedAtLT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldProcessedAt, v))
}

// ProcessedAtLTE applies the LTE predicate on the "processed_at" field.
func ProcessedAtLTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldProcessedAt, v))
}

// ProcessedAtIsNil applies the IsNil predicate on the "processed_at" field.
func ProcessedAtIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldProcessedAt))
}

// ProcessedAtNotNil applies the NotNil predicate on the "processed_at" field.
func ProcessedAtNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldProcessedAt))
}

// CancelledAtEQ applies the EQ predicate on the "cancelled_at" field.
func CancelledAtEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldCancelledAt, v))
}

// CancelledAtNEQ applies the NEQ predicate on the "cancelled_at" field.
func CancelledAtNEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldCancelledAt, v))
}

// CancelledAtIn applies the In predicate on the "cancelled_at" field.
func CancelledAtIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldCancelledAt, vs...))
}

// CancelledAtNotIn applies the NotIn predicate on the "cancelled_at" field.
func CancelledAtNotIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldCancelledAt, vs...))
}

// CancelledAtGT applies the GT predicate on the "cancelled_at" field.
func CancelledAtGT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldCancelledAt, v))
}

// CancelledAtGTE applies the GTE predicate on the "cancelled_at" field.
func CancelledAtGTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldCancelledAt, v))
}

// CancelledAtLT applies the LT predicate on the "cancelled_at" field.
func CancelledAtLT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldCancelledAt, v))
}

// CancelledAtLTE applies the LTE predicate on the "cancelled_at" field.
func CancelledAtLTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldCancelledAt, v))
}

// CancelledAtIsNil applies the IsNil predicate on the "cancelled_at" field.
func CancelledAtIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldCancelledAt))
}

// CancelledAtNotNil applies the NotNil predicate on the "cancelled_at" field.
func CancelledAtNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldCancelledAt))
}

// StartTotalizerEQ applies the EQ predicate on the "start_totalizer" field.
func StartTotalizerEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldStartTotalizer, v))
}

// StartTotalizerNEQ applies the NEQ predicate on the "start_totalizer" field.
func StartTotalizerNEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldStartTotalizer, v))
}

// StartTotalizerIn applies the In predicate on the "start_totalizer" field.
func StartTotalizerIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldStartTotalizer, vs...))
}

// StartTotalizerNotIn applies the NotIn predicate on the "start_totalizer" field.
func StartTotalizerNotIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldStartTotalizer, vs...))
}

// StartTotalizerGT applies the GT predicate on the "start_totalizer" field.
func StartTotalizerGT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldStartTotalizer, v))
}

// StartTotalizerGTE applies the GTE predicate on the "start_totalizer" field.
func StartTotalizerGTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldStartTotalizer, v))
}

// StartTotalizerLT applies the LT predicate on the "start_totalizer" field.
func StartTotalizerLT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldStartTotalizer, v))
}

// StartTotalizerLTE applies the LTE predicate on the "start_totalizer" field.
func StartTotalizerLTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldStartTotalizer, v))
}

// StartTotalizerIsNil applies the IsNil predicate on the "start_totalizer" field.
func StartTotalizerIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldStartTotalizer))
}

// StartTotalizerNotNil applies the NotNil predicate on the "start_totalizer" field.
func StartTotalizerNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldStartTotalizer))
}

// EndTotalizerEQ applies the EQ predicate on the "end_totalizer" field.
func EndTotalizerEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldEndTotalizer, v))
}

// EndTotalizerNEQ applies the NEQ predicate on the "end_totalizer" field.
func EndTotalizerNEQ(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldEndTotalizer, v))
}

// EndTotalizerIn applies the In predicate on the "end_totalizer" field.
func EndTotalizerIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldEndTotalizer, vs...))
}

// EndTotalizerNotIn applies the NotIn predicate on the "end_totalizer" field.
func EndTotalizerNotIn(vs ...float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldEndTotalizer, vs...))
}

// EndTotalizerGT applies the GT predicate on the "end_totalizer" field.
func EndTotalizerGT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldEndTotalizer, v))
}

// EndTotalizerGTE applies the GTE predicate on the "end_totalizer" field.
func EndTotalizerGTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldEndTotalizer, v))
}

// EndTotalizerLT applies the LT predicate on the "end_totalizer" field.
func EndTotalizerLT(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldEndTotalizer, v))
}

// EndTotalizerLTE applies the LTE predicate on the "end_totalizer" field.
func EndTotalizerLTE(v float64) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldEndTotalizer, v))
}

// EndTotalizerIsNil applies the IsNil predicate on the "end_totalizer" field.
func EndTotalizerIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldEndTotalizer))
}

// EndTotalizerNotNil applies the NotNil predicate on the "end_totalizer" field.
func EndTotalizerNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldEndTotalizer))
}

// NozzleStartTimeEQ applies the EQ predicate on the "nozzle_start_time" field.
func NozzleStartTimeEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldNozzleStartTime, v))
}

// NozzleStartTimeNEQ applies the NEQ predicate on the "nozzle_start_time" field.
func NozzleStartTimeNEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldNozzleStartTime, v))
}

// NozzleStartTimeIn applies the In predicate on the "nozzle_start_time" field.
func NozzleStartTimeIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldNozzleStartTime, vs...))
}

// NozzleStartTimeNotIn applies the NotIn predicate on the "nozzle_start_time" field.
func NozzleStartTimeNotIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldNozzleStartTime, vs...))
}

// NozzleStartTimeGT applies the GT predicate on the "nozzle_start_time" field.
func NozzleStartTimeGT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldNozzleStartTime, v))
}

// NozzleStartTimeGTE applies the GTE predicate on the "nozzle_start_time" field.
func NozzleStartTimeGTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldNozzleStartTime, v))
}

// NozzleStartTimeLT applies the LT predicate on the "nozzle_start_time" field.
func NozzleStartTimeLT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldNozzleStartTime, v))
}

// NozzleStartTimeLTE applies the LTE predicate on the "nozzle_start_time" field.
func NozzleStartTimeLTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldNozzleStartTime, v))
}

// NozzleStartTimeIsNil applies the IsNil predicate on the "nozzle_start_time" field.
func NozzleStartTimeIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldNozzleStartTime))
}

// NozzleStartTimeNotNil applies the NotNil predicate on the "nozzle_start_time" field.
func NozzleStartTimeNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldNozzleStartTime))
}

// NozzleEndTimeEQ applies the EQ predicate on the "nozzle_end_time" field.
func NozzleEndTimeEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldNozzleEndTime, v))
}

// NozzleEndTimeNEQ applies the NEQ predicate on the "nozzle_end_time" field.
func NozzleEndTimeNEQ(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldNozzleEndTime, v))
}

// NozzleEndTimeIn applies the In predicate on the "nozzle_end_time" field.
func NozzleEndTimeIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldNozzleEndTime, vs...))
}

// NozzleEndTimeNotIn applies the NotIn predicate on the "nozzle_end_time" field.
func NozzleEndTimeNotIn(vs ...time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldNozzleEndTime, vs...))
}

// NozzleEndTimeGT applies the GT predicate on the "nozzle_end_time" field.
func NozzleEndTimeGT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldNozzleEndTime, v))
}

// NozzleEndTimeGTE applies the GTE predicate on the "nozzle_end_time" field.
func NozzleEndTimeGTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldNozzleEndTime, v))
}

// NozzleEndTimeLT applies the LT predicate on the "nozzle_end_time" field.
func NozzleEndTimeLT(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldNozzleEndTime, v))
}

// NozzleEndTimeLTE applies the LTE predicate on the "nozzle_end_time" field.
func NozzleEndTimeLTE(v time.Time) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldNozzleEndTime, v))
}

// NozzleEndTimeIsNil applies the IsNil predicate on the "nozzle_end_time" field.
func NozzleEndTimeIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldNozzleEndTime))
}

// NozzleEndTimeNotNil applies the NotNil predicate on the "nozzle_end_time" field.
func NozzleEndTimeNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldNozzleEndTime))
}

// StaffCardIDEQ applies the EQ predicate on the "staff_card_id" field.
func StaffCardIDEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEQ(FieldStaffCardID, v))
}

// StaffCardIDNEQ applies the NEQ predicate on the "staff_card_id" field.
func StaffCardIDNEQ(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNEQ(FieldStaffCardID, v))
}

// StaffCardIDIn applies the In predicate on the "staff_card_id" field.
func StaffCardIDIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIn(FieldStaffCardID, vs...))
}

// StaffCardIDNotIn applies the NotIn predicate on the "staff_card_id" field.
func StaffCardIDNotIn(vs ...string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotIn(FieldStaffCardID, vs...))
}

// StaffCardIDGT applies the GT predicate on the "staff_card_id" field.
func StaffCardIDGT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGT(FieldStaffCardID, v))
}

// StaffCardIDGTE applies the GTE predicate on the "staff_card_id" field.
func StaffCardIDGTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldGTE(FieldStaffCardID, v))
}

// StaffCardIDLT applies the LT predicate on the "staff_card_id" field.
func StaffCardIDLT(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLT(FieldStaffCardID, v))
}

// StaffCardIDLTE applies the LTE predicate on the "staff_card_id" field.
func StaffCardIDLTE(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldLTE(FieldStaffCardID, v))
}

// StaffCardIDContains applies the Contains predicate on the "staff_card_id" field.
func StaffCardIDContains(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContains(FieldStaffCardID, v))
}

// StaffCardIDHasPrefix applies the HasPrefix predicate on the "staff_card_id" field.
func StaffCardIDHasPrefix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasPrefix(FieldStaffCardID, v))
}

// StaffCardIDHasSuffix applies the HasSuffix predicate on the "staff_card_id" field.
func StaffCardIDHasSuffix(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldHasSuffix(FieldStaffCardID, v))
}

// StaffCardIDIsNil applies the IsNil predicate on the "staff_card_id" field.
func StaffCardIDIsNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldIsNull(FieldStaffCardID))
}

// StaffCardIDNotNil applies the NotNil predicate on the "staff_card_id" field.
func StaffCardIDNotNil() predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldNotNull(FieldStaffCardID))
}

// StaffCardIDEqualFold applies the EqualFold predicate on the "staff_card_id" field.
func StaffCardIDEqualFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldEqualFold(FieldStaffCardID, v))
}

// StaffCardIDContainsFold applies the ContainsFold predicate on the "staff_card_id" field.
func StaffCardIDContainsFold(v string) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.FieldContainsFold(FieldStaffCardID, v))
}

// HasOrderLink applies the HasEdge predicate on the "order_link" edge.
func HasOrderLink() predicate.FuelTransaction {
	return predicate.FuelTransaction(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, OrderLinkTable, OrderLinkColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasOrderLinkWith applies the HasEdge predicate on the "order_link" edge with a given conditions (other predicates).
func HasOrderLinkWith(preds ...predicate.FuelTransactionOrderLink) predicate.FuelTransaction {
	return predicate.FuelTransaction(func(s *sql.Selector) {
		step := newOrderLinkStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.FuelTransaction) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.FuelTransaction) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.FuelTransaction) predicate.FuelTransaction {
	return predicate.FuelTransaction(sql.NotPredicates(p))
}
