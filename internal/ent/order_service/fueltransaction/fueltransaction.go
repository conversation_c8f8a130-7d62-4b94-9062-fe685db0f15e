// Code generated by ent, DO NOT EDIT.

package fueltransaction

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the fueltransaction type in the database.
	Label = "fuel_transaction"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTransactionNumber holds the string denoting the transaction_number field in the database.
	FieldTransactionNumber = "transaction_number"
	// FieldStationID holds the string denoting the station_id field in the database.
	FieldStationID = "station_id"
	// FieldPumpID holds the string denoting the pump_id field in the database.
	FieldPumpID = "pump_id"
	// FieldNozzleID holds the string denoting the nozzle_id field in the database.
	FieldNozzleID = "nozzle_id"
	// FieldFuelType holds the string denoting the fuel_type field in the database.
	FieldFuelType = "fuel_type"
	// FieldFuelGrade holds the string denoting the fuel_grade field in the database.
	FieldFuelGrade = "fuel_grade"
	// FieldTank holds the string denoting the tank field in the database.
	FieldTank = "tank"
	// FieldUnitPrice holds the string denoting the unit_price field in the database.
	FieldUnitPrice = "unit_price"
	// FieldVolume holds the string denoting the volume field in the database.
	FieldVolume = "volume"
	// FieldAmount holds the string denoting the amount field in the database.
	FieldAmount = "amount"
	// FieldTotalVolume holds the string denoting the total_volume field in the database.
	FieldTotalVolume = "total_volume"
	// FieldTotalAmount holds the string denoting the total_amount field in the database.
	FieldTotalAmount = "total_amount"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldMemberCardID holds the string denoting the member_card_id field in the database.
	FieldMemberCardID = "member_card_id"
	// FieldMemberID holds the string denoting the member_id field in the database.
	FieldMemberID = "member_id"
	// FieldEmployeeID holds the string denoting the employee_id field in the database.
	FieldEmployeeID = "employee_id"
	// FieldFccTransactionID holds the string denoting the fcc_transaction_id field in the database.
	FieldFccTransactionID = "fcc_transaction_id"
	// FieldPosTerminalID holds the string denoting the pos_terminal_id field in the database.
	FieldPosTerminalID = "pos_terminal_id"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldProcessedAt holds the string denoting the processed_at field in the database.
	FieldProcessedAt = "processed_at"
	// FieldCancelledAt holds the string denoting the cancelled_at field in the database.
	FieldCancelledAt = "cancelled_at"
	// FieldStartTotalizer holds the string denoting the start_totalizer field in the database.
	FieldStartTotalizer = "start_totalizer"
	// FieldEndTotalizer holds the string denoting the end_totalizer field in the database.
	FieldEndTotalizer = "end_totalizer"
	// FieldNozzleStartTime holds the string denoting the nozzle_start_time field in the database.
	FieldNozzleStartTime = "nozzle_start_time"
	// FieldNozzleEndTime holds the string denoting the nozzle_end_time field in the database.
	FieldNozzleEndTime = "nozzle_end_time"
	// FieldStaffCardID holds the string denoting the staff_card_id field in the database.
	FieldStaffCardID = "staff_card_id"
	// EdgeOrderLink holds the string denoting the order_link edge name in mutations.
	EdgeOrderLink = "order_link"
	// Table holds the table name of the fueltransaction in the database.
	Table = "fuel_transactions"
	// OrderLinkTable is the table that holds the order_link relation/edge.
	OrderLinkTable = "fuel_transaction_order_links"
	// OrderLinkInverseTable is the table name for the FuelTransactionOrderLink entity.
	// It exists in this package in order to avoid circular dependency with the "fueltransactionorderlink" package.
	OrderLinkInverseTable = "fuel_transaction_order_links"
	// OrderLinkColumn is the table column denoting the order_link relation/edge.
	OrderLinkColumn = "fuel_transaction_id"
)

// Columns holds all SQL columns for fueltransaction fields.
var Columns = []string{
	FieldID,
	FieldTransactionNumber,
	FieldStationID,
	FieldPumpID,
	FieldNozzleID,
	FieldFuelType,
	FieldFuelGrade,
	FieldTank,
	FieldUnitPrice,
	FieldVolume,
	FieldAmount,
	FieldTotalVolume,
	FieldTotalAmount,
	FieldStatus,
	FieldMemberCardID,
	FieldMemberID,
	FieldEmployeeID,
	FieldFccTransactionID,
	FieldPosTerminalID,
	FieldMetadata,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldProcessedAt,
	FieldCancelledAt,
	FieldStartTotalizer,
	FieldEndTotalizer,
	FieldNozzleStartTime,
	FieldNozzleEndTime,
	FieldStaffCardID,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// TransactionNumberValidator is a validator for the "transaction_number" field. It is called by the builders before save.
	TransactionNumberValidator func(string) error
	// PumpIDValidator is a validator for the "pump_id" field. It is called by the builders before save.
	PumpIDValidator func(string) error
	// NozzleIDValidator is a validator for the "nozzle_id" field. It is called by the builders before save.
	NozzleIDValidator func(string) error
	// FuelTypeValidator is a validator for the "fuel_type" field. It is called by the builders before save.
	FuelTypeValidator func(string) error
	// FuelGradeValidator is a validator for the "fuel_grade" field. It is called by the builders before save.
	FuelGradeValidator func(string) error
	// DefaultTotalVolume holds the default value on creation for the "total_volume" field.
	DefaultTotalVolume float64
	// DefaultTotalAmount holds the default value on creation for the "total_amount" field.
	DefaultTotalAmount float64
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus string
	// StatusValidator is a validator for the "status" field. It is called by the builders before save.
	StatusValidator func(string) error
	// MemberCardIDValidator is a validator for the "member_card_id" field. It is called by the builders before save.
	MemberCardIDValidator func(string) error
	// MemberIDValidator is a validator for the "member_id" field. It is called by the builders before save.
	MemberIDValidator func(string) error
	// EmployeeIDValidator is a validator for the "employee_id" field. It is called by the builders before save.
	EmployeeIDValidator func(string) error
	// FccTransactionIDValidator is a validator for the "fcc_transaction_id" field. It is called by the builders before save.
	FccTransactionIDValidator func(string) error
	// PosTerminalIDValidator is a validator for the "pos_terminal_id" field. It is called by the builders before save.
	PosTerminalIDValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// StaffCardIDValidator is a validator for the "staff_card_id" field. It is called by the builders before save.
	StaffCardIDValidator func(string) error
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the FuelTransaction queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTransactionNumber orders the results by the transaction_number field.
func ByTransactionNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransactionNumber, opts...).ToFunc()
}

// ByStationID orders the results by the station_id field.
func ByStationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStationID, opts...).ToFunc()
}

// ByPumpID orders the results by the pump_id field.
func ByPumpID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPumpID, opts...).ToFunc()
}

// ByNozzleID orders the results by the nozzle_id field.
func ByNozzleID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNozzleID, opts...).ToFunc()
}

// ByFuelType orders the results by the fuel_type field.
func ByFuelType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFuelType, opts...).ToFunc()
}

// ByFuelGrade orders the results by the fuel_grade field.
func ByFuelGrade(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFuelGrade, opts...).ToFunc()
}

// ByTank orders the results by the tank field.
func ByTank(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTank, opts...).ToFunc()
}

// ByUnitPrice orders the results by the unit_price field.
func ByUnitPrice(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUnitPrice, opts...).ToFunc()
}

// ByVolume orders the results by the volume field.
func ByVolume(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVolume, opts...).ToFunc()
}

// ByAmount orders the results by the amount field.
func ByAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAmount, opts...).ToFunc()
}

// ByTotalVolume orders the results by the total_volume field.
func ByTotalVolume(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTotalVolume, opts...).ToFunc()
}

// ByTotalAmount orders the results by the total_amount field.
func ByTotalAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTotalAmount, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByMemberCardID orders the results by the member_card_id field.
func ByMemberCardID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMemberCardID, opts...).ToFunc()
}

// ByMemberID orders the results by the member_id field.
func ByMemberID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMemberID, opts...).ToFunc()
}

// ByEmployeeID orders the results by the employee_id field.
func ByEmployeeID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmployeeID, opts...).ToFunc()
}

// ByFccTransactionID orders the results by the fcc_transaction_id field.
func ByFccTransactionID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFccTransactionID, opts...).ToFunc()
}

// ByPosTerminalID orders the results by the pos_terminal_id field.
func ByPosTerminalID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPosTerminalID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByProcessedAt orders the results by the processed_at field.
func ByProcessedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProcessedAt, opts...).ToFunc()
}

// ByCancelledAt orders the results by the cancelled_at field.
func ByCancelledAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCancelledAt, opts...).ToFunc()
}

// ByStartTotalizer orders the results by the start_totalizer field.
func ByStartTotalizer(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStartTotalizer, opts...).ToFunc()
}

// ByEndTotalizer orders the results by the end_totalizer field.
func ByEndTotalizer(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEndTotalizer, opts...).ToFunc()
}

// ByNozzleStartTime orders the results by the nozzle_start_time field.
func ByNozzleStartTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNozzleStartTime, opts...).ToFunc()
}

// ByNozzleEndTime orders the results by the nozzle_end_time field.
func ByNozzleEndTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNozzleEndTime, opts...).ToFunc()
}

// ByStaffCardID orders the results by the staff_card_id field.
func ByStaffCardID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStaffCardID, opts...).ToFunc()
}

// ByOrderLinkField orders the results by order_link field.
func ByOrderLinkField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newOrderLinkStep(), sql.OrderByField(field, opts...))
	}
}
func newOrderLinkStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(OrderLinkInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2O, false, OrderLinkTable, OrderLinkColumn),
	)
}
