// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

// FuelTransactionOrderLinkUpdate is the builder for updating FuelTransactionOrderLink entities.
type FuelTransactionOrderLinkUpdate struct {
	config
	hooks    []Hook
	mutation *FuelTransactionOrderLinkMutation
}

// Where appends a list predicates to the FuelTransactionOrderLinkUpdate builder.
func (ftolu *FuelTransactionOrderLinkUpdate) Where(ps ...predicate.FuelTransactionOrderLink) *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.Where(ps...)
	return ftolu
}

// SetFuelTransactionID sets the "fuel_transaction_id" field.
func (ftolu *FuelTransactionOrderLinkUpdate) SetFuelTransactionID(s string) *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.SetFuelTransactionID(s)
	return ftolu
}

// SetNillableFuelTransactionID sets the "fuel_transaction_id" field if the given value is not nil.
func (ftolu *FuelTransactionOrderLinkUpdate) SetNillableFuelTransactionID(s *string) *FuelTransactionOrderLinkUpdate {
	if s != nil {
		ftolu.SetFuelTransactionID(*s)
	}
	return ftolu
}

// SetOrderID sets the "order_id" field.
func (ftolu *FuelTransactionOrderLinkUpdate) SetOrderID(s string) *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.SetOrderID(s)
	return ftolu
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (ftolu *FuelTransactionOrderLinkUpdate) SetNillableOrderID(s *string) *FuelTransactionOrderLinkUpdate {
	if s != nil {
		ftolu.SetOrderID(*s)
	}
	return ftolu
}

// SetAllocatedAmount sets the "allocated_amount" field.
func (ftolu *FuelTransactionOrderLinkUpdate) SetAllocatedAmount(f float64) *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.ResetAllocatedAmount()
	ftolu.mutation.SetAllocatedAmount(f)
	return ftolu
}

// SetNillableAllocatedAmount sets the "allocated_amount" field if the given value is not nil.
func (ftolu *FuelTransactionOrderLinkUpdate) SetNillableAllocatedAmount(f *float64) *FuelTransactionOrderLinkUpdate {
	if f != nil {
		ftolu.SetAllocatedAmount(*f)
	}
	return ftolu
}

// AddAllocatedAmount adds f to the "allocated_amount" field.
func (ftolu *FuelTransactionOrderLinkUpdate) AddAllocatedAmount(f float64) *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.AddAllocatedAmount(f)
	return ftolu
}

// SetStatus sets the "status" field.
func (ftolu *FuelTransactionOrderLinkUpdate) SetStatus(s string) *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.SetStatus(s)
	return ftolu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ftolu *FuelTransactionOrderLinkUpdate) SetNillableStatus(s *string) *FuelTransactionOrderLinkUpdate {
	if s != nil {
		ftolu.SetStatus(*s)
	}
	return ftolu
}

// SetMetadata sets the "metadata" field.
func (ftolu *FuelTransactionOrderLinkUpdate) SetMetadata(m map[string]interface{}) *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.SetMetadata(m)
	return ftolu
}

// ClearMetadata clears the value of the "metadata" field.
func (ftolu *FuelTransactionOrderLinkUpdate) ClearMetadata() *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.ClearMetadata()
	return ftolu
}

// SetUpdatedAt sets the "updated_at" field.
func (ftolu *FuelTransactionOrderLinkUpdate) SetUpdatedAt(t time.Time) *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.SetUpdatedAt(t)
	return ftolu
}

// SetDeactivatedAt sets the "deactivated_at" field.
func (ftolu *FuelTransactionOrderLinkUpdate) SetDeactivatedAt(t time.Time) *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.SetDeactivatedAt(t)
	return ftolu
}

// SetNillableDeactivatedAt sets the "deactivated_at" field if the given value is not nil.
func (ftolu *FuelTransactionOrderLinkUpdate) SetNillableDeactivatedAt(t *time.Time) *FuelTransactionOrderLinkUpdate {
	if t != nil {
		ftolu.SetDeactivatedAt(*t)
	}
	return ftolu
}

// ClearDeactivatedAt clears the value of the "deactivated_at" field.
func (ftolu *FuelTransactionOrderLinkUpdate) ClearDeactivatedAt() *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.ClearDeactivatedAt()
	return ftolu
}

// SetFuelTransaction sets the "fuel_transaction" edge to the FuelTransaction entity.
func (ftolu *FuelTransactionOrderLinkUpdate) SetFuelTransaction(f *FuelTransaction) *FuelTransactionOrderLinkUpdate {
	return ftolu.SetFuelTransactionID(f.ID)
}

// SetOrder sets the "order" edge to the Order entity.
func (ftolu *FuelTransactionOrderLinkUpdate) SetOrder(o *Order) *FuelTransactionOrderLinkUpdate {
	return ftolu.SetOrderID(o.ID)
}

// Mutation returns the FuelTransactionOrderLinkMutation object of the builder.
func (ftolu *FuelTransactionOrderLinkUpdate) Mutation() *FuelTransactionOrderLinkMutation {
	return ftolu.mutation
}

// ClearFuelTransaction clears the "fuel_transaction" edge to the FuelTransaction entity.
func (ftolu *FuelTransactionOrderLinkUpdate) ClearFuelTransaction() *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.ClearFuelTransaction()
	return ftolu
}

// ClearOrder clears the "order" edge to the Order entity.
func (ftolu *FuelTransactionOrderLinkUpdate) ClearOrder() *FuelTransactionOrderLinkUpdate {
	ftolu.mutation.ClearOrder()
	return ftolu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ftolu *FuelTransactionOrderLinkUpdate) Save(ctx context.Context) (int, error) {
	ftolu.defaults()
	return withHooks(ctx, ftolu.sqlSave, ftolu.mutation, ftolu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ftolu *FuelTransactionOrderLinkUpdate) SaveX(ctx context.Context) int {
	affected, err := ftolu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ftolu *FuelTransactionOrderLinkUpdate) Exec(ctx context.Context) error {
	_, err := ftolu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ftolu *FuelTransactionOrderLinkUpdate) ExecX(ctx context.Context) {
	if err := ftolu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ftolu *FuelTransactionOrderLinkUpdate) defaults() {
	if _, ok := ftolu.mutation.UpdatedAt(); !ok {
		v := fueltransactionorderlink.UpdateDefaultUpdatedAt()
		ftolu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ftolu *FuelTransactionOrderLinkUpdate) check() error {
	if v, ok := ftolu.mutation.Status(); ok {
		if err := fueltransactionorderlink.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`order_service: validator failed for field "FuelTransactionOrderLink.status": %w`, err)}
		}
	}
	if _, ok := ftolu.mutation.FuelTransactionID(); ftolu.mutation.FuelTransactionCleared() && !ok {
		return errors.New(`order_service: clearing a required unique edge "FuelTransactionOrderLink.fuel_transaction"`)
	}
	if _, ok := ftolu.mutation.OrderID(); ftolu.mutation.OrderCleared() && !ok {
		return errors.New(`order_service: clearing a required unique edge "FuelTransactionOrderLink.order"`)
	}
	return nil
}

func (ftolu *FuelTransactionOrderLinkUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ftolu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(fueltransactionorderlink.Table, fueltransactionorderlink.Columns, sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString))
	if ps := ftolu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ftolu.mutation.AllocatedAmount(); ok {
		_spec.SetField(fueltransactionorderlink.FieldAllocatedAmount, field.TypeFloat64, value)
	}
	if value, ok := ftolu.mutation.AddedAllocatedAmount(); ok {
		_spec.AddField(fueltransactionorderlink.FieldAllocatedAmount, field.TypeFloat64, value)
	}
	if value, ok := ftolu.mutation.Status(); ok {
		_spec.SetField(fueltransactionorderlink.FieldStatus, field.TypeString, value)
	}
	if value, ok := ftolu.mutation.Metadata(); ok {
		_spec.SetField(fueltransactionorderlink.FieldMetadata, field.TypeJSON, value)
	}
	if ftolu.mutation.MetadataCleared() {
		_spec.ClearField(fueltransactionorderlink.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ftolu.mutation.UpdatedAt(); ok {
		_spec.SetField(fueltransactionorderlink.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ftolu.mutation.DeactivatedAt(); ok {
		_spec.SetField(fueltransactionorderlink.FieldDeactivatedAt, field.TypeTime, value)
	}
	if ftolu.mutation.DeactivatedAtCleared() {
		_spec.ClearField(fueltransactionorderlink.FieldDeactivatedAt, field.TypeTime)
	}
	if ftolu.mutation.FuelTransactionCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   fueltransactionorderlink.FuelTransactionTable,
			Columns: []string{fueltransactionorderlink.FuelTransactionColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransaction.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ftolu.mutation.FuelTransactionIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   fueltransactionorderlink.FuelTransactionTable,
			Columns: []string{fueltransactionorderlink.FuelTransactionColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransaction.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ftolu.mutation.OrderCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   fueltransactionorderlink.OrderTable,
			Columns: []string{fueltransactionorderlink.OrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(order.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ftolu.mutation.OrderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   fueltransactionorderlink.OrderTable,
			Columns: []string{fueltransactionorderlink.OrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(order.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ftolu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{fueltransactionorderlink.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ftolu.mutation.done = true
	return n, nil
}

// FuelTransactionOrderLinkUpdateOne is the builder for updating a single FuelTransactionOrderLink entity.
type FuelTransactionOrderLinkUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *FuelTransactionOrderLinkMutation
}

// SetFuelTransactionID sets the "fuel_transaction_id" field.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetFuelTransactionID(s string) *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.SetFuelTransactionID(s)
	return ftoluo
}

// SetNillableFuelTransactionID sets the "fuel_transaction_id" field if the given value is not nil.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetNillableFuelTransactionID(s *string) *FuelTransactionOrderLinkUpdateOne {
	if s != nil {
		ftoluo.SetFuelTransactionID(*s)
	}
	return ftoluo
}

// SetOrderID sets the "order_id" field.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetOrderID(s string) *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.SetOrderID(s)
	return ftoluo
}

// SetNillableOrderID sets the "order_id" field if the given value is not nil.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetNillableOrderID(s *string) *FuelTransactionOrderLinkUpdateOne {
	if s != nil {
		ftoluo.SetOrderID(*s)
	}
	return ftoluo
}

// SetAllocatedAmount sets the "allocated_amount" field.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetAllocatedAmount(f float64) *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.ResetAllocatedAmount()
	ftoluo.mutation.SetAllocatedAmount(f)
	return ftoluo
}

// SetNillableAllocatedAmount sets the "allocated_amount" field if the given value is not nil.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetNillableAllocatedAmount(f *float64) *FuelTransactionOrderLinkUpdateOne {
	if f != nil {
		ftoluo.SetAllocatedAmount(*f)
	}
	return ftoluo
}

// AddAllocatedAmount adds f to the "allocated_amount" field.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) AddAllocatedAmount(f float64) *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.AddAllocatedAmount(f)
	return ftoluo
}

// SetStatus sets the "status" field.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetStatus(s string) *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.SetStatus(s)
	return ftoluo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetNillableStatus(s *string) *FuelTransactionOrderLinkUpdateOne {
	if s != nil {
		ftoluo.SetStatus(*s)
	}
	return ftoluo
}

// SetMetadata sets the "metadata" field.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetMetadata(m map[string]interface{}) *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.SetMetadata(m)
	return ftoluo
}

// ClearMetadata clears the value of the "metadata" field.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) ClearMetadata() *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.ClearMetadata()
	return ftoluo
}

// SetUpdatedAt sets the "updated_at" field.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetUpdatedAt(t time.Time) *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.SetUpdatedAt(t)
	return ftoluo
}

// SetDeactivatedAt sets the "deactivated_at" field.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetDeactivatedAt(t time.Time) *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.SetDeactivatedAt(t)
	return ftoluo
}

// SetNillableDeactivatedAt sets the "deactivated_at" field if the given value is not nil.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetNillableDeactivatedAt(t *time.Time) *FuelTransactionOrderLinkUpdateOne {
	if t != nil {
		ftoluo.SetDeactivatedAt(*t)
	}
	return ftoluo
}

// ClearDeactivatedAt clears the value of the "deactivated_at" field.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) ClearDeactivatedAt() *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.ClearDeactivatedAt()
	return ftoluo
}

// SetFuelTransaction sets the "fuel_transaction" edge to the FuelTransaction entity.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetFuelTransaction(f *FuelTransaction) *FuelTransactionOrderLinkUpdateOne {
	return ftoluo.SetFuelTransactionID(f.ID)
}

// SetOrder sets the "order" edge to the Order entity.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SetOrder(o *Order) *FuelTransactionOrderLinkUpdateOne {
	return ftoluo.SetOrderID(o.ID)
}

// Mutation returns the FuelTransactionOrderLinkMutation object of the builder.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) Mutation() *FuelTransactionOrderLinkMutation {
	return ftoluo.mutation
}

// ClearFuelTransaction clears the "fuel_transaction" edge to the FuelTransaction entity.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) ClearFuelTransaction() *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.ClearFuelTransaction()
	return ftoluo
}

// ClearOrder clears the "order" edge to the Order entity.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) ClearOrder() *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.ClearOrder()
	return ftoluo
}

// Where appends a list predicates to the FuelTransactionOrderLinkUpdate builder.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) Where(ps ...predicate.FuelTransactionOrderLink) *FuelTransactionOrderLinkUpdateOne {
	ftoluo.mutation.Where(ps...)
	return ftoluo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) Select(field string, fields ...string) *FuelTransactionOrderLinkUpdateOne {
	ftoluo.fields = append([]string{field}, fields...)
	return ftoluo
}

// Save executes the query and returns the updated FuelTransactionOrderLink entity.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) Save(ctx context.Context) (*FuelTransactionOrderLink, error) {
	ftoluo.defaults()
	return withHooks(ctx, ftoluo.sqlSave, ftoluo.mutation, ftoluo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) SaveX(ctx context.Context) *FuelTransactionOrderLink {
	node, err := ftoluo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) Exec(ctx context.Context) error {
	_, err := ftoluo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) ExecX(ctx context.Context) {
	if err := ftoluo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) defaults() {
	if _, ok := ftoluo.mutation.UpdatedAt(); !ok {
		v := fueltransactionorderlink.UpdateDefaultUpdatedAt()
		ftoluo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ftoluo *FuelTransactionOrderLinkUpdateOne) check() error {
	if v, ok := ftoluo.mutation.Status(); ok {
		if err := fueltransactionorderlink.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`order_service: validator failed for field "FuelTransactionOrderLink.status": %w`, err)}
		}
	}
	if _, ok := ftoluo.mutation.FuelTransactionID(); ftoluo.mutation.FuelTransactionCleared() && !ok {
		return errors.New(`order_service: clearing a required unique edge "FuelTransactionOrderLink.fuel_transaction"`)
	}
	if _, ok := ftoluo.mutation.OrderID(); ftoluo.mutation.OrderCleared() && !ok {
		return errors.New(`order_service: clearing a required unique edge "FuelTransactionOrderLink.order"`)
	}
	return nil
}

func (ftoluo *FuelTransactionOrderLinkUpdateOne) sqlSave(ctx context.Context) (_node *FuelTransactionOrderLink, err error) {
	if err := ftoluo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(fueltransactionorderlink.Table, fueltransactionorderlink.Columns, sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString))
	id, ok := ftoluo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`order_service: missing "FuelTransactionOrderLink.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ftoluo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, fueltransactionorderlink.FieldID)
		for _, f := range fields {
			if !fueltransactionorderlink.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("order_service: invalid field %q for query", f)}
			}
			if f != fueltransactionorderlink.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ftoluo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ftoluo.mutation.AllocatedAmount(); ok {
		_spec.SetField(fueltransactionorderlink.FieldAllocatedAmount, field.TypeFloat64, value)
	}
	if value, ok := ftoluo.mutation.AddedAllocatedAmount(); ok {
		_spec.AddField(fueltransactionorderlink.FieldAllocatedAmount, field.TypeFloat64, value)
	}
	if value, ok := ftoluo.mutation.Status(); ok {
		_spec.SetField(fueltransactionorderlink.FieldStatus, field.TypeString, value)
	}
	if value, ok := ftoluo.mutation.Metadata(); ok {
		_spec.SetField(fueltransactionorderlink.FieldMetadata, field.TypeJSON, value)
	}
	if ftoluo.mutation.MetadataCleared() {
		_spec.ClearField(fueltransactionorderlink.FieldMetadata, field.TypeJSON)
	}
	if value, ok := ftoluo.mutation.UpdatedAt(); ok {
		_spec.SetField(fueltransactionorderlink.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ftoluo.mutation.DeactivatedAt(); ok {
		_spec.SetField(fueltransactionorderlink.FieldDeactivatedAt, field.TypeTime, value)
	}
	if ftoluo.mutation.DeactivatedAtCleared() {
		_spec.ClearField(fueltransactionorderlink.FieldDeactivatedAt, field.TypeTime)
	}
	if ftoluo.mutation.FuelTransactionCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   fueltransactionorderlink.FuelTransactionTable,
			Columns: []string{fueltransactionorderlink.FuelTransactionColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransaction.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ftoluo.mutation.FuelTransactionIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   fueltransactionorderlink.FuelTransactionTable,
			Columns: []string{fueltransactionorderlink.FuelTransactionColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransaction.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ftoluo.mutation.OrderCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   fueltransactionorderlink.OrderTable,
			Columns: []string{fueltransactionorderlink.OrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(order.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ftoluo.mutation.OrderIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   fueltransactionorderlink.OrderTable,
			Columns: []string{fueltransactionorderlink.OrderColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(order.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &FuelTransactionOrderLink{config: ftoluo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ftoluo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{fueltransactionorderlink.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ftoluo.mutation.done = true
	return _node, nil
}
