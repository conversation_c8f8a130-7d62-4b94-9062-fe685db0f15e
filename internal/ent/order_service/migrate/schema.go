// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// FuelTransactionsColumns holds the columns for the "fuel_transactions" table.
	FuelTransactionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Size: 255},
		{Name: "transaction_number", Type: field.TypeString, Size: 50},
		{Name: "station_id", Type: field.TypeInt64},
		{Name: "pump_id", Type: field.TypeString, Size: 50},
		{Name: "nozzle_id", Type: field.TypeString, Size: 50},
		{Name: "fuel_type", Type: field.TypeString, Size: 50},
		{Name: "fuel_grade", Type: field.TypeString, Size: 50},
		{Name: "tank", Type: field.TypeInt},
		{Name: "unit_price", Type: field.TypeFloat64, SchemaType: map[string]string{"postgres": "numeric(10,2)"}},
		{Name: "volume", Type: field.TypeFloat64, SchemaType: map[string]string{"postgres": "numeric(10,3)"}},
		{Name: "amount", Type: field.TypeFloat64, SchemaType: map[string]string{"postgres": "numeric(12,2)"}},
		{Name: "total_volume", Type: field.TypeFloat64, Default: 0, SchemaType: map[string]string{"postgres": "numeric(10,3)"}},
		{Name: "total_amount", Type: field.TypeFloat64, Default: 0, SchemaType: map[string]string{"postgres": "numeric(12,2)"}},
		{Name: "status", Type: field.TypeString, Size: 20, Default: "pending"},
		{Name: "member_card_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "member_id", Type: field.TypeString, Nullable: true, Size: 255},
		{Name: "employee_id", Type: field.TypeString, Nullable: true, Size: 255},
		{Name: "fcc_transaction_id", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "pos_terminal_id", Type: field.TypeString, Nullable: true, Size: 50},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "processed_at", Type: field.TypeTime, Nullable: true},
		{Name: "cancelled_at", Type: field.TypeTime, Nullable: true},
		{Name: "start_totalizer", Type: field.TypeFloat64, Nullable: true, SchemaType: map[string]string{"postgres": "numeric(15,3)"}},
		{Name: "end_totalizer", Type: field.TypeFloat64, Nullable: true, SchemaType: map[string]string{"postgres": "numeric(15,3)"}},
		{Name: "nozzle_start_time", Type: field.TypeTime, Nullable: true},
		{Name: "nozzle_end_time", Type: field.TypeTime, Nullable: true},
		{Name: "staff_card_id", Type: field.TypeString, Nullable: true, Size: 255},
	}
	// FuelTransactionsTable holds the schema information for the "fuel_transactions" table.
	FuelTransactionsTable = &schema.Table{
		Name:       "fuel_transactions",
		Columns:    FuelTransactionsColumns,
		PrimaryKey: []*schema.Column{FuelTransactionsColumns[0]},
	}
	// FuelTransactionOrderLinksColumns holds the columns for the "fuel_transaction_order_links" table.
	FuelTransactionOrderLinksColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Size: 255},
		{Name: "allocated_amount", Type: field.TypeFloat64, SchemaType: map[string]string{"postgres": "numeric(12,2)"}},
		{Name: "status", Type: field.TypeString, Size: 20, Default: "active"},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deactivated_at", Type: field.TypeTime, Nullable: true},
		{Name: "fuel_transaction_id", Type: field.TypeString, Unique: true, Size: 255},
		{Name: "order_id", Type: field.TypeString, Unique: true, Size: 255},
	}
	// FuelTransactionOrderLinksTable holds the schema information for the "fuel_transaction_order_links" table.
	FuelTransactionOrderLinksTable = &schema.Table{
		Name:       "fuel_transaction_order_links",
		Columns:    FuelTransactionOrderLinksColumns,
		PrimaryKey: []*schema.Column{FuelTransactionOrderLinksColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "fuel_transaction_order_links_fuel_transactions_order_link",
				Columns:    []*schema.Column{FuelTransactionOrderLinksColumns[7]},
				RefColumns: []*schema.Column{FuelTransactionsColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "fuel_transaction_order_links_orders_fuel_transaction_link",
				Columns:    []*schema.Column{FuelTransactionOrderLinksColumns[8]},
				RefColumns: []*schema.Column{OrdersColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// OrdersColumns holds the columns for the "orders" table.
	OrdersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Size: 255},
		{Name: "order_number", Type: field.TypeString, Size: 100},
		{Name: "customer_id", Type: field.TypeInt64, Nullable: true},
		{Name: "customer_phone", Type: field.TypeString, Nullable: true, Size: 20},
		{Name: "license_plate", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "customer_name", Type: field.TypeString, Nullable: true, Size: 100},
		{Name: "station_id", Type: field.TypeInt64},
		{Name: "status", Type: field.TypeString, Size: 20, Default: "new"},
		{Name: "total_amount", Type: field.TypeFloat64, Default: 0, SchemaType: map[string]string{"postgres": "numeric(12,2)"}},
		{Name: "discount_amount", Type: field.TypeFloat64, Default: 0, SchemaType: map[string]string{"postgres": "numeric(12,2)"}},
		{Name: "final_amount", Type: field.TypeFloat64, Default: 0, SchemaType: map[string]string{"postgres": "numeric(12,2)"}},
		{Name: "tax_amount", Type: field.TypeFloat64, Default: 0, SchemaType: map[string]string{"postgres": "numeric(12,2)"}},
		{Name: "paid_amount", Type: field.TypeFloat64, Default: 0, SchemaType: map[string]string{"postgres": "numeric(12,2)"}},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "completed_at", Type: field.TypeTime, Nullable: true},
		{Name: "cancelled_at", Type: field.TypeTime, Nullable: true},
		{Name: "employee_no", Type: field.TypeString, Nullable: true, Size: 50},
		{Name: "staff_card_id", Type: field.TypeInt64, Nullable: true},
	}
	// OrdersTable holds the schema information for the "orders" table.
	OrdersTable = &schema.Table{
		Name:       "orders",
		Columns:    OrdersColumns,
		PrimaryKey: []*schema.Column{OrdersColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		FuelTransactionsTable,
		FuelTransactionOrderLinksTable,
		OrdersTable,
	}
)

func init() {
	FuelTransactionOrderLinksTable.ForeignKeys[0].RefTable = FuelTransactionsTable
	FuelTransactionOrderLinksTable.ForeignKeys[1].RefTable = OrdersTable
}
