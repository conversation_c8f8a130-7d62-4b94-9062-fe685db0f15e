// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
)

// FuelTransactionCreate is the builder for creating a FuelTransaction entity.
type FuelTransactionCreate struct {
	config
	mutation *FuelTransactionMutation
	hooks    []Hook
}

// SetTransactionNumber sets the "transaction_number" field.
func (ftc *FuelTransactionCreate) SetTransactionNumber(s string) *FuelTransactionCreate {
	ftc.mutation.SetTransactionNumber(s)
	return ftc
}

// SetStationID sets the "station_id" field.
func (ftc *FuelTransactionCreate) SetStationID(i int64) *FuelTransactionCreate {
	ftc.mutation.SetStationID(i)
	return ftc
}

// SetPumpID sets the "pump_id" field.
func (ftc *FuelTransactionCreate) SetPumpID(s string) *FuelTransactionCreate {
	ftc.mutation.SetPumpID(s)
	return ftc
}

// SetNozzleID sets the "nozzle_id" field.
func (ftc *FuelTransactionCreate) SetNozzleID(s string) *FuelTransactionCreate {
	ftc.mutation.SetNozzleID(s)
	return ftc
}

// SetFuelType sets the "fuel_type" field.
func (ftc *FuelTransactionCreate) SetFuelType(s string) *FuelTransactionCreate {
	ftc.mutation.SetFuelType(s)
	return ftc
}

// SetFuelGrade sets the "fuel_grade" field.
func (ftc *FuelTransactionCreate) SetFuelGrade(s string) *FuelTransactionCreate {
	ftc.mutation.SetFuelGrade(s)
	return ftc
}

// SetTank sets the "tank" field.
func (ftc *FuelTransactionCreate) SetTank(i int) *FuelTransactionCreate {
	ftc.mutation.SetTank(i)
	return ftc
}

// SetUnitPrice sets the "unit_price" field.
func (ftc *FuelTransactionCreate) SetUnitPrice(f float64) *FuelTransactionCreate {
	ftc.mutation.SetUnitPrice(f)
	return ftc
}

// SetVolume sets the "volume" field.
func (ftc *FuelTransactionCreate) SetVolume(f float64) *FuelTransactionCreate {
	ftc.mutation.SetVolume(f)
	return ftc
}

// SetAmount sets the "amount" field.
func (ftc *FuelTransactionCreate) SetAmount(f float64) *FuelTransactionCreate {
	ftc.mutation.SetAmount(f)
	return ftc
}

// SetTotalVolume sets the "total_volume" field.
func (ftc *FuelTransactionCreate) SetTotalVolume(f float64) *FuelTransactionCreate {
	ftc.mutation.SetTotalVolume(f)
	return ftc
}

// SetNillableTotalVolume sets the "total_volume" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableTotalVolume(f *float64) *FuelTransactionCreate {
	if f != nil {
		ftc.SetTotalVolume(*f)
	}
	return ftc
}

// SetTotalAmount sets the "total_amount" field.
func (ftc *FuelTransactionCreate) SetTotalAmount(f float64) *FuelTransactionCreate {
	ftc.mutation.SetTotalAmount(f)
	return ftc
}

// SetNillableTotalAmount sets the "total_amount" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableTotalAmount(f *float64) *FuelTransactionCreate {
	if f != nil {
		ftc.SetTotalAmount(*f)
	}
	return ftc
}

// SetStatus sets the "status" field.
func (ftc *FuelTransactionCreate) SetStatus(s string) *FuelTransactionCreate {
	ftc.mutation.SetStatus(s)
	return ftc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableStatus(s *string) *FuelTransactionCreate {
	if s != nil {
		ftc.SetStatus(*s)
	}
	return ftc
}

// SetMemberCardID sets the "member_card_id" field.
func (ftc *FuelTransactionCreate) SetMemberCardID(s string) *FuelTransactionCreate {
	ftc.mutation.SetMemberCardID(s)
	return ftc
}

// SetNillableMemberCardID sets the "member_card_id" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableMemberCardID(s *string) *FuelTransactionCreate {
	if s != nil {
		ftc.SetMemberCardID(*s)
	}
	return ftc
}

// SetMemberID sets the "member_id" field.
func (ftc *FuelTransactionCreate) SetMemberID(s string) *FuelTransactionCreate {
	ftc.mutation.SetMemberID(s)
	return ftc
}

// SetNillableMemberID sets the "member_id" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableMemberID(s *string) *FuelTransactionCreate {
	if s != nil {
		ftc.SetMemberID(*s)
	}
	return ftc
}

// SetEmployeeID sets the "employee_id" field.
func (ftc *FuelTransactionCreate) SetEmployeeID(s string) *FuelTransactionCreate {
	ftc.mutation.SetEmployeeID(s)
	return ftc
}

// SetNillableEmployeeID sets the "employee_id" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableEmployeeID(s *string) *FuelTransactionCreate {
	if s != nil {
		ftc.SetEmployeeID(*s)
	}
	return ftc
}

// SetFccTransactionID sets the "fcc_transaction_id" field.
func (ftc *FuelTransactionCreate) SetFccTransactionID(s string) *FuelTransactionCreate {
	ftc.mutation.SetFccTransactionID(s)
	return ftc
}

// SetNillableFccTransactionID sets the "fcc_transaction_id" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableFccTransactionID(s *string) *FuelTransactionCreate {
	if s != nil {
		ftc.SetFccTransactionID(*s)
	}
	return ftc
}

// SetPosTerminalID sets the "pos_terminal_id" field.
func (ftc *FuelTransactionCreate) SetPosTerminalID(s string) *FuelTransactionCreate {
	ftc.mutation.SetPosTerminalID(s)
	return ftc
}

// SetNillablePosTerminalID sets the "pos_terminal_id" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillablePosTerminalID(s *string) *FuelTransactionCreate {
	if s != nil {
		ftc.SetPosTerminalID(*s)
	}
	return ftc
}

// SetMetadata sets the "metadata" field.
func (ftc *FuelTransactionCreate) SetMetadata(m map[string]interface{}) *FuelTransactionCreate {
	ftc.mutation.SetMetadata(m)
	return ftc
}

// SetCreatedAt sets the "created_at" field.
func (ftc *FuelTransactionCreate) SetCreatedAt(t time.Time) *FuelTransactionCreate {
	ftc.mutation.SetCreatedAt(t)
	return ftc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableCreatedAt(t *time.Time) *FuelTransactionCreate {
	if t != nil {
		ftc.SetCreatedAt(*t)
	}
	return ftc
}

// SetUpdatedAt sets the "updated_at" field.
func (ftc *FuelTransactionCreate) SetUpdatedAt(t time.Time) *FuelTransactionCreate {
	ftc.mutation.SetUpdatedAt(t)
	return ftc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableUpdatedAt(t *time.Time) *FuelTransactionCreate {
	if t != nil {
		ftc.SetUpdatedAt(*t)
	}
	return ftc
}

// SetProcessedAt sets the "processed_at" field.
func (ftc *FuelTransactionCreate) SetProcessedAt(t time.Time) *FuelTransactionCreate {
	ftc.mutation.SetProcessedAt(t)
	return ftc
}

// SetNillableProcessedAt sets the "processed_at" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableProcessedAt(t *time.Time) *FuelTransactionCreate {
	if t != nil {
		ftc.SetProcessedAt(*t)
	}
	return ftc
}

// SetCancelledAt sets the "cancelled_at" field.
func (ftc *FuelTransactionCreate) SetCancelledAt(t time.Time) *FuelTransactionCreate {
	ftc.mutation.SetCancelledAt(t)
	return ftc
}

// SetNillableCancelledAt sets the "cancelled_at" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableCancelledAt(t *time.Time) *FuelTransactionCreate {
	if t != nil {
		ftc.SetCancelledAt(*t)
	}
	return ftc
}

// SetStartTotalizer sets the "start_totalizer" field.
func (ftc *FuelTransactionCreate) SetStartTotalizer(f float64) *FuelTransactionCreate {
	ftc.mutation.SetStartTotalizer(f)
	return ftc
}

// SetNillableStartTotalizer sets the "start_totalizer" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableStartTotalizer(f *float64) *FuelTransactionCreate {
	if f != nil {
		ftc.SetStartTotalizer(*f)
	}
	return ftc
}

// SetEndTotalizer sets the "end_totalizer" field.
func (ftc *FuelTransactionCreate) SetEndTotalizer(f float64) *FuelTransactionCreate {
	ftc.mutation.SetEndTotalizer(f)
	return ftc
}

// SetNillableEndTotalizer sets the "end_totalizer" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableEndTotalizer(f *float64) *FuelTransactionCreate {
	if f != nil {
		ftc.SetEndTotalizer(*f)
	}
	return ftc
}

// SetNozzleStartTime sets the "nozzle_start_time" field.
func (ftc *FuelTransactionCreate) SetNozzleStartTime(t time.Time) *FuelTransactionCreate {
	ftc.mutation.SetNozzleStartTime(t)
	return ftc
}

// SetNillableNozzleStartTime sets the "nozzle_start_time" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableNozzleStartTime(t *time.Time) *FuelTransactionCreate {
	if t != nil {
		ftc.SetNozzleStartTime(*t)
	}
	return ftc
}

// SetNozzleEndTime sets the "nozzle_end_time" field.
func (ftc *FuelTransactionCreate) SetNozzleEndTime(t time.Time) *FuelTransactionCreate {
	ftc.mutation.SetNozzleEndTime(t)
	return ftc
}

// SetNillableNozzleEndTime sets the "nozzle_end_time" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableNozzleEndTime(t *time.Time) *FuelTransactionCreate {
	if t != nil {
		ftc.SetNozzleEndTime(*t)
	}
	return ftc
}

// SetStaffCardID sets the "staff_card_id" field.
func (ftc *FuelTransactionCreate) SetStaffCardID(s string) *FuelTransactionCreate {
	ftc.mutation.SetStaffCardID(s)
	return ftc
}

// SetNillableStaffCardID sets the "staff_card_id" field if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableStaffCardID(s *string) *FuelTransactionCreate {
	if s != nil {
		ftc.SetStaffCardID(*s)
	}
	return ftc
}

// SetID sets the "id" field.
func (ftc *FuelTransactionCreate) SetID(s string) *FuelTransactionCreate {
	ftc.mutation.SetID(s)
	return ftc
}

// SetOrderLinkID sets the "order_link" edge to the FuelTransactionOrderLink entity by ID.
func (ftc *FuelTransactionCreate) SetOrderLinkID(id string) *FuelTransactionCreate {
	ftc.mutation.SetOrderLinkID(id)
	return ftc
}

// SetNillableOrderLinkID sets the "order_link" edge to the FuelTransactionOrderLink entity by ID if the given value is not nil.
func (ftc *FuelTransactionCreate) SetNillableOrderLinkID(id *string) *FuelTransactionCreate {
	if id != nil {
		ftc = ftc.SetOrderLinkID(*id)
	}
	return ftc
}

// SetOrderLink sets the "order_link" edge to the FuelTransactionOrderLink entity.
func (ftc *FuelTransactionCreate) SetOrderLink(f *FuelTransactionOrderLink) *FuelTransactionCreate {
	return ftc.SetOrderLinkID(f.ID)
}

// Mutation returns the FuelTransactionMutation object of the builder.
func (ftc *FuelTransactionCreate) Mutation() *FuelTransactionMutation {
	return ftc.mutation
}

// Save creates the FuelTransaction in the database.
func (ftc *FuelTransactionCreate) Save(ctx context.Context) (*FuelTransaction, error) {
	ftc.defaults()
	return withHooks(ctx, ftc.sqlSave, ftc.mutation, ftc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ftc *FuelTransactionCreate) SaveX(ctx context.Context) *FuelTransaction {
	v, err := ftc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ftc *FuelTransactionCreate) Exec(ctx context.Context) error {
	_, err := ftc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ftc *FuelTransactionCreate) ExecX(ctx context.Context) {
	if err := ftc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ftc *FuelTransactionCreate) defaults() {
	if _, ok := ftc.mutation.TotalVolume(); !ok {
		v := fueltransaction.DefaultTotalVolume
		ftc.mutation.SetTotalVolume(v)
	}
	if _, ok := ftc.mutation.TotalAmount(); !ok {
		v := fueltransaction.DefaultTotalAmount
		ftc.mutation.SetTotalAmount(v)
	}
	if _, ok := ftc.mutation.Status(); !ok {
		v := fueltransaction.DefaultStatus
		ftc.mutation.SetStatus(v)
	}
	if _, ok := ftc.mutation.CreatedAt(); !ok {
		v := fueltransaction.DefaultCreatedAt()
		ftc.mutation.SetCreatedAt(v)
	}
	if _, ok := ftc.mutation.UpdatedAt(); !ok {
		v := fueltransaction.DefaultUpdatedAt()
		ftc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ftc *FuelTransactionCreate) check() error {
	if _, ok := ftc.mutation.TransactionNumber(); !ok {
		return &ValidationError{Name: "transaction_number", err: errors.New(`order_service: missing required field "FuelTransaction.transaction_number"`)}
	}
	if v, ok := ftc.mutation.TransactionNumber(); ok {
		if err := fueltransaction.TransactionNumberValidator(v); err != nil {
			return &ValidationError{Name: "transaction_number", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.transaction_number": %w`, err)}
		}
	}
	if _, ok := ftc.mutation.StationID(); !ok {
		return &ValidationError{Name: "station_id", err: errors.New(`order_service: missing required field "FuelTransaction.station_id"`)}
	}
	if _, ok := ftc.mutation.PumpID(); !ok {
		return &ValidationError{Name: "pump_id", err: errors.New(`order_service: missing required field "FuelTransaction.pump_id"`)}
	}
	if v, ok := ftc.mutation.PumpID(); ok {
		if err := fueltransaction.PumpIDValidator(v); err != nil {
			return &ValidationError{Name: "pump_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.pump_id": %w`, err)}
		}
	}
	if _, ok := ftc.mutation.NozzleID(); !ok {
		return &ValidationError{Name: "nozzle_id", err: errors.New(`order_service: missing required field "FuelTransaction.nozzle_id"`)}
	}
	if v, ok := ftc.mutation.NozzleID(); ok {
		if err := fueltransaction.NozzleIDValidator(v); err != nil {
			return &ValidationError{Name: "nozzle_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.nozzle_id": %w`, err)}
		}
	}
	if _, ok := ftc.mutation.FuelType(); !ok {
		return &ValidationError{Name: "fuel_type", err: errors.New(`order_service: missing required field "FuelTransaction.fuel_type"`)}
	}
	if v, ok := ftc.mutation.FuelType(); ok {
		if err := fueltransaction.FuelTypeValidator(v); err != nil {
			return &ValidationError{Name: "fuel_type", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.fuel_type": %w`, err)}
		}
	}
	if _, ok := ftc.mutation.FuelGrade(); !ok {
		return &ValidationError{Name: "fuel_grade", err: errors.New(`order_service: missing required field "FuelTransaction.fuel_grade"`)}
	}
	if v, ok := ftc.mutation.FuelGrade(); ok {
		if err := fueltransaction.FuelGradeValidator(v); err != nil {
			return &ValidationError{Name: "fuel_grade", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.fuel_grade": %w`, err)}
		}
	}
	if _, ok := ftc.mutation.Tank(); !ok {
		return &ValidationError{Name: "tank", err: errors.New(`order_service: missing required field "FuelTransaction.tank"`)}
	}
	if _, ok := ftc.mutation.UnitPrice(); !ok {
		return &ValidationError{Name: "unit_price", err: errors.New(`order_service: missing required field "FuelTransaction.unit_price"`)}
	}
	if _, ok := ftc.mutation.Volume(); !ok {
		return &ValidationError{Name: "volume", err: errors.New(`order_service: missing required field "FuelTransaction.volume"`)}
	}
	if _, ok := ftc.mutation.Amount(); !ok {
		return &ValidationError{Name: "amount", err: errors.New(`order_service: missing required field "FuelTransaction.amount"`)}
	}
	if _, ok := ftc.mutation.TotalVolume(); !ok {
		return &ValidationError{Name: "total_volume", err: errors.New(`order_service: missing required field "FuelTransaction.total_volume"`)}
	}
	if _, ok := ftc.mutation.TotalAmount(); !ok {
		return &ValidationError{Name: "total_amount", err: errors.New(`order_service: missing required field "FuelTransaction.total_amount"`)}
	}
	if _, ok := ftc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`order_service: missing required field "FuelTransaction.status"`)}
	}
	if v, ok := ftc.mutation.Status(); ok {
		if err := fueltransaction.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.status": %w`, err)}
		}
	}
	if v, ok := ftc.mutation.MemberCardID(); ok {
		if err := fueltransaction.MemberCardIDValidator(v); err != nil {
			return &ValidationError{Name: "member_card_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.member_card_id": %w`, err)}
		}
	}
	if v, ok := ftc.mutation.MemberID(); ok {
		if err := fueltransaction.MemberIDValidator(v); err != nil {
			return &ValidationError{Name: "member_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.member_id": %w`, err)}
		}
	}
	if v, ok := ftc.mutation.EmployeeID(); ok {
		if err := fueltransaction.EmployeeIDValidator(v); err != nil {
			return &ValidationError{Name: "employee_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.employee_id": %w`, err)}
		}
	}
	if v, ok := ftc.mutation.FccTransactionID(); ok {
		if err := fueltransaction.FccTransactionIDValidator(v); err != nil {
			return &ValidationError{Name: "fcc_transaction_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.fcc_transaction_id": %w`, err)}
		}
	}
	if v, ok := ftc.mutation.PosTerminalID(); ok {
		if err := fueltransaction.PosTerminalIDValidator(v); err != nil {
			return &ValidationError{Name: "pos_terminal_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.pos_terminal_id": %w`, err)}
		}
	}
	if _, ok := ftc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`order_service: missing required field "FuelTransaction.created_at"`)}
	}
	if _, ok := ftc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`order_service: missing required field "FuelTransaction.updated_at"`)}
	}
	if v, ok := ftc.mutation.StaffCardID(); ok {
		if err := fueltransaction.StaffCardIDValidator(v); err != nil {
			return &ValidationError{Name: "staff_card_id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.staff_card_id": %w`, err)}
		}
	}
	if v, ok := ftc.mutation.ID(); ok {
		if err := fueltransaction.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`order_service: validator failed for field "FuelTransaction.id": %w`, err)}
		}
	}
	return nil
}

func (ftc *FuelTransactionCreate) sqlSave(ctx context.Context) (*FuelTransaction, error) {
	if err := ftc.check(); err != nil {
		return nil, err
	}
	_node, _spec := ftc.createSpec()
	if err := sqlgraph.CreateNode(ctx, ftc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected FuelTransaction.ID type: %T", _spec.ID.Value)
		}
	}
	ftc.mutation.id = &_node.ID
	ftc.mutation.done = true
	return _node, nil
}

func (ftc *FuelTransactionCreate) createSpec() (*FuelTransaction, *sqlgraph.CreateSpec) {
	var (
		_node = &FuelTransaction{config: ftc.config}
		_spec = sqlgraph.NewCreateSpec(fueltransaction.Table, sqlgraph.NewFieldSpec(fueltransaction.FieldID, field.TypeString))
	)
	if id, ok := ftc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := ftc.mutation.TransactionNumber(); ok {
		_spec.SetField(fueltransaction.FieldTransactionNumber, field.TypeString, value)
		_node.TransactionNumber = value
	}
	if value, ok := ftc.mutation.StationID(); ok {
		_spec.SetField(fueltransaction.FieldStationID, field.TypeInt64, value)
		_node.StationID = value
	}
	if value, ok := ftc.mutation.PumpID(); ok {
		_spec.SetField(fueltransaction.FieldPumpID, field.TypeString, value)
		_node.PumpID = value
	}
	if value, ok := ftc.mutation.NozzleID(); ok {
		_spec.SetField(fueltransaction.FieldNozzleID, field.TypeString, value)
		_node.NozzleID = value
	}
	if value, ok := ftc.mutation.FuelType(); ok {
		_spec.SetField(fueltransaction.FieldFuelType, field.TypeString, value)
		_node.FuelType = value
	}
	if value, ok := ftc.mutation.FuelGrade(); ok {
		_spec.SetField(fueltransaction.FieldFuelGrade, field.TypeString, value)
		_node.FuelGrade = value
	}
	if value, ok := ftc.mutation.Tank(); ok {
		_spec.SetField(fueltransaction.FieldTank, field.TypeInt, value)
		_node.Tank = value
	}
	if value, ok := ftc.mutation.UnitPrice(); ok {
		_spec.SetField(fueltransaction.FieldUnitPrice, field.TypeFloat64, value)
		_node.UnitPrice = value
	}
	if value, ok := ftc.mutation.Volume(); ok {
		_spec.SetField(fueltransaction.FieldVolume, field.TypeFloat64, value)
		_node.Volume = value
	}
	if value, ok := ftc.mutation.Amount(); ok {
		_spec.SetField(fueltransaction.FieldAmount, field.TypeFloat64, value)
		_node.Amount = value
	}
	if value, ok := ftc.mutation.TotalVolume(); ok {
		_spec.SetField(fueltransaction.FieldTotalVolume, field.TypeFloat64, value)
		_node.TotalVolume = value
	}
	if value, ok := ftc.mutation.TotalAmount(); ok {
		_spec.SetField(fueltransaction.FieldTotalAmount, field.TypeFloat64, value)
		_node.TotalAmount = value
	}
	if value, ok := ftc.mutation.Status(); ok {
		_spec.SetField(fueltransaction.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := ftc.mutation.MemberCardID(); ok {
		_spec.SetField(fueltransaction.FieldMemberCardID, field.TypeString, value)
		_node.MemberCardID = &value
	}
	if value, ok := ftc.mutation.MemberID(); ok {
		_spec.SetField(fueltransaction.FieldMemberID, field.TypeString, value)
		_node.MemberID = &value
	}
	if value, ok := ftc.mutation.EmployeeID(); ok {
		_spec.SetField(fueltransaction.FieldEmployeeID, field.TypeString, value)
		_node.EmployeeID = &value
	}
	if value, ok := ftc.mutation.FccTransactionID(); ok {
		_spec.SetField(fueltransaction.FieldFccTransactionID, field.TypeString, value)
		_node.FccTransactionID = &value
	}
	if value, ok := ftc.mutation.PosTerminalID(); ok {
		_spec.SetField(fueltransaction.FieldPosTerminalID, field.TypeString, value)
		_node.PosTerminalID = &value
	}
	if value, ok := ftc.mutation.Metadata(); ok {
		_spec.SetField(fueltransaction.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	if value, ok := ftc.mutation.CreatedAt(); ok {
		_spec.SetField(fueltransaction.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ftc.mutation.UpdatedAt(); ok {
		_spec.SetField(fueltransaction.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ftc.mutation.ProcessedAt(); ok {
		_spec.SetField(fueltransaction.FieldProcessedAt, field.TypeTime, value)
		_node.ProcessedAt = &value
	}
	if value, ok := ftc.mutation.CancelledAt(); ok {
		_spec.SetField(fueltransaction.FieldCancelledAt, field.TypeTime, value)
		_node.CancelledAt = &value
	}
	if value, ok := ftc.mutation.StartTotalizer(); ok {
		_spec.SetField(fueltransaction.FieldStartTotalizer, field.TypeFloat64, value)
		_node.StartTotalizer = &value
	}
	if value, ok := ftc.mutation.EndTotalizer(); ok {
		_spec.SetField(fueltransaction.FieldEndTotalizer, field.TypeFloat64, value)
		_node.EndTotalizer = &value
	}
	if value, ok := ftc.mutation.NozzleStartTime(); ok {
		_spec.SetField(fueltransaction.FieldNozzleStartTime, field.TypeTime, value)
		_node.NozzleStartTime = &value
	}
	if value, ok := ftc.mutation.NozzleEndTime(); ok {
		_spec.SetField(fueltransaction.FieldNozzleEndTime, field.TypeTime, value)
		_node.NozzleEndTime = &value
	}
	if value, ok := ftc.mutation.StaffCardID(); ok {
		_spec.SetField(fueltransaction.FieldStaffCardID, field.TypeString, value)
		_node.StaffCardID = &value
	}
	if nodes := ftc.mutation.OrderLinkIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   fueltransaction.OrderLinkTable,
			Columns: []string{fueltransaction.OrderLinkColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// FuelTransactionCreateBulk is the builder for creating many FuelTransaction entities in bulk.
type FuelTransactionCreateBulk struct {
	config
	err      error
	builders []*FuelTransactionCreate
}

// Save creates the FuelTransaction entities in the database.
func (ftcb *FuelTransactionCreateBulk) Save(ctx context.Context) ([]*FuelTransaction, error) {
	if ftcb.err != nil {
		return nil, ftcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ftcb.builders))
	nodes := make([]*FuelTransaction, len(ftcb.builders))
	mutators := make([]Mutator, len(ftcb.builders))
	for i := range ftcb.builders {
		func(i int, root context.Context) {
			builder := ftcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*FuelTransactionMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ftcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ftcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ftcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ftcb *FuelTransactionCreateBulk) SaveX(ctx context.Context) []*FuelTransaction {
	v, err := ftcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ftcb *FuelTransactionCreateBulk) Exec(ctx context.Context) error {
	_, err := ftcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ftcb *FuelTransactionCreateBulk) ExecX(ctx context.Context) {
	if err := ftcb.Exec(ctx); err != nil {
		panic(err)
	}
}
