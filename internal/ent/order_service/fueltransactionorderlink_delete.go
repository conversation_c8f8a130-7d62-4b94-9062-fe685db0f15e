// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

// FuelTransactionOrderLinkDelete is the builder for deleting a FuelTransactionOrderLink entity.
type FuelTransactionOrderLinkDelete struct {
	config
	hooks    []Hook
	mutation *FuelTransactionOrderLinkMutation
}

// Where appends a list predicates to the FuelTransactionOrderLinkDelete builder.
func (ftold *FuelTransactionOrderLinkDelete) Where(ps ...predicate.FuelTransactionOrderLink) *FuelTransactionOrderLinkDelete {
	ftold.mutation.Where(ps...)
	return ftold
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ftold *FuelTransactionOrderLinkDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ftold.sqlExec, ftold.mutation, ftold.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ftold *FuelTransactionOrderLinkDelete) ExecX(ctx context.Context) int {
	n, err := ftold.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ftold *FuelTransactionOrderLinkDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(fueltransactionorderlink.Table, sqlgraph.NewFieldSpec(fueltransactionorderlink.FieldID, field.TypeString))
	if ps := ftold.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ftold.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ftold.mutation.done = true
	return affected, err
}

// FuelTransactionOrderLinkDeleteOne is the builder for deleting a single FuelTransactionOrderLink entity.
type FuelTransactionOrderLinkDeleteOne struct {
	ftold *FuelTransactionOrderLinkDelete
}

// Where appends a list predicates to the FuelTransactionOrderLinkDelete builder.
func (ftoldo *FuelTransactionOrderLinkDeleteOne) Where(ps ...predicate.FuelTransactionOrderLink) *FuelTransactionOrderLinkDeleteOne {
	ftoldo.ftold.mutation.Where(ps...)
	return ftoldo
}

// Exec executes the deletion query.
func (ftoldo *FuelTransactionOrderLinkDeleteOne) Exec(ctx context.Context) error {
	n, err := ftoldo.ftold.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{fueltransactionorderlink.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ftoldo *FuelTransactionOrderLinkDeleteOne) ExecX(ctx context.Context) {
	if err := ftoldo.Exec(ctx); err != nil {
		panic(err)
	}
}
