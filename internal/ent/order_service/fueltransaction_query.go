// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/predicate"
)

// FuelTransactionQuery is the builder for querying FuelTransaction entities.
type FuelTransactionQuery struct {
	config
	ctx           *QueryContext
	order         []fueltransaction.OrderOption
	inters        []Interceptor
	predicates    []predicate.FuelTransaction
	withOrderLink *FuelTransactionOrderLinkQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the FuelTransactionQuery builder.
func (ftq *FuelTransactionQuery) Where(ps ...predicate.FuelTransaction) *FuelTransactionQuery {
	ftq.predicates = append(ftq.predicates, ps...)
	return ftq
}

// Limit the number of records to be returned by this query.
func (ftq *FuelTransactionQuery) Limit(limit int) *FuelTransactionQuery {
	ftq.ctx.Limit = &limit
	return ftq
}

// Offset to start from.
func (ftq *FuelTransactionQuery) Offset(offset int) *FuelTransactionQuery {
	ftq.ctx.Offset = &offset
	return ftq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ftq *FuelTransactionQuery) Unique(unique bool) *FuelTransactionQuery {
	ftq.ctx.Unique = &unique
	return ftq
}

// Order specifies how the records should be ordered.
func (ftq *FuelTransactionQuery) Order(o ...fueltransaction.OrderOption) *FuelTransactionQuery {
	ftq.order = append(ftq.order, o...)
	return ftq
}

// QueryOrderLink chains the current query on the "order_link" edge.
func (ftq *FuelTransactionQuery) QueryOrderLink() *FuelTransactionOrderLinkQuery {
	query := (&FuelTransactionOrderLinkClient{config: ftq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := ftq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := ftq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(fueltransaction.Table, fueltransaction.FieldID, selector),
			sqlgraph.To(fueltransactionorderlink.Table, fueltransactionorderlink.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, fueltransaction.OrderLinkTable, fueltransaction.OrderLinkColumn),
		)
		fromU = sqlgraph.SetNeighbors(ftq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first FuelTransaction entity from the query.
// Returns a *NotFoundError when no FuelTransaction was found.
func (ftq *FuelTransactionQuery) First(ctx context.Context) (*FuelTransaction, error) {
	nodes, err := ftq.Limit(1).All(setContextOp(ctx, ftq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{fueltransaction.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ftq *FuelTransactionQuery) FirstX(ctx context.Context) *FuelTransaction {
	node, err := ftq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first FuelTransaction ID from the query.
// Returns a *NotFoundError when no FuelTransaction ID was found.
func (ftq *FuelTransactionQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = ftq.Limit(1).IDs(setContextOp(ctx, ftq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{fueltransaction.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ftq *FuelTransactionQuery) FirstIDX(ctx context.Context) string {
	id, err := ftq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single FuelTransaction entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one FuelTransaction entity is found.
// Returns a *NotFoundError when no FuelTransaction entities are found.
func (ftq *FuelTransactionQuery) Only(ctx context.Context) (*FuelTransaction, error) {
	nodes, err := ftq.Limit(2).All(setContextOp(ctx, ftq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{fueltransaction.Label}
	default:
		return nil, &NotSingularError{fueltransaction.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ftq *FuelTransactionQuery) OnlyX(ctx context.Context) *FuelTransaction {
	node, err := ftq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only FuelTransaction ID in the query.
// Returns a *NotSingularError when more than one FuelTransaction ID is found.
// Returns a *NotFoundError when no entities are found.
func (ftq *FuelTransactionQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = ftq.Limit(2).IDs(setContextOp(ctx, ftq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{fueltransaction.Label}
	default:
		err = &NotSingularError{fueltransaction.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ftq *FuelTransactionQuery) OnlyIDX(ctx context.Context) string {
	id, err := ftq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of FuelTransactions.
func (ftq *FuelTransactionQuery) All(ctx context.Context) ([]*FuelTransaction, error) {
	ctx = setContextOp(ctx, ftq.ctx, "All")
	if err := ftq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*FuelTransaction, *FuelTransactionQuery]()
	return withInterceptors[[]*FuelTransaction](ctx, ftq, qr, ftq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ftq *FuelTransactionQuery) AllX(ctx context.Context) []*FuelTransaction {
	nodes, err := ftq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of FuelTransaction IDs.
func (ftq *FuelTransactionQuery) IDs(ctx context.Context) (ids []string, err error) {
	if ftq.ctx.Unique == nil && ftq.path != nil {
		ftq.Unique(true)
	}
	ctx = setContextOp(ctx, ftq.ctx, "IDs")
	if err = ftq.Select(fueltransaction.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ftq *FuelTransactionQuery) IDsX(ctx context.Context) []string {
	ids, err := ftq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ftq *FuelTransactionQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ftq.ctx, "Count")
	if err := ftq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ftq, querierCount[*FuelTransactionQuery](), ftq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ftq *FuelTransactionQuery) CountX(ctx context.Context) int {
	count, err := ftq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ftq *FuelTransactionQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ftq.ctx, "Exist")
	switch _, err := ftq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("order_service: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ftq *FuelTransactionQuery) ExistX(ctx context.Context) bool {
	exist, err := ftq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the FuelTransactionQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ftq *FuelTransactionQuery) Clone() *FuelTransactionQuery {
	if ftq == nil {
		return nil
	}
	return &FuelTransactionQuery{
		config:        ftq.config,
		ctx:           ftq.ctx.Clone(),
		order:         append([]fueltransaction.OrderOption{}, ftq.order...),
		inters:        append([]Interceptor{}, ftq.inters...),
		predicates:    append([]predicate.FuelTransaction{}, ftq.predicates...),
		withOrderLink: ftq.withOrderLink.Clone(),
		// clone intermediate query.
		sql:  ftq.sql.Clone(),
		path: ftq.path,
	}
}

// WithOrderLink tells the query-builder to eager-load the nodes that are connected to
// the "order_link" edge. The optional arguments are used to configure the query builder of the edge.
func (ftq *FuelTransactionQuery) WithOrderLink(opts ...func(*FuelTransactionOrderLinkQuery)) *FuelTransactionQuery {
	query := (&FuelTransactionOrderLinkClient{config: ftq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	ftq.withOrderLink = query
	return ftq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TransactionNumber string `json:"transaction_number,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.FuelTransaction.Query().
//		GroupBy(fueltransaction.FieldTransactionNumber).
//		Aggregate(order_service.Count()).
//		Scan(ctx, &v)
func (ftq *FuelTransactionQuery) GroupBy(field string, fields ...string) *FuelTransactionGroupBy {
	ftq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &FuelTransactionGroupBy{build: ftq}
	grbuild.flds = &ftq.ctx.Fields
	grbuild.label = fueltransaction.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TransactionNumber string `json:"transaction_number,omitempty"`
//	}
//
//	client.FuelTransaction.Query().
//		Select(fueltransaction.FieldTransactionNumber).
//		Scan(ctx, &v)
func (ftq *FuelTransactionQuery) Select(fields ...string) *FuelTransactionSelect {
	ftq.ctx.Fields = append(ftq.ctx.Fields, fields...)
	sbuild := &FuelTransactionSelect{FuelTransactionQuery: ftq}
	sbuild.label = fueltransaction.Label
	sbuild.flds, sbuild.scan = &ftq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a FuelTransactionSelect configured with the given aggregations.
func (ftq *FuelTransactionQuery) Aggregate(fns ...AggregateFunc) *FuelTransactionSelect {
	return ftq.Select().Aggregate(fns...)
}

func (ftq *FuelTransactionQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ftq.inters {
		if inter == nil {
			return fmt.Errorf("order_service: uninitialized interceptor (forgotten import order_service/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ftq); err != nil {
				return err
			}
		}
	}
	for _, f := range ftq.ctx.Fields {
		if !fueltransaction.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("order_service: invalid field %q for query", f)}
		}
	}
	if ftq.path != nil {
		prev, err := ftq.path(ctx)
		if err != nil {
			return err
		}
		ftq.sql = prev
	}
	return nil
}

func (ftq *FuelTransactionQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*FuelTransaction, error) {
	var (
		nodes       = []*FuelTransaction{}
		_spec       = ftq.querySpec()
		loadedTypes = [1]bool{
			ftq.withOrderLink != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*FuelTransaction).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &FuelTransaction{config: ftq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ftq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := ftq.withOrderLink; query != nil {
		if err := ftq.loadOrderLink(ctx, query, nodes, nil,
			func(n *FuelTransaction, e *FuelTransactionOrderLink) { n.Edges.OrderLink = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (ftq *FuelTransactionQuery) loadOrderLink(ctx context.Context, query *FuelTransactionOrderLinkQuery, nodes []*FuelTransaction, init func(*FuelTransaction), assign func(*FuelTransaction, *FuelTransactionOrderLink)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[string]*FuelTransaction)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(fueltransactionorderlink.FieldFuelTransactionID)
	}
	query.Where(predicate.FuelTransactionOrderLink(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(fueltransaction.OrderLinkColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.FuelTransactionID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "fuel_transaction_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (ftq *FuelTransactionQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ftq.querySpec()
	_spec.Node.Columns = ftq.ctx.Fields
	if len(ftq.ctx.Fields) > 0 {
		_spec.Unique = ftq.ctx.Unique != nil && *ftq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ftq.driver, _spec)
}

func (ftq *FuelTransactionQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(fueltransaction.Table, fueltransaction.Columns, sqlgraph.NewFieldSpec(fueltransaction.FieldID, field.TypeString))
	_spec.From = ftq.sql
	if unique := ftq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ftq.path != nil {
		_spec.Unique = true
	}
	if fields := ftq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, fueltransaction.FieldID)
		for i := range fields {
			if fields[i] != fueltransaction.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := ftq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ftq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ftq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ftq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ftq *FuelTransactionQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ftq.driver.Dialect())
	t1 := builder.Table(fueltransaction.Table)
	columns := ftq.ctx.Fields
	if len(columns) == 0 {
		columns = fueltransaction.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ftq.sql != nil {
		selector = ftq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ftq.ctx.Unique != nil && *ftq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ftq.predicates {
		p(selector)
	}
	for _, p := range ftq.order {
		p(selector)
	}
	if offset := ftq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ftq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// FuelTransactionGroupBy is the group-by builder for FuelTransaction entities.
type FuelTransactionGroupBy struct {
	selector
	build *FuelTransactionQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ftgb *FuelTransactionGroupBy) Aggregate(fns ...AggregateFunc) *FuelTransactionGroupBy {
	ftgb.fns = append(ftgb.fns, fns...)
	return ftgb
}

// Scan applies the selector query and scans the result into the given value.
func (ftgb *FuelTransactionGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ftgb.build.ctx, "GroupBy")
	if err := ftgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*FuelTransactionQuery, *FuelTransactionGroupBy](ctx, ftgb.build, ftgb, ftgb.build.inters, v)
}

func (ftgb *FuelTransactionGroupBy) sqlScan(ctx context.Context, root *FuelTransactionQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ftgb.fns))
	for _, fn := range ftgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ftgb.flds)+len(ftgb.fns))
		for _, f := range *ftgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ftgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ftgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// FuelTransactionSelect is the builder for selecting fields of FuelTransaction entities.
type FuelTransactionSelect struct {
	*FuelTransactionQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (fts *FuelTransactionSelect) Aggregate(fns ...AggregateFunc) *FuelTransactionSelect {
	fts.fns = append(fts.fns, fns...)
	return fts
}

// Scan applies the selector query and scans the result into the given value.
func (fts *FuelTransactionSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, fts.ctx, "Select")
	if err := fts.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*FuelTransactionQuery, *FuelTransactionSelect](ctx, fts.FuelTransactionQuery, fts, fts.inters, v)
}

func (fts *FuelTransactionSelect) sqlScan(ctx context.Context, root *FuelTransactionQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(fts.fns))
	for _, fn := range fts.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*fts.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := fts.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
