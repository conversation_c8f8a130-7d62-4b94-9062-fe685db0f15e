// Code generated by ent, DO NOT EDIT.

package order_service

import (
	"time"

	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransaction"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/fueltransactionorderlink"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/order"
	"gitlab4.weicheche.cn/indo-bp/hos-reporter/internal/ent/order_service/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	fueltransactionFields := schema.FuelTransaction{}.Fields()
	_ = fueltransactionFields
	// fueltransactionDescTransactionNumber is the schema descriptor for transaction_number field.
	fueltransactionDescTransactionNumber := fueltransactionFields[1].Descriptor()
	// fueltransaction.TransactionNumberValidator is a validator for the "transaction_number" field. It is called by the builders before save.
	fueltransaction.TransactionNumberValidator = func() func(string) error {
		validators := fueltransactionDescTransactionNumber.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(transaction_number string) error {
			for _, fn := range fns {
				if err := fn(transaction_number); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// fueltransactionDescPumpID is the schema descriptor for pump_id field.
	fueltransactionDescPumpID := fueltransactionFields[3].Descriptor()
	// fueltransaction.PumpIDValidator is a validator for the "pump_id" field. It is called by the builders before save.
	fueltransaction.PumpIDValidator = func() func(string) error {
		validators := fueltransactionDescPumpID.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(pump_id string) error {
			for _, fn := range fns {
				if err := fn(pump_id); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// fueltransactionDescNozzleID is the schema descriptor for nozzle_id field.
	fueltransactionDescNozzleID := fueltransactionFields[4].Descriptor()
	// fueltransaction.NozzleIDValidator is a validator for the "nozzle_id" field. It is called by the builders before save.
	fueltransaction.NozzleIDValidator = func() func(string) error {
		validators := fueltransactionDescNozzleID.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(nozzle_id string) error {
			for _, fn := range fns {
				if err := fn(nozzle_id); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// fueltransactionDescFuelType is the schema descriptor for fuel_type field.
	fueltransactionDescFuelType := fueltransactionFields[5].Descriptor()
	// fueltransaction.FuelTypeValidator is a validator for the "fuel_type" field. It is called by the builders before save.
	fueltransaction.FuelTypeValidator = func() func(string) error {
		validators := fueltransactionDescFuelType.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(fuel_type string) error {
			for _, fn := range fns {
				if err := fn(fuel_type); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// fueltransactionDescFuelGrade is the schema descriptor for fuel_grade field.
	fueltransactionDescFuelGrade := fueltransactionFields[6].Descriptor()
	// fueltransaction.FuelGradeValidator is a validator for the "fuel_grade" field. It is called by the builders before save.
	fueltransaction.FuelGradeValidator = func() func(string) error {
		validators := fueltransactionDescFuelGrade.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(fuel_grade string) error {
			for _, fn := range fns {
				if err := fn(fuel_grade); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// fueltransactionDescTotalVolume is the schema descriptor for total_volume field.
	fueltransactionDescTotalVolume := fueltransactionFields[11].Descriptor()
	// fueltransaction.DefaultTotalVolume holds the default value on creation for the total_volume field.
	fueltransaction.DefaultTotalVolume = fueltransactionDescTotalVolume.Default.(float64)
	// fueltransactionDescTotalAmount is the schema descriptor for total_amount field.
	fueltransactionDescTotalAmount := fueltransactionFields[12].Descriptor()
	// fueltransaction.DefaultTotalAmount holds the default value on creation for the total_amount field.
	fueltransaction.DefaultTotalAmount = fueltransactionDescTotalAmount.Default.(float64)
	// fueltransactionDescStatus is the schema descriptor for status field.
	fueltransactionDescStatus := fueltransactionFields[13].Descriptor()
	// fueltransaction.DefaultStatus holds the default value on creation for the status field.
	fueltransaction.DefaultStatus = fueltransactionDescStatus.Default.(string)
	// fueltransaction.StatusValidator is a validator for the "status" field. It is called by the builders before save.
	fueltransaction.StatusValidator = fueltransactionDescStatus.Validators[0].(func(string) error)
	// fueltransactionDescMemberCardID is the schema descriptor for member_card_id field.
	fueltransactionDescMemberCardID := fueltransactionFields[14].Descriptor()
	// fueltransaction.MemberCardIDValidator is a validator for the "member_card_id" field. It is called by the builders before save.
	fueltransaction.MemberCardIDValidator = fueltransactionDescMemberCardID.Validators[0].(func(string) error)
	// fueltransactionDescMemberID is the schema descriptor for member_id field.
	fueltransactionDescMemberID := fueltransactionFields[15].Descriptor()
	// fueltransaction.MemberIDValidator is a validator for the "member_id" field. It is called by the builders before save.
	fueltransaction.MemberIDValidator = fueltransactionDescMemberID.Validators[0].(func(string) error)
	// fueltransactionDescEmployeeID is the schema descriptor for employee_id field.
	fueltransactionDescEmployeeID := fueltransactionFields[16].Descriptor()
	// fueltransaction.EmployeeIDValidator is a validator for the "employee_id" field. It is called by the builders before save.
	fueltransaction.EmployeeIDValidator = fueltransactionDescEmployeeID.Validators[0].(func(string) error)
	// fueltransactionDescFccTransactionID is the schema descriptor for fcc_transaction_id field.
	fueltransactionDescFccTransactionID := fueltransactionFields[17].Descriptor()
	// fueltransaction.FccTransactionIDValidator is a validator for the "fcc_transaction_id" field. It is called by the builders before save.
	fueltransaction.FccTransactionIDValidator = fueltransactionDescFccTransactionID.Validators[0].(func(string) error)
	// fueltransactionDescPosTerminalID is the schema descriptor for pos_terminal_id field.
	fueltransactionDescPosTerminalID := fueltransactionFields[18].Descriptor()
	// fueltransaction.PosTerminalIDValidator is a validator for the "pos_terminal_id" field. It is called by the builders before save.
	fueltransaction.PosTerminalIDValidator = fueltransactionDescPosTerminalID.Validators[0].(func(string) error)
	// fueltransactionDescCreatedAt is the schema descriptor for created_at field.
	fueltransactionDescCreatedAt := fueltransactionFields[20].Descriptor()
	// fueltransaction.DefaultCreatedAt holds the default value on creation for the created_at field.
	fueltransaction.DefaultCreatedAt = fueltransactionDescCreatedAt.Default.(func() time.Time)
	// fueltransactionDescUpdatedAt is the schema descriptor for updated_at field.
	fueltransactionDescUpdatedAt := fueltransactionFields[21].Descriptor()
	// fueltransaction.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	fueltransaction.DefaultUpdatedAt = fueltransactionDescUpdatedAt.Default.(func() time.Time)
	// fueltransaction.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	fueltransaction.UpdateDefaultUpdatedAt = fueltransactionDescUpdatedAt.UpdateDefault.(func() time.Time)
	// fueltransactionDescStaffCardID is the schema descriptor for staff_card_id field.
	fueltransactionDescStaffCardID := fueltransactionFields[28].Descriptor()
	// fueltransaction.StaffCardIDValidator is a validator for the "staff_card_id" field. It is called by the builders before save.
	fueltransaction.StaffCardIDValidator = fueltransactionDescStaffCardID.Validators[0].(func(string) error)
	// fueltransactionDescID is the schema descriptor for id field.
	fueltransactionDescID := fueltransactionFields[0].Descriptor()
	// fueltransaction.IDValidator is a validator for the "id" field. It is called by the builders before save.
	fueltransaction.IDValidator = fueltransactionDescID.Validators[0].(func(string) error)
	fueltransactionorderlinkFields := schema.FuelTransactionOrderLink{}.Fields()
	_ = fueltransactionorderlinkFields
	// fueltransactionorderlinkDescStatus is the schema descriptor for status field.
	fueltransactionorderlinkDescStatus := fueltransactionorderlinkFields[4].Descriptor()
	// fueltransactionorderlink.DefaultStatus holds the default value on creation for the status field.
	fueltransactionorderlink.DefaultStatus = fueltransactionorderlinkDescStatus.Default.(string)
	// fueltransactionorderlink.StatusValidator is a validator for the "status" field. It is called by the builders before save.
	fueltransactionorderlink.StatusValidator = fueltransactionorderlinkDescStatus.Validators[0].(func(string) error)
	// fueltransactionorderlinkDescCreatedAt is the schema descriptor for created_at field.
	fueltransactionorderlinkDescCreatedAt := fueltransactionorderlinkFields[6].Descriptor()
	// fueltransactionorderlink.DefaultCreatedAt holds the default value on creation for the created_at field.
	fueltransactionorderlink.DefaultCreatedAt = fueltransactionorderlinkDescCreatedAt.Default.(func() time.Time)
	// fueltransactionorderlinkDescUpdatedAt is the schema descriptor for updated_at field.
	fueltransactionorderlinkDescUpdatedAt := fueltransactionorderlinkFields[7].Descriptor()
	// fueltransactionorderlink.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	fueltransactionorderlink.DefaultUpdatedAt = fueltransactionorderlinkDescUpdatedAt.Default.(func() time.Time)
	// fueltransactionorderlink.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	fueltransactionorderlink.UpdateDefaultUpdatedAt = fueltransactionorderlinkDescUpdatedAt.UpdateDefault.(func() time.Time)
	// fueltransactionorderlinkDescID is the schema descriptor for id field.
	fueltransactionorderlinkDescID := fueltransactionorderlinkFields[0].Descriptor()
	// fueltransactionorderlink.IDValidator is a validator for the "id" field. It is called by the builders before save.
	fueltransactionorderlink.IDValidator = fueltransactionorderlinkDescID.Validators[0].(func(string) error)
	orderFields := schema.Order{}.Fields()
	_ = orderFields
	// orderDescOrderNumber is the schema descriptor for order_number field.
	orderDescOrderNumber := orderFields[1].Descriptor()
	// order.OrderNumberValidator is a validator for the "order_number" field. It is called by the builders before save.
	order.OrderNumberValidator = func() func(string) error {
		validators := orderDescOrderNumber.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(order_number string) error {
			for _, fn := range fns {
				if err := fn(order_number); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// orderDescCustomerPhone is the schema descriptor for customer_phone field.
	orderDescCustomerPhone := orderFields[3].Descriptor()
	// order.CustomerPhoneValidator is a validator for the "customer_phone" field. It is called by the builders before save.
	order.CustomerPhoneValidator = orderDescCustomerPhone.Validators[0].(func(string) error)
	// orderDescLicensePlate is the schema descriptor for license_plate field.
	orderDescLicensePlate := orderFields[4].Descriptor()
	// order.LicensePlateValidator is a validator for the "license_plate" field. It is called by the builders before save.
	order.LicensePlateValidator = orderDescLicensePlate.Validators[0].(func(string) error)
	// orderDescCustomerName is the schema descriptor for customer_name field.
	orderDescCustomerName := orderFields[5].Descriptor()
	// order.CustomerNameValidator is a validator for the "customer_name" field. It is called by the builders before save.
	order.CustomerNameValidator = orderDescCustomerName.Validators[0].(func(string) error)
	// orderDescStatus is the schema descriptor for status field.
	orderDescStatus := orderFields[7].Descriptor()
	// order.DefaultStatus holds the default value on creation for the status field.
	order.DefaultStatus = orderDescStatus.Default.(string)
	// order.StatusValidator is a validator for the "status" field. It is called by the builders before save.
	order.StatusValidator = orderDescStatus.Validators[0].(func(string) error)
	// orderDescTotalAmount is the schema descriptor for total_amount field.
	orderDescTotalAmount := orderFields[8].Descriptor()
	// order.DefaultTotalAmount holds the default value on creation for the total_amount field.
	order.DefaultTotalAmount = orderDescTotalAmount.Default.(float64)
	// orderDescDiscountAmount is the schema descriptor for discount_amount field.
	orderDescDiscountAmount := orderFields[9].Descriptor()
	// order.DefaultDiscountAmount holds the default value on creation for the discount_amount field.
	order.DefaultDiscountAmount = orderDescDiscountAmount.Default.(float64)
	// orderDescFinalAmount is the schema descriptor for final_amount field.
	orderDescFinalAmount := orderFields[10].Descriptor()
	// order.DefaultFinalAmount holds the default value on creation for the final_amount field.
	order.DefaultFinalAmount = orderDescFinalAmount.Default.(float64)
	// orderDescTaxAmount is the schema descriptor for tax_amount field.
	orderDescTaxAmount := orderFields[11].Descriptor()
	// order.DefaultTaxAmount holds the default value on creation for the tax_amount field.
	order.DefaultTaxAmount = orderDescTaxAmount.Default.(float64)
	// orderDescPaidAmount is the schema descriptor for paid_amount field.
	orderDescPaidAmount := orderFields[12].Descriptor()
	// order.DefaultPaidAmount holds the default value on creation for the paid_amount field.
	order.DefaultPaidAmount = orderDescPaidAmount.Default.(float64)
	// orderDescCreatedAt is the schema descriptor for created_at field.
	orderDescCreatedAt := orderFields[14].Descriptor()
	// order.DefaultCreatedAt holds the default value on creation for the created_at field.
	order.DefaultCreatedAt = orderDescCreatedAt.Default.(func() time.Time)
	// orderDescUpdatedAt is the schema descriptor for updated_at field.
	orderDescUpdatedAt := orderFields[15].Descriptor()
	// order.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	order.DefaultUpdatedAt = orderDescUpdatedAt.Default.(func() time.Time)
	// order.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	order.UpdateDefaultUpdatedAt = orderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// orderDescEmployeeNo is the schema descriptor for employee_no field.
	orderDescEmployeeNo := orderFields[18].Descriptor()
	// order.EmployeeNoValidator is a validator for the "employee_no" field. It is called by the builders before save.
	order.EmployeeNoValidator = orderDescEmployeeNo.Validators[0].(func(string) error)
	// orderDescID is the schema descriptor for id field.
	orderDescID := orderFields[0].Descriptor()
	// order.IDValidator is a validator for the "id" field. It is called by the builders before save.
	order.IDValidator = orderDescID.Validators[0].(func(string) error)
}
