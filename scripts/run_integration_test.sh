#!/bin/bash

# HOS Reporter 集成测试运行脚本
# 
# 功能：可指定URL、order ID或具体内容，发送同步请求到ERP系统
# 特点：跳过Redis缓存，从真实数据库查询订单数据
# 
# 使用方法:
#   ./scripts/run_integration_test.sh                    # 从数据库查询真实订单
#   ./scripts/run_integration_test.sh --order-id 12345   # 指定订单ID
#   ./scripts/run_integration_test.sh --custom-data      # 自定义数据
#   ./scripts/run_integration_test.sh --batch            # 批量测试
#   ./scripts/run_integration_test.sh --url http://...   # 自定义URL

set -e

# 默认参数
DEFAULT_ERP_URL="http://*************/apr/api"
ORDER_ID=""
CUSTOM_DATA=""
BATCH_TEST=""
ERP_BASE_URL=""
HELP=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --order-id)
            ORDER_ID="$2"
            shift 2
            ;;
        --custom-data)
            CUSTOM_DATA="true"
            shift
            ;;
        --batch)
            BATCH_TEST="1"
            shift
            ;;
        --url)
            ERP_BASE_URL="$2"
            shift 2
            ;;
        --help|-h)
            HELP=true
            shift
            ;;
        *)
            echo "未知参数: $1"
            HELP=true
            shift
            ;;
    esac
done

# 显示帮助信息
if [[ "$HELP" == true ]]; then
    echo "HOS Reporter 集成测试运行脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --order-id <ID>     指定订单ID进行测试"
    echo "  --custom-data       使用自定义测试数据（多笔交易）"
    echo "  --batch             运行批量测试（多个连续请求）"
    echo "  --url <URL>         指定自定义ERP API URL"
    echo "  --help, -h          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 默认测试"
    echo "  $0 --order-id 12345                  # 测试订单ID 12345"
    echo "  $0 --custom-data                     # 测试自定义数据"
    echo "  $0 --batch                           # 批量测试"
    echo "  $0 --url http://test.api.com         # 测试自定义URL"
    echo ""
    echo "默认ERP URL: $DEFAULT_ERP_URL"
    exit 0
fi

# 设置环境变量
export INTEGRATION_TEST=1

if [[ -n "$ORDER_ID" ]]; then
    export ORDER_ID="$ORDER_ID"
    echo "🎯 运行集成测试 - 订单ID: $ORDER_ID"
elif [[ "$CUSTOM_DATA" == "true" ]]; then
    export CUSTOM_DATA="true"
    echo "🎨 运行集成测试 - 自定义数据"
elif [[ "$BATCH_TEST" == "1" ]]; then
    export BATCH_TEST="1"
    echo "📦 运行批量集成测试"
else
    echo "🚀 运行默认集成测试"
fi

if [[ -n "$ERP_BASE_URL" ]]; then
    export ERP_BASE_URL="$ERP_BASE_URL"
    echo "🌐 使用自定义ERP URL: $ERP_BASE_URL"
else
    echo "🌐 使用默认ERP URL: $DEFAULT_ERP_URL"
fi

echo "====================================="

# 检查是否在项目根目录
if [[ ! -f "go.mod" ]]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "❌ 错误: 未找到Go，请先安装Go"
    exit 1
fi

# 运行测试
echo "⏳ 开始运行集成测试..."
echo ""

if [[ "$BATCH_TEST" == "1" ]]; then
    # 运行批量测试
    go test -v ./internal/task/reporter/ -run TestSyncOrder_BatchIntegration -timeout 5m
else
    # 运行单个测试
    go test -v ./internal/task/reporter/ -run TestSyncOrder_Integration -timeout 2m
fi

TEST_EXIT_CODE=$?

echo ""
if [[ $TEST_EXIT_CODE -eq 0 ]]; then
    echo "✅ 集成测试完成"
else
    echo "❌ 集成测试失败 (退出码: $TEST_EXIT_CODE)"
    echo ""
    echo "常见问题排查:"
    echo "1. 检查网络连接是否正常"
    echo "2. 确认ERP服务是否可访问"
    echo "3. 验证用户凭证是否正确"
    echo "4. 查看上方详细错误信息"
fi

exit $TEST_EXIT_CODE 