# ERP字段映射配置
field_mappings:
  # Pump ID 到 Dispenser Number 的映射
  pump_dispenser:
    # 简单数字ID
    "1": 1
    "2": 2
    "3": 3
    "4": 4
    # 字母数字组合ID
    "A01": 1
    "A02": 2
    "B01": 3
    "B02": 4
    # 标准PUMP格式
    "PUMP_001": 1
    "PUMP_002": 2
    # 复杂设备ID格式 - 修复device_com7_pump01格式支持
    "device_com7_pump01": 1
    "device_com7_pump02": 2
    "device_com7_pump03": 3
    "device_com7_pump04": 4
    "device_com7_pump05": 5
    "device_com7_pump06": 6
    "device_com7_pump07": 7
    "device_com7_pump08": 8
    # 默认映射规则：如果找不到精确匹配，尝试提取数字
    "_default": "extract_number"

  # Nozzle ID 到 Nozzle Number 的映射
  nozzle_number:
    # 简单数字ID
    "1": 1
    "2": 2
    "3": 3
    "4": 4
    # 字母数字组合ID
    "N01": 1
    "N02": 2
    "N03": 3
    "N04": 4
    # 标准NOZZLE格式
    "NOZZLE_A01": 1
    "NOZZLE_A02": 2
    # 复杂设备ID格式 - 修复device_com7_nozzle01格式支持
    "device_com7_nozzle01": 1
    "device_com7_nozzle02": 2
    "device_com7_nozzle03": 3
    "device_com7_nozzle04": 4
    "device_com7_nozzle05": 5
    "device_com7_nozzle06": 6
    "device_com7_nozzle07": 7
    "device_com7_nozzle08": 8
    "_default": "extract_number"

  # Fuel Type 到 Product ID 的映射
  fuel_product:
    # 文字型燃油类型
    "PERTAMAX": "PERTAMAX"
    "PERTALITE": "PERTALITE"
    "PERTAMAX_TURBO": "PERTAMAX_TURBO"
    "PERTAMINA_DEX": "PERTAMINA_DEX"
    "SOLAR": "DIESEL"
    "DIESEL": "DIESEL"
    # 数字型燃油代码映射 - 修复数字代码支持
    "102": "102"        # BP Ultimate 数字代码
    "103": "103"        # BP 95 数字代码
    "104": "104"        # BP Diesel 数字代码
    "105": "105"        # BP Regular 数字代码
    "92": "PERTALITE"   # RON 92 汽油
    "95": "PERTAMAX"    # RON 95 汽油
    "98": "PERTAMAX_TURBO"  # RON 98 汽油
    "_default": "PERTALITE"

  # Vehicle Type 映射
  vehicle_type:
    "CAR": 1
    "MOTOR": 2
    "MOTORCYCLE": 2
    "TRUCK": 3
    "_default": 1

  # Vehicle Type Group 映射
  vehicle_group:
    1: "Car"
    2: "Motor"
    3: "Truck"
    "_default": "Car"

# 默认值配置
defaults:
  site_id_prefix: "SITE"
  device_id_prefix: "DEV"
  operator_id_prefix: "OP"
  transaction_id_prefix: "TXN"
  slip_number_prefix: "SLIP"
  vehicle_id_prefix: "VEH"
  
  # 默认数值
  dispenser_number: 1
  nozzle_number: 1
  vehicle_type: 1
  reprint: 0
  transaction_length: 5
  default_price: 15000.00
  default_operator: "OP001"
  default_category: "Fuel"
  default_area: "Jakarta"
  default_promotion_type: "None"

# 数据提取规则
extraction_rules:
  extract_number:
    pattern: "\\d+$"  # 修复：提取末尾数字而不是第一个数字
    fallback: 1
  
  extract_last_digits:
    pattern: "\\d+$"  # 提取末尾数字
    fallback: 1 