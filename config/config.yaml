# HOS Reporter 服务配置文件

# 数据库配置
order_service_database:
  driver: "postgres"
  host: "*************"
  port: 5432
  database: "bos_db"
  username: "postgres"
  password: "GGPoaJhs00Zq"
  ssl_mode: "disable"

reporter_database:
  driver: "postgres"
  host: "*************"
  port: 5432
  database: "hos_reporter"
  username: "postgres"
  password: "GGPoaJhs00Zq"
  ssl_mode: "disable"

# Redis 配置
redis:
  address: "*************:6379"
  password: ""
  db: 0
  prefix: "hos-reporter" # Redis 键前缀，用于区分不同服务

# ERP API 配置
erp_api:
  base_url_uat: "http://*************/apr/api"
  base_url_prod: "http://**************/apr/api"
  # 用于交易数据上报的用户凭证
  transaction_user:
    key: "123456789"
    user: "wecar"
    password: "1!8$3#7*4@"
  # 用于发货和库存数据上报的用户凭证
  atg_user:
    key: "123456789"
    user: "wecar"
    password: "1!8$3#7*4@"

# 上报任务配置(0 代表关闭上报)
reporter:
  transaction_interval: 30  # 交易数据上报间隔：600秒（10分钟）
  inventory_interval: 0     # 库存数据上报间隔：1800秒（30分钟）
  data_sync_interval: 30    # 数据同步间隔：30秒 - 将其他表数据同步到report_record表
  batch_size: 1             # 每次上报的数据批次大小