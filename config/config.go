package config

import (
	"log"
	"os"
	"path/filepath"
	"sync"

	"gopkg.in/yaml.v3"
)

// 全局配置实例
var (
	globalConfig *Config
	configOnce   sync.Once
)

// Config 主配置结构体
type Config struct {
	RpDatabase RpDatabaseConfig `yaml:"reporter_database"`
	OdDatabase OdDatabaseConfig `yaml:"order_service_database"`
	Redis      RedisConfig      `yaml:"redis"`
	ERPAPI     ERPAPIConfig     `yaml:"erp_api"`
	Reporter   ReporterConfig   `yaml:"reporter"`
}

// RpDatabaseConfig 数据库配置
type RpDatabaseConfig struct {
	Driver   string `yaml:"driver"` // 数据库驱动: postgres, mysql, sqlite3
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Database string `yaml:"database"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	SSLMode  string `yaml:"ssl_mode"` // PostgreSQL SSL 模式
}

// OdDatabaseConfig 数据库配置
type OdDatabaseConfig struct {
	Driver   string `yaml:"driver"` // 数据库驱动: postgres, mysql, sqlite3
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Database string `yaml:"database"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	SSLMode  string `yaml:"ssl_mode"` // PostgreSQL SSL 模式
}

// RedisConfig Redis 配置
type RedisConfig struct {
	Address  string `yaml:"address"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
	Prefix   string `yaml:"prefix"` // Redis 键前缀，用于区分不同服务
}

// ERPAPIConfig ERP API 配置
type ERPAPIConfig struct {
	BaseURLUAT      string        `yaml:"base_url_uat"`
	BaseURLProd     string        `yaml:"base_url_prod"`
	TransactionUser ERPUserConfig `yaml:"transaction_user"`
	ATGUser         ERPUserConfig `yaml:"atg_user"`
}

// ERPUserConfig ERP 用户凭证配置
type ERPUserConfig struct {
	Key      string `yaml:"key"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
}

// ReporterConfig 上报任务配置
type ReporterConfig struct {
	TransactionInterval int `yaml:"transaction_interval"` // 交易数据上报间隔（秒）
	InventoryInterval   int `yaml:"inventory_interval"`   // 库存数据上报间隔（秒）
	DataSyncInterval    int `yaml:"data_sync_interval"`   // 数据同步间隔（秒）- 将其他表数据同步到report_record表
	BatchSize           int `yaml:"batch_size"`           // 每次上报的数据批次大小
}

// GetConfig 获取全局配置实例（单例模式）
func GetConfig() *Config {
	// 如果配置还未加载，使用 sync.Once 确保只加载一次
	configOnce.Do(func() {
		// 查找项目根目录（包含go.mod文件的目录）
		projectRoot, err := findProjectRoot()
		if err != nil {
			panic("查找项目根目录失败: " + err.Error())
		}

		configPath := filepath.Join(projectRoot, "config", "config.yaml")
		if len(os.Args) > 1 {
			configPath = os.Args[1]
		}
		cfg, err := LoadConfig(configPath)
		if err != nil {
			// 在实际应用中，这里可能需要更优雅的错误处理
			panic("加载配置失败: " + err.Error())
		}
		log.Printf("使用配置文件: %s", configPath)

		globalConfig = cfg
	})

	return globalConfig
}

// LoadConfig 从指定路径加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	var config *Config

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// findProjectRoot 查找项目根目录（包含go.mod文件的目录）
func findProjectRoot() (string, error) {
	dir, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// 向上查找，直到找到go.mod文件或到达文件系统根目录
	for {
		goModPath := filepath.Join(dir, "go.mod")
		if _, err := os.Stat(goModPath); err == nil {
			return dir, nil
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			// 已经到达文件系统根目录
			break
		}
		dir = parent
	}

	return "", os.ErrNotExist
}

func GetBaseURL() string {
	return GetConfig().ERPAPI.BaseURLUAT
}

// ResetConfigForTesting 重置全局配置（仅用于测试）
func ResetConfigForTesting() {
	globalConfig = nil
	configOnce = sync.Once{}
}

// SetConfigForTesting 设置测试用的配置（仅用于测试）
func SetConfigForTesting(config *Config) {
	globalConfig = config
}
