## HOS 数据上报功能 - 构建计划

**总体目标**: 实现一个健壮的后台服务，能够定时将 HOS 系统中的交易数据、发货数据和库存数据上报至外部 ERP 系统。

**测试策略**:
*   `adapter` 层的方法需要进行集成测试，因为它们与外部 API 交互。
*   `repository` 层的方法需要编写单元测试，连接到测试数据库。
*   `usecase` 和 `scheduler` 的逻辑需要通过单元测试（mock 依赖）和集成测试来验证。

---

### **阶段 0: 项目初始化与基础架构搭建 (上报功能特定)**

** [x] 任务 0.1: 新增 `hos-reporter` 服务入口**
*   **开始**: 已有 HOS 项目结构。
*   **任务**:
    1.  在 `cmd/` 目录下创建新目录 `hos-reporter/`。
    2.  在 `cmd/hos-reporter/` 中创建 `main.go` 文件，并写入一个简单的 "Hello, Reporter!" `fmt.Println` 语句。
*   **结束**: 能够通过 `go run ./cmd/hos-reporter/main.go` 成功运行，并看到输出。
*   **关注点**: 验证新服务的入口点已正确创建。

** [x] 任务 0.2: 扩展配置文件 (`config/config.yaml`)**
*   **开始**: 任务 0.1 完成。
*   **任务**:
    1.  打开 `config/config.yaml` 文件。
    2.  添加 `redis`, `erp_api`, 和 `reporter` 的配置节，可以先填入占位符值。
    3.  更新 `config/config.go`，添加对应的 Go 结构体来解析这些新配置。
    4.  在 `cmd/hos-reporter/main.go` 中尝试加载并打印这些新配置，以验证加载逻辑正确。
*   **结束**: 新增的配置项能够被成功加载和解析。
*   **关注点**: 系统配置管理。

** [x] 任务 0.3: 基础设施 - Redis 客户端 (`infra/cache/redis.go`)**
*   **开始**: 任务 0.2 完成。
*   **任务**:
    1.  在 `infra/` 目录下创建新目录 `cache/`。
    2.  创建 `infra/cache/redis.go` 文件。
    3.  引入 `go-redis/redis/v8` 或更高版本库。
    4.  实现 `NewRedisClient(*config.RedisConfig)` 函数，用于初始化并返回一个 `*redis.Client` 实例。
    5.  在 `cmd/hos-reporter/main.go` 中调用此函数并检查是否返回错误，验证连接（需要一个可用的 Redis 服务）。
*   **结束**: Redis 客户端能够基于配置成功初始化。
*   **关注点**: Redis 基础设施连接。

---

### **阶段 1: ERP 适配器 (Adapter) - 封装外部 API 交互**

** [x] 任务 1.1: 定义 ERP API 的数据结构 (`internal/adapter/erp/types.go`)**
*   **开始**: 项目基础架构就绪。
*   **任务**:
    1.  创建 `internal/adapter/erp/types.go` 文件。
    2.  根据 API 文档，定义 `GetTokenRequest`, `GetTokenResponse`, `DispenserTransactionRequest`, `DispenserTransactionResponse` 等所有需要用到的请求和响应的 Go 结构体。
    3.  使用 `json:"..."` 标签来精确匹配 API 的字段名。
*   **结束**: 所有与 ERP API 交互的 Go 数据结构定义完毕。
*   **关注点**: API 数据契约的精确建模。

** [x] 任务 1.2: 实现 Token 管理器 - 获取新 Token (`internal/adapter/erp/token.go`)**
*   **开始**: 任务 0.3, 1.1 完成。
*   **任务**:
    1.  创建 `internal/adapter/erp/token.go` 文件。
    2.  定义 `TokenManager` 结构体，包含 `*http.Client`, `*config.ERPAPIConfig`, `*redis.Client`。
    3.  实现 `FetchNewToken(ctx context.Context, userType string)` 方法。此方法 **不** 检查缓存，而是直接调用 ERP 的 `/ATG/GetToken` 接口获取一个新的 Token。
    4.  编写一个集成测试来验证此方法能否成功从 UAT 环境获取 Token。
*   **结束**: `FetchNewToken` 方法能够成功获取一个新的 Token。
*   **关注点**: 单次 API 认证调用的实现。

** [x] 任务 1.3: 实现 Token 管理器 - 缓存逻辑 (`internal/adapter/erp/token.go`)**
*   **开始**: 任务 1.2 完成。
*   **任务**:
    1.  在 `TokenManager` 中实现核心方法 `GetToken(ctx context.Context, userType string)`。
    2.  **逻辑**:
        a.  根据 `userType` 构造 Redis key。
        b.  尝试从 Redis 读取 Token。
        c.  如果读取成功，返回 Token。
        d.  如果失败（key 不存在），调用上一步实现的 `FetchNewToken` 方法。
        e.  将获取到的新 Token 和其过期时间存入 Redis。
        f.  返回新 Token。
    3.  编写单元测试（mock Redis 和 `FetchNewToken`）和集成测试来验证缓存逻辑。
*   **结束**: `GetToken` 方法能够智能地处理缓存和刷新。
*   **关注点**: Token 的生命周期管理。

** [x] 任务 1.4: 实现通用的 API 客户端 (`internal/adapter/erp/client.go`)**
*   **开始**: 任务 1.3 完成。
*   **任务**:
    1.  创建 `internal/adapter/erp/client.go`。
    2.  定义 `ERPClient` 结构体，包含 `*http.Client` 和 `*TokenManager`。
    3.  实现一个通用的 `Post(ctx context.Context, path string, userType string, body interface{}) (*http.Response, error)` 方法。
    4.  此方法内部调用 `tokenManager.GetToken` 获取 Token，并将其设置到 `Authorization` 头中。
    5.  编写一个简单的集成测试，尝试调用一个简单的 ERP 端点（如果存在）或 mock 一个。
*   **结束**: `ERPClient` 能够自动处理认证并发送请求。
*   **关注点**: 封装认证和请求发送的通用逻辑。

** [x] 任务 1.5: 实现交易上报 API 调用 (`internal/adapter/erp/transaction.go`)**
*   **开始**: 任务 1.4 完成。
*   **任务**:
    1.  创建 `internal/adapter/erp/transaction.go` 文件。
    2.  在 `ERPClient` 上实现 `PostTransactions(ctx context.Context, req *types.DispenserTransactionRequest) (*types.DispenserTransactionResponse, error)` 方法。
    3.  此方法调用通用的 `Post` 方法，并处理响应的解析（从 JSON 到 Go 结构体）。
    4.  处理特定的业务响应码，例如 `200` (Success) 和 `210` (Duplicate)。
    5.  编写一个集成测试，使用文档中的示例数据调用 UAT 环境的 `/ATG/DispenserTransaction` 接口。
*   **结束**: 能够成功将一笔交易上报给 ERP。
*   **关注点**: 单个业务 API 的完整调用链路。

---

### **阶段 2: 数据库与仓储层扩展**

** [x] 任务 2.1: 更新 Ent Schema 以支持上报状态 (`internal/ent/schema/`)**
*   **开始**: 项目已有 `Transaction` 等实体。
*   **任务**:
    1.  打开 `internal/ent/schema/transaction.go` (以及 `delivery.go`, `inventory.go` 如果存在)。
    2.  添加 `reporting_status`, `last_reporting_attempt_at`, `reporting_retry_count` 字段，如架构文档所示。
    3.  运行 `go generate ./...`。
*   **结束**: 数据库 schema 更新，Ent 代码重新生成。
*   **关注点**: 数据模型对上报状态的支持。

** [x] 任务 2.2: 仓储层上报记录接口 (`internal/repository/repo.go`)**
*   **开始**: 任务 2.1 完成。
*   **任务**:
    1.  打开 `internal/repository/repo.go`。
    2.  在 `ReportRecordRepo` (或等效) 接口中，添加新方法：
        *   `FindPending(ctx context.Context, limit int) ([]*ent.ReportRecord, error)`
        *   `Add(ctx context.Context, data *ent.ReportRecord) error`
*   **结束**: 数据访问层的接口定义更新。
*   **关注点**: 数据访问层的抽象。

** [x] 任务 2.3: 实现仓储层上报记录接口 (`internal/repository/`)**
*   **开始**: 任务 2.2 完成。
*   **任务**:
    1.  打开 `internal/repository/report_record_repo.go`。
    2.  实现 `FindPending` 方法，查询 `reporting_status` 为 `pending` 的记录。
    3.  实现 `Add` 方法，添加待上报数据。
    4.  为这两个新方法编写单元测试（使用测试数据库）。
*   **结束**: 仓储层能够查询待上报数据并更新其状态。
*   **关注点**: 数据库操作的具体实现。

** [x] 任务 2.4: 仓储层订单接口 (`internal/repository/repo.go`)**
*   **开始**: 任务 2.3 完成。
*   **任务**:
    1.  打开 `internal/repository/repo.go`。
    2.  在 `OrderRepo` (或等效) 接口中，添加新方法：
        *   `FindPaid(ctx context.Context) ([]*ent.Order, error)`
*   **结束**: 数据访问层的接口定义更新。
*   **关注点**: 数据访问层的抽象。

** [x] 任务 2.5: 实现仓储层订单接口 (`internal/repository/`)**
*   **开始**: 任务 2.4 完成。
*   **任务**:
    1.  打开 `internal/repository/order_repo.go`。
    2.  实现 `FindPaid` 方法，查询 `status` 为 `completed` 的订单。
*   **结束**: 仓储层能够查询待上报数据。
*   **关注点**: 数据库操作的具体实现。

---

### **阶段 3: 上报任务核心逻辑**

** [x] 任务 3.1: 定义上报 Usecase (`internal/task/reporter/usecase.go`)**
*   **开始**: 任务 1.5, 2.3 完成。
*   **任务**:
    1.  创建 `internal/task/reporter/usecase.go`。
    2.  定义 `ReporterUsecase` 结构体，依赖 `ent.ReportRecordRepo` 和 `ent.OrderRepo`。
    3.  实现 `NewReporterUsecase(...)` 构造函数。
    4.  实现 `ReportTransactions(ctx context.Context)` 方法的骨架，但内部逻辑暂时为空。
*   **结束**: 上报业务逻辑的承载结构体和方法定义完成。
*   **关注点**: 业务逻辑层的结构定义。

** [x] 任务 3.2: 实现上报 Usecase - 核心流程 (`internal/task/reporter/report_order_usecase.go`)**
*   **开始**: 任务 3.1 完成。
*   **任务**:
    1.  在 `ReportTransactions` 方法中实现核心逻辑：
        a.  调用 `repo.FindPending` 获取一批待上报数据。
        b.  如果列表为空，记录一条 "No pending transactions to report." 日志并返回。
        c.  将 `ent.ReportRecord` 列表转换为 `types.DispenserTransactionRequest`。
        d.  调用 `adapter.PostTransactions`。
        e.  **[简化]** 暂时只处理完全成功的情况：调用 `repo.UpdateReportingStatus` 将这批数据的状态更新为 `success`。
    2.  编写单元测试（mock `repo` 和 `adapter`）。
*   **结束**: 能够执行一次完整的“获取数据 -> 上报 -> 更新状态”流程（仅限成功路径）。
*   **关注点**: 核心上报 happy path 的实现。

** [x] 任务 3.3: 增强上报 Usecase - 错误处理 (`internal/task/reporter/usecase.go`)**
*   **开始**: 任务 3.2 完成。
*   **任务**:
    1.  修改 `ReportTransactions` 方法。
    2.  当 `adapter.PostTransactions` 调用失败时（例如网络错误或 API 返回非 2xx 码），调用 `repo.UpdateReportingStatus` 将这批数据的状态更新为 `failed`，并增加重试次数。
    3.  编写单元测试，覆盖 API 调用失败的场景。
*   **结束**: Usecase 能够处理上报失败的情况。
*   **关注点**: 错误处理和重试逻辑。

---

### **阶段 4: 调度与启动**

** [x] 任务 4.1: 实现调度器 (`internal/task/reporter/scheduler.go`)**
*   **开始**: 任务 3.3 完成。
*   **任务**:
    1.  创建 `internal/task/reporter/scheduler.go`。
    2.  使用定时器 + 协程实现核心调度逻辑。
    3.  定义 `Scheduler` 结构体，依赖 `*ReporterUsecase` 和 `*config.ReporterConfig`。
    4.  实现 `Start()` 方法，该方法根据配置中的 cron 表达式 (`transaction_schedule`)，定时调用 `usecase.ReportTransactions()`。
*   **结束**: 调度器能够根据配置定时触发任务。
*   **关注点**: 任务的自动化调度。

** [x] 任务 4.2: 组装并启动服务 (`cmd/hos-reporter/main.go`)**
*   **开始**: 任务 0.3, 2.3, 3.3, 4.1 完成。
*   **任务**:
    1.  修改 `cmd/hos-reporter/main.go`。
    2.  在此文件中，按顺序进行完整的依赖注入（DI）组装：
        a.  加载配置 `cfg`。
        b.  初始化日志 `logger`。
        c.  初始化 Redis 客户端 `redisClient`。
        d.  初始化 PostgreSQL 客户端 `dbClient`。
        e.  初始化 `erpAdapter` (包括 `tokenManager` 和 `erpClient`)。
        f.  初始化 `transactionRepo`。
        g.  初始化 `reporterUsecase`。
        h.  初始化 `scheduler`。
    3.  在 `main` 函数末尾调用 `scheduler.Start()` 并阻塞程序（例如，使用一个 channel）。
*   **结束**: 运行 `go run ./cmd/hos-reporter/main.go`，服务启动，并能看到定时任务按预期触发的日志。
*   **关注点**: 整个服务的组装和启动。

---

### **阶段 5: 扩展与完善**

*   **任务 5.1**: 重复阶段 1-4 的步骤，实现**发货数据 (Delivery)** 的上报。
*   **任务 5.2**: 重复阶段 1-4 的步骤，实现**库存数据 (Inventory)** 的上报。
*   **任务 5.3**: 完善 `ERPClient`，使其能处理 `TokenExpired` (1001) 错误，并自动重试一次。
*   **任务 5.4**: 增加对部分成功/失败的处理逻辑（如果 API 支持）。