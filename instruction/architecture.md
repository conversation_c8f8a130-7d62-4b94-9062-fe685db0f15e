## HOS 系统 - 数据上报架构文档

### 1. 架构总览

本架构设计了一个独立的、可定时执行的后台服务 (`hos-reporter`)，专门负责从 HOS 系统的 PostgreSQL 数据库中提取待上报数据，调用外部 ERP 的 API，并更新上报状态。使用 Redis 缓存来高效、安全地管理有时效性的 API `access_token`。

#### 1.1 逻辑架构图

```mermaid
graph TD
    subgraph "HOS 内部系统"
        PG_DB[(PostgreSQL<br>业务数据源<br>- transaction<br>- delivery_log<br>- inventory_log)]
        Reporter_Service[上报服务<br>HOS-Reporter]
        Redis[(Redis<br>Token 缓存)]
        Scheduler[定时任务触发器<br>e.g.,每10分钟]
    end

    subgraph "外部 ERP 系统"
        ERP_API[ERP API 端点<br>- /ATG/GetToken<br>- /ATG/DispenserTransaction<br>- ...]
    end

    %% 流程
    Scheduler -- 1.触发 --> Reporter_Service
    Reporter_Service -- 2.查询待上报数据 --> PG_DB
    Reporter_Service -- 3.请求 Token --> Redis
    Redis -- 4a.Token 命中 --> Reporter_Service
    Redis -- 4b.Token 未命中 --> Reporter_Service
    Reporter_Service -- 5.(若未命中)调用 API 获取 Token --> ERP_API
    ERP_API -- 6.返回新 Token --> Reporter_Service
    Reporter_Service -- 7.缓存新 Token --> Redis
    Reporter_Service -- 8.携带 Token 上报业务数据 --> ERP_API
    ERP_API -- 9.返回处理结果 --> Reporter_Service
    Reporter_Service -- 10.更新数据上报状态 --> PG_DB

    style Reporter_Service fill:#f9f,stroke:#333,stroke-width:2px
    style PG_DB fill:#9cf,stroke:#333,stroke-width:2px
    style Redis fill:#f96,stroke:#333,stroke-width:2px
    style ERP_API fill:#ccf,stroke:#333,stroke-width:2px
```

#### 1.2 核心组件

1.  **上报服务 (Reporter Service)**:
    *   一个独立的、常驻后台的 Go 应用程序 (`hos-reporter`)。
    *   内部包含一个**定时器 (Scheduler)**，根据配置（例如，交易数据每10分钟，库存数据每30分钟）周期性地触发上报流程。
    *   负责整个上报逻辑的编排：从数据库捞取数据 -> 管理 Token -> 调用 API -> 更新状态。

2.  **PostgreSQL 数据库**:
    *   **业务数据的来源**。所有需要上报的原始数据（如交易、出入库、盘点）都存储在这里。
    *   **上报状态的持久化**。我们会在相关的业务表上增加字段（如 `reporting_status`, `last_attempt_at`, `retry_count`）来跟踪每条记录的上报状态。这是保证数据不重报、不漏报的关键。

3.  **Redis 缓存**:
    *   **API Token 的唯一存储位置**。由于 Token 有效期有限（UAT 1个月，PROD 6个月），使用 Redis 的 TTL (Time-To-Live) 特性来管理其生命周期是完美的解决方案。
    *   **性能优化**。避免每次上报都重新请求 Token，大幅降低认证开销。
    *   **分布式锁（可选）**。如果未来 `hos-reporter` 需要多实例部署以提高可用性，可以使用 Redis 实现分布式锁，确保同一时间只有一个实例在执行上报任务，防止冲突。

4.  **ERP 适配器 (ERP Adapter)**:
    *   在 Go 代码中，这是一个专门的模块，封装了所有与外部 ERP API 的交互细节。
    *   它负责构造请求、处理认证（自动从 Redis 获取或刷新 Token）、解析响应、处理 ERP 定义的各种业务代码（如 `200`, `210`, `1001`）。

### 2. 文件与文件夹结构

我们将创建一个新的主程序目录 `cmd/hos-reporter/`，并扩展 `internal` 目录来支持上报功能。

```bash
hos/
├── cmd/
│   └── hos-reporter/       # [新增] 上报服务主程序
│       └── main.go
├── config/
│   └── config.yaml         # [更新] 增加 reporter 和 redis, erp_api 的配置
├── internal/
│   ├── adapter/            # [新增] 外部系统适配器
│   │   └── erp/
│   │       ├── client.go   # ERP API 客户端，封装 http 请求
│   │       ├── token.go    # [关键] Token 管理器，负责从 Redis 获取/刷新 Token
│   │       ├── transaction.go # 封装交易上报接口的调用
│   │       ├── delivery.go    # 封装发货上报接口的调用
│   │       ├── inventory.go   # 封装库存上报接口的调用
│   │       └── types.go       # 定义 ERP API 的请求和响应结构体
│   ├── ent/
│   │   └── schema/
│   │       └── report_record.go # [更新] 增加上报状态字段
│   ├── repository/
│   │   └── report_record_repo.go # [更新] 增加查询待上报、更新上报状态的方法
│   ├── task/               # 后台任务
│   │   └── reporter/       # [新增] 上报任务核心逻辑
│   │       ├── scheduler.go # 定时器，启动和管理定时任务
│   │       └── usecase.go   # 上报业务逻辑的实现 (获取数据、调用adapter、更新状态)
│   └── ...                 # 其他已有目录
└── infra/                  # 基础设施层
    ├── cache/              # [新增] Redis 客户端封装
    │   └── redis.go
    ├── database/
    └── logger/
```

### 3. 各部分的作用

*   **`cmd/hos-reporter/main.go`**:
    *   **作用**: 上报服务的启动入口。
    *   **职责**: 初始化配置、日志、PostgreSQL 客户端、Redis 客户端，然后组装并启动 `internal/task/reporter` 中的调度器。

*   **`config/config.yaml`**:
    *   **作用**: 存放所有可配置项。
    *   **新增内容**:
        ```yaml
        redis:
          address: "localhost:6379"
          password: ""
          db: 0

        erp_api:
          base_url_uat: "http://*************/apr/api"
          base_url_prod: "http://**************/apr/api"
          # 需要两组用户凭证
          transaction_user:
            key: "user1_key"
            user: "user1"
            password: "password1"
          atg_user: # 用于 Delivery 和 Inventory
            key: "user2_key"
            user: "user2"
            password: "password2"

        reporter:
          transaction_schedule: "@every 10m" # 每10分钟
          inventory_schedule: "@every 30m" # 每30分钟
          batch_size: 100 # 每次上报的数据批次大小
        ```

*   **`internal/adapter/erp/`**:
    *   **作用**: **隔离外部依赖**。所有与 ERP API 相关的丑陋细节都封装在这里，上层业务逻辑无需关心。
    *   **`token.go`**: **核心中的核心**。实现一个 `TokenManager`。
        *   提供 `GetToken(ctx, userType string)` 方法 (e.g., `userType`可以是 "transaction" 或 "atg")。
        *   **逻辑**:
            1.  根据 `userType` 生成 Redis 键 (e.g., `erp_token:transaction`)。
            2.  尝试从 Redis 获取 Token。
            3.  如果获取到，直接返回。
            4.  如果未获取到，调用 ERP 的 `/ATG/GetToken` 接口。
            5.  获取新 Token 后，将其存入 Redis，并根据环境设置合适的过期时间（UAT 1个月，PROD 6个月）。
            6.  返回新 Token。
    *   **`client.go`**: 实现一个 HTTP client，它能自动处理 `TokenExpired` (1001) 错误。当检测到 1001 错误时，它会主动删除 Redis 中的旧 Token，并进行一次重试（重试时 `TokenManager` 会自动获取新 Token）。
    *   **`transaction.go` 等**: 封装具体的业务 API 调用，例如 `PostTransactions` 方法，它接收 HOS 的业务数据，构造 ERP 需要的请求体，然后通过 `client.go` 发送。

*   **`internal/ent/schema/`**:
    *   **作用**: 在业务表（如 `ReportRecord`）中增加字段来跟踪上报状态。
    *   **示例 (report_record.go)**:
        ```go
        func (ReportRecord) Fields() []ent.Field {
            return []ent.Field{
                // ... other fields
                field.Enum("reporting_status").
                    Values("pending", "success", "failed").
                    Default("pending").
                    Comment("上报状态: pending-待处理, success-成功, failed-失败"),
                field.Time("last_reporting_attempt_at").
                    Optional().
                    Comment("上次尝试上报的时间"),
                field.Int("reporting_retry_count").
                    Default(0).
                    Comment("上报重试次数"),
            }
        }
        ```

*   **`internal/repository/`**:
    *   **作用**: 更新数据访问层，增加上报相关方法。
    *   **示例 (transaction_repo.go)**:
        *   `FindPendingTransactions(limit int)`: 查询 `reporting_status = 'pending'` 的交易记录。
        *   `UpdateReportingStatus(ids []int64, status string, newRetryCount int)`: 批量更新记录的上报状态。

*   **`internal/task/reporter/`**:
    *   **作用**: 上报任务的业务逻辑编排。
    *   **`scheduler.go`**: 使用 `gocron/cron` 或类似库，根据 `config.yaml` 中的配置，定时调用 `usecase.go` 中的方法。
    *   **`usecase.go`**: 实现 `ReportTransactions` 等方法。
        1.  调用 `repository` 获取一批待上报数据。
        2.  如果无数据，则结束。
        3.  调用 `erp_adapter` 的 `PostTransactions` 方法进行上报。
        4.  根据返回结果（成功、失败、部分成功），准备需要更新状态的记录ID列表。
        5.  调用 `repository` 的 `UpdateReportingStatus` 方法更新数据库中记录的状态。处理重试次数等逻辑。

*   **`infra/cache/redis.go`**:
    *   **作用**: 提供一个简单的 Redis 客户端封装，向上层提供如 `Get(key)`, `Set(key, value, ttl)` 等方法。

### 4. 状态存储与服务连接

#### 4.1 状态存储位置

*   **业务数据和上报任务状态 (Source of Truth)**:
    *   **位置**: **PostgreSQL**
    *   **内容**: 交易、库存等原始业务数据，以及它们的上报状态 (`reporting_status`, `retry_count` 等)。
    *   **为什么**: 数据库提供事务保证和持久化，是记录任务状态最可靠的地方。即使 `hos-reporter` 服务崩溃重启，也能从上次失败的地方继续。

*   **API 认证会话状态 (Cache)**:
    *   **位置**: **Redis**
    *   **内容**: 从 ERP 获取的 `access_token`。键可能是 `erp_token:transaction_user`，值就是 token 字符串。
    *   **为什么**: Redis 速度快，且内置的 TTL 机制非常适合管理会话/令牌的生命周期。它将易失的、有时效性的认证状态与核心业务数据分离开。

#### 4.2 服务连接方式

*   **Reporter Service -> PostgreSQL**:
    *   服务启动时，通过 `config.yaml` 读取数据库连接信息。
    *   使用 `ent` ORM 创建一个长连接的数据库客户端实例，并在整个应用中共享。

*   **Reporter Service -> Redis**:
    *   服务启动时，通过 `config.yaml` 读取 Redis 的地址和凭证。
    *   创建一个 Redis 客户端实例，并在整个应用中共享。

*   **Reporter Service -> ERP API**:
    *   这是通过 `internal/adapter/erp/client.go` 进行的 HTTP 连接。
    *   `client` 在被调用时，会向 `TokenManager` 请求一个有效的 Token。
    *   `TokenManager` 会先检查 Redis，如果需要则通过 HTTP 请求 ERP 获取新 Token。
    *   拿到 Token 后，`client` 将其放入 `Authorization` 请求头，然后向 ERP 的业务 API 端点（如 `/ATG/DispenserTransaction`）发起 `POST` 请求。连接是短期的、按需建立的。