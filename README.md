# HOS Reporter

HOS Reporter 是一个用于定时上报数据到 ERP 系统的服务。

## 配置说明

### 配置文件格式

```yaml
# 上报任务配置
reporter:
  transaction_interval: 600  # 交易数据上报间隔：600秒（10分钟）
  inventory_interval: 1800   # 库存数据上报间隔：1800秒（30分钟）
  data_sync_interval: 30     # 数据同步间隔：30秒 - 将其他表数据同步到report_record表
  batch_size: 100            # 每次上报的数据批次大小
```

### 配置参数

- `transaction_interval`: 交易数据上报间隔（秒），设置为 0 则禁用该任务
- `inventory_interval`: 库存数据上报间隔（秒），设置为 0 则禁用该任务
- `data_sync_interval`: 数据同步间隔（秒），设置为 0 则禁用该任务 - 用于将其他表数据同步到report_record表
- `batch_size`: 每次上报的数据批次大小

## 使用方法

### 启动服务

```bash
# 使用默认配置文件
go run ./cmd/hos-reporter/main.go

# 使用自定义配置文件
go run ./cmd/hos-reporter/main.go custom_config.yaml
```

### 停止服务

使用 `Ctrl+C` 或发送 `SIGTERM` 信号来优雅停止服务。

## API 接口

### 调度器方法

```go
// 创建调度器
scheduler := reporter.NewScheduler(usecase, config)

// 启动调度器
err := scheduler.Start()

// 停止调度器
scheduler.Stop()

// 检查运行状态
isRunning := scheduler.IsRunning()
```

### 生成 ent 代码

```bash
$ go run -mod=mod entgo.io/ent/cmd/ent generate --target ./internal/ent/order_service ./internal/ent/order_service/schema
$ go run -mod=mod entgo.io/ent/cmd/ent generate --target ./internal/ent/hos_reporter ./internal/ent/hos_reporter/schema
```
