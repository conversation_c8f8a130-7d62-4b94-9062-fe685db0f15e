hos_reporter
.DS_Store
// 执行文件
# 忽略所有可执行文件
*.exe
*.out
*.bin
*.dll
*.so
*.a
*.o
*.test
*.run
*.app
*.pyc
*.class
*.jar
*.war
*.deb
*.rpm
*.msi
*.apk
*.ipa
*.dmg
*.log
# 忽略本地开发和测试相关目录
.idea/
.vscode/
*.swp
*.swo
*.tmp
*.bak

# Go模块缓存和依赖
vendor/
go.sum
go.work
go.work.sum

# 覆盖率和测试输出
coverage.*
*.coverprofile

# 生成的文档和临时文件
*.md.html
*.pdf

# Mac系统文件
__MACOSX/

# 日志和调试输出
*.log.*

# 其他常见忽略
node_modules/
dist/
build/
.env
.env.*
# 个人配置
*.local
*.secret
