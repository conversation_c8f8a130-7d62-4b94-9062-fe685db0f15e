# 模块职责总结

## 单一数据来源策略下的模块分工

### 📋 模块职责一览表

| 模块 | 字段数量 | 主要职责 | 关键字段 |
|------|----------|----------|----------|
| **初始化模块** | 5个 | 基础标识和客户信息 | TransactionID, SlipNumber, VehicleID, CustomerName, CustomerPhoneNo |
| **燃油交易模块** | 7个 | 核心交易数据 | ProductID, Amount, Price, Volume, TransactionDate, TotalizerStart/End, RFIDVehicleID |
| **元数据ERP模块** | 7个 | 设备和操作员信息 | DispenserNumber, NozzleNumber, VehicleType, SiteID, DeviceID, OperatorID, FieldTambahan1 |
| **元数据基础模块** | 5个 | 客户扩展信息 | Email, Gender, DOB, DEXROWID, DEX_ROW_TS |
| **映射计算模块** | 2个 | 业务规则映射 | IDVehicleTypeGroup, IDProductGroup |
| **计算模块** | 3个 | 时间和折扣计算 | TransactionLength, FinalDiscount, PercentDiscount |
| **数据库联查模块** | 4个 | 关联数据查询 | FieldTambahan2, PromotionType, AmountDiscount, FlagItemPromotion |
| **固定值** | 9个 | 系统常量 | MessageID, Category, AreaSite, Reprint, AmountPercentDiscount, Voucher, Point, FieldTambahan3, MessageHeader |

### 🎯 关键业务规则

#### 车辆类型映射
```
1 → Dinas (公务车)
2 → Truck (卡车)  
3 → Car (轿车)
4 → Motor (摩托车)
```

#### 产品分组映射
```
BP 92, BPULTIMATE → Gasoline
BPULTIMATEDIESEL → Diesel
其他包含DIESEL → Diesel
默认 → Gasoline
```

### 📊 数据来源统计

- **订单直接字段**: 5个 (15%)
- **燃油交易字段**: 7个 (21%) 
- **元数据字段**: 12个 (36%)
- **计算字段**: 5个 (15%)
- **联查字段**: 4个 (12%)
- **固定值**: 9个 (27%)

### ✅ 改造成果

- **消除重复赋值**: 15个字段的多来源问题已解决
- **单一职责**: 每个模块职责明确，互不干扰
- **数据流清晰**: 从数据源到最终字段的路径一目了然
- **易于维护**: 字段问题可快速定位到具体模块
